import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';

export type Theme = 'light' | 'dark' | 'system';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'app-theme';
  private currentThemeSubject = new BehaviorSubject<Theme>('system');
  private effectiveThemeSubject = new BehaviorSubject<'light' | 'dark'>('light');

  public currentTheme$ = this.currentThemeSubject.asObservable();
  public effectiveTheme$ = this.effectiveThemeSubject.asObservable();

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    if (isPlatformBrowser(this.platformId)) {
      this.initializeTheme();
      this.listenToSystemThemeChanges();
    }
  }

  private initializeTheme(): void {
    const savedTheme = this.getSavedTheme();
    this.setTheme(savedTheme);
  }

  public toggleTheme(): void {
    const effectiveTheme = this.effectiveThemeSubject.value;
    this.setTheme(effectiveTheme === 'light' ? 'dark' : 'light');
  }

  public setTheme(theme: Theme): void {
    this.currentThemeSubject.next(theme);
    this.saveTheme(theme);
    this.applyTheme(theme);
  }

  private applyTheme(theme: Theme): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const effectiveTheme = this.getEffectiveTheme(theme);
    this.effectiveThemeSubject.next(effectiveTheme);

    document.documentElement.setAttribute('data-theme', effectiveTheme);
  }

  private getEffectiveTheme(theme: Theme): 'light' | 'dark' {
    if (theme === 'system') {
      return this.getSystemTheme();
    }
    return theme;
  }

  private getSystemTheme(): 'light' | 'dark' {
    if (!isPlatformBrowser(this.platformId)) return 'light';
    
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  private getSavedTheme(): Theme {
    try {
      const saved = localStorage.getItem(this.THEME_KEY) as Theme;
      return saved && ['light', 'dark', 'system'].includes(saved) ? saved : 'system';
    } catch {
      return 'system';
    }
  }

  private saveTheme(theme: Theme): void {
    try {
      localStorage.setItem(this.THEME_KEY, theme);
    } catch {
      // Handle localStorage errors silently
    }
  }

  public listenToSystemThemeChanges(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      if (this.currentThemeSubject.value === 'system') {
        this.applyTheme('system');
      }
    };

    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange);
    }
  }

  public isDarkMode(): boolean {
    return this.effectiveThemeSubject.value === 'dark';
  }

  public isLightMode(): boolean {
    return this.effectiveThemeSubject.value === 'light';
  }
}