import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {RouterModule} from '@angular/router';
import {LoadingSpinnerComponent} from '../../components/ui/loading-spinner/loading-spinner.component';
import {StatsComponent} from '../../components/stats/stats.component';
import {ButtonComponent} from '../../components/ui/button/button.component';
import {ActionComponent} from '../../components/ui/action/action.component';
import {QuickActionsComponent} from '../../components/quick-actions/quick-actions.component';
import {RecentActivityComponent} from '../../components/recent-activity/recent-activity.component';
import {ExploreComponentComponent} from '../../components/explore-component/explore-component.component';
import {PageHeaderComponent} from '../../components/page-header/page-header.component';


@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule, RouterModule, LoadingSpinnerComponent, StatsComponent, ButtonComponent, QuickActionsComponent,
    RecentActivityComponent, ExploreComponentComponent, PageHeaderComponent
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent implements OnInit {
  loading = true;

  ngOnInit(): void {
    this.loadDashboardData();
  }

  private async loadDashboardData(): Promise<void> {
    try {
      await this.delay(1500);

      this.loading = false;
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      this.loading = false;
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  onRefreshData(): void {
    this.loading = true;
    this.loadDashboardData();
  }
}
