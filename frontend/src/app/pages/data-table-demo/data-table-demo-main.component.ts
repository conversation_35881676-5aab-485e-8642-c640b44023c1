import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTableDemoComponent } from './data-table-demo.component';
import { AdvancedDemoComponent } from './advanced-demo.component';
import { StatesDemoComponent } from './states-demo.component';

@Component({
  selector: 'app-data-table-demo-main',
  standalone: true,
  imports: [
    CommonModule,
    DataTableDemoComponent,
    AdvancedDemoComponent,
    StatesDemoComponent
  ],
  template: `
    <div class="demo-main-container">
      <!-- Header -->
      <header class="demo-header">
        <h1>Data Table Component Library</h1>
        <p class="demo-description">
          A comprehensive, accessible, and highly customizable data table library for Angular applications.
          This demo showcases all the features and capabilities of the data table components.
        </p>
        
        <!-- Navigation -->
        <nav class="demo-nav">
          <button 
            *ngFor="let tab of tabs; trackBy: trackByTab"
            class="nav-btn"
            [class.active]="activeTab === tab.id"
            (click)="setActiveTab(tab.id)">
            {{ tab.label }}
          </button>
        </nav>
      </header>

      <!-- Demo Content -->
      <main class="demo-content">
        <!-- Basic Demo -->
        <div *ngIf="activeTab === 'basic'" class="demo-tab">
          <app-data-table-demo></app-data-table-demo>
        </div>

        <!-- Advanced Demo -->
        <div *ngIf="activeTab === 'advanced'" class="demo-tab">
          <app-advanced-demo></app-advanced-demo>
        </div>

        <!-- States Demo -->
        <div *ngIf="activeTab === 'states'" class="demo-tab">
          <app-states-demo></app-states-demo>
        </div>

        <!-- Features Overview -->
        <div *ngIf="activeTab === 'features'" class="demo-tab">
          <div class="features-container">
            <h1>Features Overview</h1>
            
            <div class="features-grid">
              <div class="feature-card">
                <h3>🔧 TypeScript Generics</h3>
                <p>Full type safety with TypeScript generics support for your data models.</p>
              </div>

              <div class="feature-card">
                <h3>📱 Responsive Design</h3>
                <p>Mobile-first design that adapts to all screen sizes with touch-friendly interactions.</p>
              </div>

              <div class="feature-card">
                <h3>♿ Accessibility</h3>
                <p>WCAG compliant with proper ARIA labels, keyboard navigation, and screen reader support.</p>
              </div>

              <div class="feature-card">
                <h3>🎨 Themeable</h3>
                <p>Easy customization with CSS variables and multiple built-in themes.</p>
              </div>

              <div class="feature-card">
                <h3>🔍 Advanced Filtering</h3>
                <p>Column-specific filters with multiple operators and debounced input.</p>
              </div>

              <div class="feature-card">
                <h3>📊 Sorting</h3>
                <p>Multi-column sorting with visual indicators and custom sort functions.</p>
              </div>

              <div class="feature-card">
                <h3>📄 Pagination</h3>
                <p>Server-side and client-side pagination with configurable page sizes.</p>
              </div>

              <div class="feature-card">
                <h3>✅ Selection</h3>
                <p>Single and multi-row selection with indeterminate states.</p>
              </div>

              <div class="feature-card">
                <h3>🎭 Actions</h3>
                <p>Conditional row actions with permissions and confirmation dialogs.</p>
              </div>

              <div class="feature-card">
                <h3>💀 Loading States</h3>
                <p>Beautiful skeleton UI with animations for better user experience.</p>
              </div>

              <div class="feature-card">
                <h3>🚫 Empty States</h3>
                <p>Customizable empty state messaging with action buttons.</p>
              </div>

              <div class="feature-card">
                <h3>🎯 Performance</h3>
                <p>OnPush change detection, track by functions, and optimized rendering.</p>
              </div>
            </div>

            <div class="usage-section">
              <h2>Quick Start</h2>
              <pre class="code-block"><code>import {{ '{' }} DataTableComponent {{ '}' }} from './components/ui/data-table';

&lt;app-data-table
  [data]="users"
  [columns]="columns"
  [config]="config"
  (sortChange)="onSort($event)"
  (selectionChange)="onSelectionChange($event)"&gt;
&lt;/app-data-table&gt;</code></pre>
            </div>
          </div>
        </div>
      </main>

      <!-- Footer -->
      <footer class="demo-footer">
        <p>Built with Angular {{ angularVersion }} • TypeScript • CSS Variables</p>
        <p>Supports all modern browsers and assistive technologies</p>
      </footer>
    </div>
  `,
  styles: [`
    .demo-main-container {
      min-height: 100vh;
      background: var(--bg-tertiary, #f8fafc);
    }

    .demo-header {
      background: var(--bg-primary, #ffffff);
      padding: var(--space-6, 24px);
      border-bottom: 1px solid var(--border-primary, #e5e7eb);
      text-align: center;
    }

    .demo-header h1 {
      margin: 0 0 var(--space-3, 12px) 0;
      color: var(--text-primary, #111827);
      font-size: var(--text-3xl, 30px);
      font-weight: var(--font-bold, 700);
    }

    .demo-description {
      max-width: 600px;
      margin: 0 auto var(--space-6, 24px) auto;
      color: var(--text-secondary, #6b7280);
      font-size: var(--text-lg, 18px);
      line-height: var(--leading-relaxed, 1.625);
    }

    .demo-nav {
      display: flex;
      justify-content: center;
      gap: var(--space-2, 8px);
      flex-wrap: wrap;
    }

    .nav-btn {
      padding: var(--space-3, 12px) var(--space-4, 16px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-md, 8px);
      background: var(--bg-secondary, #f9fafb);
      color: var(--text-secondary, #6b7280);
      font-size: var(--text-sm, 14px);
      font-weight: var(--font-medium, 500);
      cursor: pointer;
      transition: all var(--transition-fast, 150ms);
    }

    .nav-btn:hover {
      background: var(--bg-tertiary, #f3f4f6);
      color: var(--text-primary, #111827);
    }

    .nav-btn.active {
      background: var(--interactive-primary, #3b82f6);
      color: white;
      border-color: var(--interactive-primary, #3b82f6);
    }

    .demo-content {
      min-height: calc(100vh - 200px);
    }

    .demo-tab {
      animation: fadeIn 0.3s ease-in-out;
    }

    .features-container {
      padding: var(--space-6, 24px);
      max-width: 1200px;
      margin: 0 auto;
    }

    .features-container h1 {
      text-align: center;
      margin-bottom: var(--space-8, 32px);
      color: var(--text-primary, #111827);
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--space-6, 24px);
      margin-bottom: var(--space-8, 32px);
    }

    .feature-card {
      background: var(--bg-primary, #ffffff);
      padding: var(--space-6, 24px);
      border-radius: var(--radius-lg, 12px);
      border: 1px solid var(--border-primary, #e5e7eb);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: transform var(--transition-fast, 150ms);
    }

    .feature-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .feature-card h3 {
      margin: 0 0 var(--space-3, 12px) 0;
      color: var(--text-primary, #111827);
      font-size: var(--text-lg, 18px);
    }

    .feature-card p {
      margin: 0;
      color: var(--text-secondary, #6b7280);
      line-height: var(--leading-relaxed, 1.625);
    }

    .usage-section {
      background: var(--bg-primary, #ffffff);
      padding: var(--space-6, 24px);
      border-radius: var(--radius-lg, 12px);
      border: 1px solid var(--border-primary, #e5e7eb);
    }

    .usage-section h2 {
      margin: 0 0 var(--space-4, 16px) 0;
      color: var(--text-primary, #111827);
    }

    .code-block {
      background: var(--bg-secondary, #f9fafb);
      padding: var(--space-4, 16px);
      border-radius: var(--radius-md, 8px);
      border: 1px solid var(--border-primary, #e5e7eb);
      overflow-x: auto;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: var(--text-sm, 14px);
      line-height: var(--leading-relaxed, 1.625);
    }

    .demo-footer {
      background: var(--bg-primary, #ffffff);
      padding: var(--space-4, 16px);
      border-top: 1px solid var(--border-primary, #e5e7eb);
      text-align: center;
      color: var(--text-tertiary, #9ca3af);
      font-size: var(--text-sm, 14px);
    }

    .demo-footer p {
      margin: var(--space-1, 4px) 0;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Responsive */
    @media (max-width: 768px) {
      .demo-header {
        padding: var(--space-4, 16px);
      }

      .demo-header h1 {
        font-size: var(--text-2xl, 24px);
      }

      .demo-description {
        font-size: var(--text-base, 16px);
      }

      .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4, 16px);
      }

      .feature-card {
        padding: var(--space-4, 16px);
      }

      .nav-btn {
        padding: var(--space-2, 8px) var(--space-3, 12px);
        font-size: var(--text-xs, 12px);
      }
    }
  `]
})
export class DataTableDemoMainComponent {
  activeTab = 'features';
  angularVersion = '19.2';

  tabs = [
    { id: 'features', label: 'Features' },
    { id: 'basic', label: 'Basic Demo' },
    { id: 'advanced', label: 'Advanced Demo' },
    { id: 'states', label: 'States Demo' }
  ];

  setActiveTab(tabId: string) {
    this.activeTab = tabId;
  }

  trackByTab(index: number, tab: any) {
    return tab.id;
  }
}
