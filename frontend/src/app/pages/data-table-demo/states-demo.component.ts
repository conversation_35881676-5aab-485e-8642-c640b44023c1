import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DataTableComponent } from '../../components/ui/data-table/data-table.component';
import { DataTableColumn, DataTableConfig } from '../../models';

interface StateUser {
  id: number;
  name: string;
  email: string;
  status: string;
}

@Component({
  selector: 'app-states-demo',
  standalone: true,
  imports: [CommonModule, FormsModule, DataTableComponent],
  template: `
    <div class="demo-container">
      <h1>Data Table States Demo</h1>

      <!-- Controls -->
      <div class="controls-section">
        <h2>State Controls</h2>
        <div class="controls-grid">
          <label>
            <input type="checkbox" [(ngModel)]="showLoading" (change)="updateStates()">
            Loading State
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="showEmpty" (change)="updateStates()">
            Empty State
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="showSkeleton" (change)="updateStates()">
            Show Skeleton
          </label>
          <button (click)="loadData()" class="load-btn">Load Sample Data</button>
          <button (click)="clearData()" class="clear-btn">Clear Data</button>
        </div>
      </div>

      <!-- Loading State Table -->
      <section class="demo-section">
        <h2>Loading State</h2>
        <app-data-table
          [data]="loadingData"
          [columns]="columns"
          [config]="loadingConfig"
          [loading]="showLoading"
          [showSkeleton]="showSkeleton">
        </app-data-table>
      </section>

      <!-- Empty State Table -->
      <section class="demo-section">
        <h2>Empty State</h2>
        <app-data-table
          [data]="emptyData"
          [columns]="columns"
          [config]="emptyConfig"
          [showAddButton]="true"
          [addButtonLabel]="'Add First User'"
          (addItemClick)="onAddItem()">
        </app-data-table>
        <p *ngIf="addItemClicked" class="add-info">
          Add item button was clicked!
        </p>
      </section>

      <!-- Different Table Variants -->
      <section class="demo-section">
        <h2>Table Variants</h2>
        
        <h3>Striped Table</h3>
        <app-data-table
          [data]="sampleData"
          [columns]="columns"
          [config]="stripedConfig">
        </app-data-table>

        <h3>Bordered Table</h3>
        <app-data-table
          [data]="sampleData"
          [columns]="columns"
          [config]="borderedConfig">
        </app-data-table>

        <h3>Dense Table</h3>
        <app-data-table
          [data]="sampleData"
          [columns]="columns"
          [config]="denseConfig">
        </app-data-table>
      </section>
    </div>
  `,
  styles: [`
    .demo-container {
      padding: var(--space-6, 24px);
      max-width: 1200px;
      margin: 0 auto;
    }

    .controls-section {
      margin-bottom: var(--space-6, 24px);
      padding: var(--space-4, 16px);
      background: var(--bg-secondary, #f9fafb);
      border-radius: var(--radius-md, 8px);
    }

    .controls-section h2 {
      margin: 0 0 var(--space-3, 12px) 0;
      color: var(--text-primary, #111827);
    }

    .controls-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: var(--space-3, 12px);
      align-items: center;
    }

    .controls-grid label {
      display: flex;
      align-items: center;
      gap: var(--space-2, 8px);
      font-size: var(--text-sm, 14px);
    }

    .load-btn,
    .clear-btn {
      padding: var(--space-2, 8px) var(--space-3, 12px);
      border-radius: var(--radius-sm, 4px);
      border: 1px solid;
      cursor: pointer;
      font-size: var(--text-sm, 14px);
      transition: all var(--transition-fast, 150ms);
    }

    .load-btn {
      background: var(--interactive-primary, #3b82f6);
      color: white;
      border-color: var(--interactive-primary, #3b82f6);
    }

    .clear-btn {
      background: var(--bg-secondary, #f9fafb);
      color: var(--text-secondary, #6b7280);
      border-color: var(--border-primary, #e5e7eb);
    }

    .demo-section {
      margin-bottom: var(--space-8, 32px);
      padding: var(--space-4, 16px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-md, 8px);
      background: var(--bg-primary, #ffffff);
    }

    .demo-section h2 {
      margin: 0 0 var(--space-4, 16px) 0;
      color: var(--text-primary, #111827);
      font-size: var(--text-xl, 20px);
    }

    .demo-section h3 {
      margin: var(--space-6, 24px) 0 var(--space-3, 12px) 0;
      color: var(--text-primary, #111827);
      font-size: var(--text-lg, 18px);
    }

    .demo-section h3:first-of-type {
      margin-top: 0;
    }

    .add-info {
      margin-top: var(--space-3, 12px);
      padding: var(--space-2, 8px) var(--space-3, 12px);
      background: var(--status-success-bg, #d1fae5);
      color: var(--status-success, #10b981);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--text-sm, 14px);
    }

    h1 {
      color: var(--text-primary, #111827);
      margin-bottom: var(--space-6, 24px);
    }
  `]
})
export class StatesDemoComponent {
  showLoading = false;
  showEmpty = false;
  showSkeleton = true;
  addItemClicked = false;

  loadingData: StateUser[] = [];
  emptyData: StateUser[] = [];
  sampleData: StateUser[] = [];

  columns: DataTableColumn<StateUser>[] = [
    { key: 'id', label: 'ID', type: 'number', width: '80px' },
    { key: 'name', label: 'Name', type: 'text' },
    { key: 'email', label: 'Email', type: 'text' },
    { key: 'status', label: 'Status', type: 'text' }
  ];

  loadingConfig: DataTableConfig = {
    loading: false, // Will be controlled by showLoading
    striped: true,
    hover: true
  };

  emptyConfig: DataTableConfig = {
    emptyMessage: 'No users found',
    striped: true,
    hover: true
  };

  stripedConfig: DataTableConfig = {
    striped: true,
    hover: true
  };

  borderedConfig: DataTableConfig = {
    bordered: true,
    hover: true
  };

  denseConfig: DataTableConfig = {
    dense: true,
    striped: true,
    hover: true
  };

  constructor() {
    this.loadSampleData();
  }

  private loadSampleData() {
    this.sampleData = [
      { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'Active' },
      { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'Inactive' },
      { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'Active' }
    ];
  }

  updateStates() {
    this.loadingConfig = {
      ...this.loadingConfig,
      loading: this.showLoading
    };

    if (this.showEmpty) {
      this.emptyData = [];
    } else {
      this.emptyData = [...this.sampleData];
    }

    if (!this.showLoading) {
      this.loadingData = [...this.sampleData];
    } else {
      this.loadingData = [];
    }
  }

  loadData() {
    this.showLoading = true;
    this.showEmpty = false;
    this.updateStates();

    // Simulate loading delay
    setTimeout(() => {
      this.showLoading = false;
      this.updateStates();
    }, 2000);
  }

  clearData() {
    this.showLoading = false;
    this.showEmpty = true;
    this.updateStates();
  }

  onAddItem() {
    this.addItemClicked = true;
    console.log('Add item clicked');
    
    // Reset the message after 3 seconds
    setTimeout(() => {
      this.addItemClicked = false;
    }, 3000);
  }
}
