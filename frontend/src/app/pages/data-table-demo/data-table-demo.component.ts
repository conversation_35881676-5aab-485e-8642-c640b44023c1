import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTableComponent } from '../../components/ui/data-table/data-table.component';
import { DataTableColumn, DataTableConfig } from '../../models';

interface DemoUser {
  id: number;
  name: string;
  email: string;
  age: number;
  active: boolean;
}

@Component({
  selector: 'app-data-table-demo',
  standalone: true,
  imports: [CommonModule, DataTableComponent],
  template: `
    <div class="demo-container">
      <h1>Data Table Demo</h1>
      <p>This page demonstrates the data table components with sample data.</p>

      <!-- Basic Table -->
      <section class="demo-section">
        <h2>Basic Table</h2>
        <app-data-table
          [data]="users"
          [columns]="basicColumns"
          [config]="basicConfig">
        </app-data-table>
      </section>

      <!-- Table with Selection -->
      <section class="demo-section">
        <h2>Table with Selection</h2>
        <app-data-table
          [data]="users"
          [columns]="basicColumns"
          [config]="selectableConfig"
          (selectionChange)="onSelectionChange($event)">
        </app-data-table>
        <p *ngIf="selectedCount > 0" class="selection-info">
          Selected: {{ selectedCount }} users
        </p>
      </section>

      <!-- Table with Sorting -->
      <section class="demo-section">
        <h2>Table with Sorting</h2>
        <app-data-table
          [data]="users"
          [columns]="sortableColumns"
          [config]="sortableConfig"
          (sortChange)="onSortChange($event)">
        </app-data-table>
        <p *ngIf="currentSort" class="sort-info">
          Sorted by: {{ currentSort.column }} ({{ currentSort.direction }})
        </p>
      </section>
    </div>
  `,
  styles: [`
    .demo-container {
      padding: var(--space-6, 24px);
      max-width: 1200px;
      margin: 0 auto;
    }

    .demo-section {
      margin-bottom: var(--space-8, 32px);
      padding: var(--space-4, 16px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-md, 8px);
      background: var(--bg-primary, #ffffff);
    }

    .demo-section h2 {
      margin: 0 0 var(--space-4, 16px) 0;
      color: var(--text-primary, #111827);
      font-size: var(--text-xl, 20px);
    }

    .selection-info,
    .sort-info {
      margin-top: var(--space-3, 12px);
      padding: var(--space-2, 8px) var(--space-3, 12px);
      background: var(--bg-secondary, #f9fafb);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--text-sm, 14px);
      color: var(--text-secondary, #6b7280);
    }

    h1 {
      color: var(--text-primary, #111827);
      margin-bottom: var(--space-4, 16px);
    }

    p {
      color: var(--text-secondary, #6b7280);
      margin-bottom: var(--space-6, 24px);
    }
  `]
})
export class DataTableDemoComponent implements OnInit {
  users: DemoUser[] = [];
  selectedCount = 0;
  currentSort: any = null;

  // Basic columns
  basicColumns: DataTableColumn<DemoUser>[] = [
    { key: 'id', label: 'ID', type: 'number', width: '80px' },
    { key: 'name', label: 'Name', type: 'text' },
    { key: 'email', label: 'Email', type: 'text' },
    { key: 'age', label: 'Age', type: 'number', align: 'right' },
    { key: 'active', label: 'Active', type: 'boolean' }
  ];

  // Sortable columns
  sortableColumns: DataTableColumn<DemoUser>[] = [
    { key: 'id', label: 'ID', type: 'number', width: '80px', sortable: true },
    { key: 'name', label: 'Name', type: 'text', sortable: true },
    { key: 'email', label: 'Email', type: 'text', sortable: true },
    { key: 'age', label: 'Age', type: 'number', align: 'right', sortable: true },
    { key: 'active', label: 'Active', type: 'boolean', sortable: true }
  ];

  // Basic config
  basicConfig: DataTableConfig = {
    striped: true,
    hover: true
  };

  // Selectable config
  selectableConfig: DataTableConfig = {
    selectable: true,
    multiSelect: true,
    striped: true,
    hover: true
  };

  // Sortable config
  sortableConfig: DataTableConfig = {
    sortable: true,
    striped: true,
    hover: true
  };

  ngOnInit() {
    this.loadSampleData();
  }

  private loadSampleData() {
    this.users = [
      { id: 1, name: 'John Doe', email: '<EMAIL>', age: 30, active: true },
      { id: 2, name: 'Jane Smith', email: '<EMAIL>', age: 25, active: false },
      { id: 3, name: 'Bob Johnson', email: '<EMAIL>', age: 35, active: true },
      { id: 4, name: 'Alice Brown', email: '<EMAIL>', age: 28, active: true },
      { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', age: 42, active: false }
    ];
  }

  onSelectionChange(selection: any) {
    this.selectedCount = selection.selected.length;
    console.log('Selection changed:', selection);
  }

  onSortChange(sort: any) {
    this.currentSort = sort;
    console.log('Sort changed:', sort);
  }
}
