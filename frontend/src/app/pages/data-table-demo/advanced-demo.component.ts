import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTableComponent } from '../../components/ui/data-table/data-table.component';
import { DataTableColumn, DataTableConfig, DataTableAction } from '../../models';

interface AdvancedUser {
  id: number;
  name: string;
  email: string;
  age: number;
  department: string;
  active: boolean;
  joinDate: Date;
}

@Component({
  selector: 'app-advanced-demo',
  standalone: true,
  imports: [CommonModule, DataTableComponent],
  template: `
    <div class="demo-container">
      <h1>Advanced Data Table Features</h1>

      <!-- Table with Filtering -->
      <section class="demo-section">
        <h2>Table with Filtering</h2>
        <app-data-table
          [data]="users"
          [columns]="filterableColumns"
          [config]="filterableConfig"
          (filtersChange)="onFiltersChange($event)">
        </app-data-table>
        <div *ngIf="activeFilters.length > 0" class="filter-info">
          <p>Active filters: {{ activeFilters.length }}</p>
          <ul>
            <li *ngFor="let filter of activeFilters">
              {{ filter.column }}: {{ filter.value }}
            </li>
          </ul>
        </div>
      </section>

      <!-- Table with Pagination -->
      <section class="demo-section">
        <h2>Table with Pagination</h2>
        <app-data-table
          [data]="largeDataset"
          [columns]="basicColumns"
          [config]="paginatedConfig"
          (pageChange)="onPageChange($event)"
          (pageSizeChange)="onPageSizeChange($event)">
        </app-data-table>
        <p class="pagination-info">
          Current page: {{ currentPage }} | Page size: {{ currentPageSize }}
        </p>
      </section>

      <!-- Table with Actions -->
      <section class="demo-section">
        <h2>Table with Actions</h2>
        <app-data-table
          [data]="users"
          [columns]="basicColumns"
          [config]="actionsConfig"
          [actions]="userActions"
          (actionClick)="onActionClick($event)">
        </app-data-table>
        <p *ngIf="lastAction" class="action-info">
          Last action: {{ lastAction.action.label }} on {{ lastAction.item.name }}
        </p>
      </section>
    </div>
  `,
  styles: [`
    .demo-container {
      padding: var(--space-6, 24px);
      max-width: 1200px;
      margin: 0 auto;
    }

    .demo-section {
      margin-bottom: var(--space-8, 32px);
      padding: var(--space-4, 16px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-md, 8px);
      background: var(--bg-primary, #ffffff);
    }

    .demo-section h2 {
      margin: 0 0 var(--space-4, 16px) 0;
      color: var(--text-primary, #111827);
      font-size: var(--text-xl, 20px);
    }

    .filter-info,
    .pagination-info,
    .action-info {
      margin-top: var(--space-3, 12px);
      padding: var(--space-2, 8px) var(--space-3, 12px);
      background: var(--bg-secondary, #f9fafb);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--text-sm, 14px);
      color: var(--text-secondary, #6b7280);
    }

    .filter-info ul {
      margin: var(--space-2, 8px) 0 0 0;
      padding-left: var(--space-4, 16px);
    }

    .filter-info li {
      margin-bottom: var(--space-1, 4px);
    }

    h1 {
      color: var(--text-primary, #111827);
      margin-bottom: var(--space-6, 24px);
    }
  `]
})
export class AdvancedDemoComponent implements OnInit {
  users: AdvancedUser[] = [];
  largeDataset: AdvancedUser[] = [];
  activeFilters: any[] = [];
  currentPage = 1;
  currentPageSize = 10;
  lastAction: any = null;

  // Basic columns
  basicColumns: DataTableColumn<AdvancedUser>[] = [
    { key: 'id', label: 'ID', type: 'number', width: '80px' },
    { key: 'name', label: 'Name', type: 'text' },
    { key: 'email', label: 'Email', type: 'text' },
    { key: 'department', label: 'Department', type: 'text' },
    { key: 'active', label: 'Active', type: 'boolean' }
  ];

  // Filterable columns
  filterableColumns: DataTableColumn<AdvancedUser>[] = [
    { key: 'id', label: 'ID', type: 'number', width: '80px', sortable: true },
    { key: 'name', label: 'Name', type: 'text', sortable: true, filterable: true },
    { key: 'email', label: 'Email', type: 'text', sortable: true, filterable: true },
    { key: 'age', label: 'Age', type: 'number', sortable: true, filterable: true, align: 'right' },
    { key: 'department', label: 'Department', type: 'text', sortable: true, filterable: true },
    { key: 'active', label: 'Active', type: 'boolean', sortable: true, filterable: true }
  ];

  // Configurations
  filterableConfig: DataTableConfig = {
    filterable: true,
    sortable: true,
    striped: true,
    hover: true
  };

  paginatedConfig: DataTableConfig = {
    paginated: true,
    pageSize: 10,
    pageSizeOptions: [5, 10, 15, 20],
    striped: true,
    hover: true
  };

  actionsConfig: DataTableConfig = {
    actionColumn: true,
    striped: true,
    hover: true
  };

  // Actions
  userActions: DataTableAction<AdvancedUser>[] = [
    {
      label: 'View',
      icon: '👁️',
      action: (user) => this.viewUser(user),
      visible: () => true,
      disabled: () => false
    },
    {
      label: 'Edit',
      icon: '✏️',
      action: (user) => this.editUser(user),
      visible: () => true,
      disabled: () => false
    },
    {
      label: 'Delete',
      icon: '🗑️',
      action: (user) => this.deleteUser(user),
      visible: (user) => user.active,
      disabled: () => false,
      confirmMessage: 'Are you sure you want to delete this user?'
    }
  ];

  ngOnInit() {
    this.loadSampleData();
    this.generateLargeDataset();
  }

  private loadSampleData() {
    this.users = [
      { 
        id: 1, 
        name: 'John Doe', 
        email: '<EMAIL>', 
        age: 30, 
        department: 'Engineering',
        active: true,
        joinDate: new Date('2022-01-15')
      },
      { 
        id: 2, 
        name: 'Jane Smith', 
        email: '<EMAIL>', 
        age: 25, 
        department: 'Marketing',
        active: false,
        joinDate: new Date('2022-03-20')
      },
      { 
        id: 3, 
        name: 'Bob Johnson', 
        email: '<EMAIL>', 
        age: 35, 
        department: 'Sales',
        active: true,
        joinDate: new Date('2021-11-10')
      },
      { 
        id: 4, 
        name: 'Alice Brown', 
        email: '<EMAIL>', 
        age: 28, 
        department: 'Engineering',
        active: true,
        joinDate: new Date('2023-02-05')
      },
      { 
        id: 5, 
        name: 'Charlie Wilson', 
        email: '<EMAIL>', 
        age: 42, 
        department: 'HR',
        active: false,
        joinDate: new Date('2020-08-12')
      }
    ];
  }

  private generateLargeDataset() {
    const departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance'];
    const names = ['John', 'Jane', 'Bob', 'Alice', 'Charlie', 'Diana', 'Eve', 'Frank'];
    const surnames = ['Doe', 'Smith', 'Johnson', 'Brown', 'Wilson', 'Davis', 'Miller', 'Garcia'];

    this.largeDataset = Array.from({ length: 50 }, (_, i) => {
      const name = names[i % names.length];
      const surname = surnames[Math.floor(i / names.length) % surnames.length];
      return {
        id: i + 1,
        name: `${name} ${surname} ${i + 1}`,
        email: `${name.toLowerCase()}${i + 1}@example.com`,
        age: 22 + Math.floor(Math.random() * 40),
        department: departments[Math.floor(Math.random() * departments.length)],
        active: Math.random() > 0.3,
        joinDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28))
      };
    });
  }

  onFiltersChange(filters: any[]) {
    this.activeFilters = filters;
    console.log('Filters changed:', filters);
  }

  onPageChange(page: number) {
    this.currentPage = page;
    console.log('Page changed:', page);
  }

  onPageSizeChange(pageSize: number) {
    this.currentPageSize = pageSize;
    console.log('Page size changed:', pageSize);
  }

  onActionClick(event: any) {
    this.lastAction = event;
    console.log('Action clicked:', event);
  }

  viewUser(user: AdvancedUser) {
    alert(`Viewing user: ${user.name}`);
  }

  editUser(user: AdvancedUser) {
    alert(`Editing user: ${user.name}`);
  }

  deleteUser(user: AdvancedUser) {
    alert(`Deleting user: ${user.name}`);
  }
}
