<app-page-header
  title="Products"
  subtitle="Manage your product catalog and inventory.">
  <ng-template #actions>
    <app-button
      (onClick)="onRefresh()"
      [disabled]="loading"
      variant="secondary"
      size="md"
      icon="🔄"
      text="Refresh"
    >
    </app-button>
    <app-button
      href="/products/create"
      text="Add Product"
      icon="➕"
      variant="primary"
      size="md"
    >
    </app-button>
  </ng-template>
</app-page-header>

<!-- Filters and Search -->
<div class="filters-section">
  <div class="filters-content">
    <div class="search-group">
      <app-search-bar
        placeholder="Search products..."
        [showClearButton]="true"
        [loading]="loading"
        (search)="onSearchTermChange($event)"
        (clear)="onClearSearch()"
      ></app-search-bar>
    </div>

    <!-- Filters -->
    <div class="filter-group">
      <app-select [defaultItem]="{value: '', text: 'All Categories'}"
                  [(ngModel)]="selectedCategory"
                  (onSelect)="onCategoryChange()">
        <app-select-item *ngFor="let category of categories"
                         [text]="category"
                         [value]="category">
        </app-select-item>
      </app-select>

      <app-select
        [(ngModel)]="selectedStatus"
        [defaultItem]="{value: '', text: 'All Status'}"
        (onSelect)="onStatusChange()">
        <app-select-item *ngFor="let status of statuses"
                         [text]="status.label"
                         [value]="status.value">
        </app-select-item>
      </app-select>

      <app-button
        (onClick)="onClearFilters()"
        text="Clear"
        [disabled]="true"
        variant="error"
        size="xs"
        title="Clear all filters"
      >
      </app-button>
    </div>
  </div>

  <!-- Results Summary -->
  <div class="results-summary">
    <span class="results-count">
      Showing {{ paginatedProducts.length }} of {{ totalItems }} products
    </span>
    <div class="sort-controls">
      <label class="sort-label">Sort by:</label>
      <app-button
        text="Name"
        [icon]="sortDirection === 'asc' ? '↑' : '↓' "
        (onClick)="onSort('name')"
        [class.active]="sortBy === 'name'"
        hover="none"
        variant="info"
        size="xs"
      >
      </app-button>
    </div>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="loading-section">
  <div class="loading-grid">
    <div *ngFor="let i of [1,2,3,4,5,6,7,8]" class="product-card-skeleton">
      <div class="skeleton skeleton-image"></div>
      <div class="skeleton-content">
        <div class="skeleton skeleton-text"></div>
        <div class="skeleton skeleton-text"></div>
        <div class="skeleton skeleton-text" style="width: 60%;"></div>
      </div>
    </div>
  </div>
</div>

<!-- Products Grid -->
<div *ngIf="!loading" class="products-section">
  <div *ngIf="paginatedProducts.length === 0" class="empty-state">
    <div class="empty-icon">📦</div>
    <h3 class="empty-title">No products found</h3>
    <p class="empty-description">
      {{
        searchTerm || selectedCategory || selectedStatus ?
          'Try adjusting your filters or search terms.' :
          'Get started by adding your first product.'
      }}
    </p>
    <a *ngIf="!searchTerm && !selectedCategory && !selectedStatus"
       routerLink="/products/create"
       class="btn btn-primary">
      <span class="btn-icon">➕</span>
      <span>Add First Product</span>
    </a>
  </div>

  <div *ngIf="paginatedProducts.length > 0" class="products-grid">
    <div
      *ngFor="let product of paginatedProducts; trackBy: trackByProductId"
      class="product-card"
    >
      <!-- Product Image -->
      <div class="product-image">
        <img
          [src]="product.image"
          [alt]="product.name"
          loading="lazy"
          onerror="this.src='https://via.placeholder.com/300x300?text=No+Image'"
        >
        <div class="product-status">
          <span class="badge">
            status
          </span>
        </div>
      </div>

      <!-- Product Content -->
      <div class="product-content">
        <div class="product-header">
          <h3 class="product-name">{{ product.name }}</h3>
          <div class="product-category">{{ product.categoryName }}</div>
        </div>

        <p class="product-description">{{ product.description }}</p>

        <div class="product-details">
          <div class="product-price">Price</div>
          <div class="product-stock">
            <span class="stock-icon">📦</span>
            <span>stock in stock</span>
          </div>
        </div>

        <div class="product-actions">
          <a
            [routerLink]="['/products', product.id, 'view']"
            class="btn btn-sm btn-secondary"
            title="View product"
          >
            <span>👁️</span>
          </a>
          <a
            [routerLink]="['/products', product.id, 'edit']"
            class="btn btn-sm btn-primary"
            title="Edit product"
          >
            <span>✏️</span>
          </a>
          <button
            class="btn btn-sm btn-error"
            title="Delete product"
            (click)="onDeleteProduct(product)"
          >
            <span>🗑️</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="totalPages > 1" class="pagination-section">
    <div class="pagination">
      <button
        class="pagination-btn"
        [disabled]="currentPage === 1"
        (click)="onPageChange(currentPage - 1)"
        title="Previous page"
      >
        ← Previous
      </button>

      <div class="pagination-pages">
        <button
          *ngFor="let page of [].constructor(totalPages); let i = index"
          class="pagination-page"
          [class.active]="currentPage === i + 1"
          (click)="onPageChange(i + 1)"
        >
          {{ i + 1 }}
        </button>
      </div>

      <button
        class="pagination-btn"
        [disabled]="currentPage === totalPages"
        (click)="onPageChange(currentPage + 1)"
        title="Next page"
      >
        Next →
      </button>
    </div>

    <div class="pagination-info">
      Page {{ currentPage }} of {{ totalPages }}
    </div>
  </div>
</div>
