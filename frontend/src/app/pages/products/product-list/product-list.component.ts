import { Component, OnInit } from '@angular/core';
import {CommonModule, <PERSON>ForOf, NgIf} from '@angular/common';
import {RouterLink, RouterModule} from '@angular/router';
import { FormsModule } from '@angular/forms';
import {ViewProductDto} from '../../../models';
import {ButtonComponent} from '../../../components/ui/button/button.component';
import {PageHeaderComponent} from '../../../components/page-header/page-header.component';
import {SelectComponent} from '../../../components/ui/select/select.component';
import {SelectItemComponent} from '../../../components/ui/select-item/select-item.component';
import {SearchBarComponent} from '../../../components/ui/search-bar/search-bar.component';



@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule, FormsModule, NgForOf, NgIf, RouterLink,
    ButtonComponent, PageHeaderComponent, SelectItemComponent, SelectComponent, SearchBarComponent],
  templateUrl: './product-list.component.html',
  styleUrl: './product-list.component.css'
})
export class ProductListComponent implements OnInit {
  products: ViewProductDto[] = [];
  filteredProducts: ViewProductDto[] = [];
  loading = true;
  searchTerm = '';
  selectedCategory = '';
  selectedStatus = '';
  sortBy = 'name';
  sortDirection: 'asc' | 'desc' = 'asc';

  categories = ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports', 'Toys'];
  statuses = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'out-of-stock', label: 'Out of Stock' }
  ];

  currentPage = 1;
  itemsPerPage = 12;
  totalItems = 0;

  ngOnInit(): void {
    this.loadProducts();
  }

  private async loadProducts(): Promise<void> {
    try {
      await this.delay(1000);

      this.products = this.generateMockProducts();
      this.totalItems = this.products.length;
      this.applyFilters();
      this.loading = false;
    } catch (error) {
      console.error('Error loading products:', error);
      this.loading = false;
    }
  }

  private generateMockProducts(): ViewProductDto[] {
    const mockProducts: ViewProductDto[] = [];
    const productNames = [
      'Wireless Bluetooth Headphones',
      'Smart Watch Series 5',
      'Gaming Mechanical Keyboard',
      'Portable Power Bank',
      'Wireless Mouse',
      'USB-C Hub',
      'Laptop Stand',
      'Phone Case',
      'Tablet Screen Protector',
      'Bluetooth Speaker',
      'Fitness Tracker',
      'Wireless Charger'
    ];

    for (let i = 0; i < 50; i++) {
      const name = productNames[i % productNames.length];
      const categoryName = this.categories[Math.floor(Math.random() * this.categories.length)];

      mockProducts.push({
        id: i + 1,
        name: `${name} ${i + 1}`,
        description: `High-quality ${name.toLowerCase()} with premium features and excellent performance.`,
        categoryName,
        image: `https://picsum.photos/300/300?random=${i + 1}`,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        tags: []
      });
    }

    return mockProducts;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  onSearch(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onCategoryChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onStatusChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onSort(field: string): void {
    if (this.sortBy === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortDirection = 'asc';
    }
    this.applyFilters();
  }

  private applyFilters(): void {
    let filtered = [...this.products];

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(term) ||
        product.description.toLowerCase().includes(term) ||
        product.categoryName.toLowerCase().includes(term)
      );
    }

    if (this.selectedCategory) {
      filtered = filtered.filter(product => product.categoryName === this.selectedCategory);
    }

    filtered.sort((a, b) => {
      let aValue: any = a[this.sortBy as keyof ViewProductDto];
      let bValue: any = b[this.sortBy as keyof ViewProductDto];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return this.sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return this.sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    this.filteredProducts = filtered;
    this.totalItems = filtered.length;
  }

  get paginatedProducts(): ViewProductDto[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredProducts.slice(startIndex, endIndex);
  }

  get totalPages(): number {
    return Math.ceil(this.totalItems / this.itemsPerPage);
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  getStatusBadgeClass(status: string): string {
    const classes: { [key: string]: string } = {
      'active': 'badge-success',
      'inactive': 'badge-secondary',
      'out-of-stock': 'badge-error'
    };
    return classes[status] || 'badge-secondary';
  }

  getStockStatusClass(stock: number): string {
    if (stock === 0) return 'text-error';
    if (stock < 10) return 'text-warning';
    return 'text-success';
  }

  onRefresh(): void {
    this.loading = true;
    this.loadProducts();
  }

  onClearFilters(): void {
    this.searchTerm = '';
    this.selectedCategory = '';
    this.selectedStatus = '';
    this.currentPage = 1;
    this.applyFilters();
  }

  onDeleteProduct(product: ViewProductDto): void {
    if (confirm(`Are you sure you want to delete "${product.name}"?`)) {
      console.log('Deleting product:', product.id);
    }
  }

  trackByProductId(index: number, product: ViewProductDto): number {
    return product.id;
  }

  onSearchTermChange(term: string): void {
    this.searchTerm = term;
    this.onSearch();
  }

  onClearSearch(): void {
    this.searchTerm = '';
    this.onSearch();
  }
}
