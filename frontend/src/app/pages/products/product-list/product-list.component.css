/* Page Header */
.page-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.page-title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--text-secondary);
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-shrink: 0;
}

/* Filters Section */
.filters-section {
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-8);
  box-shadow: var(--shadow-sm);
}

.filters-content {
  display: flex;
  gap: var(--space-6);
  align-items: flex-end;
  margin-bottom: var(--space-4);
}

.search-group {
  flex: 1;
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--text-base);
}

.search-input {
  padding-left: var(--space-10);
}

.filter-group {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.form-select {
  min-width: 150px;
}

/* Results Summary */
.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

.results-count {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.sort-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  background: none;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.sort-btn:hover {
  background-color: var(--bg-surface-hover);
  color: var(--text-primary);
}

.sort-btn.active {
  background-color: var(--primary-100);
  border-color: var(--primary-300);
  color: var(--primary-700);
}

.sort-arrow {
  font-size: var(--text-xs);
}

/* Loading Section */
.loading-section {
  margin: var(--space-8) 0;
}

.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6);
}

.product-card-skeleton {
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  overflow: hidden;
}

.skeleton-image {
  width: 100%;
  height: 200px;
  margin-bottom: var(--space-4);
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

/* Products Section */
.products-section {
  margin: var(--space-8) 0;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-16) var(--space-8);
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
}

.empty-icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.empty-title {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.empty-description {
  margin: 0 0 var(--space-6) 0;
  font-size: var(--text-base);
  color: var(--text-secondary);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-6);
}

.product-card {
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
  position: relative;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* Product Image */
.product-image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background-color: var(--bg-secondary);
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-base);
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-status {
  position: absolute;
  top: var(--space-3);
  right: var(--space-3);
}

/* Product Content */
.product-content {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.product-header {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.product-name {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  line-height: var(--leading-tight);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-category {
  font-size: var(--text-xs);
  color: var(--text-muted);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-description {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) 0;
  border-top: 1px solid var(--border-primary);
  border-bottom: 1px solid var(--border-primary);
}

.product-price {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.product-stock {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.stock-icon {
  font-size: var(--text-xs);
}

.text-success { color: var(--color-success-600); }
.text-warning { color: var(--color-warning-600); }
.text-error { color: var(--color-error-600); }

.product-actions {
  display: flex;
  gap: var(--space-2);
  justify-content: flex-end;
}

/* Pagination */
.pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-8);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-primary);
}

.pagination {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.pagination-btn {
  padding: var(--space-2) var(--space-4);
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--bg-surface-hover);
  box-shadow: var(--shadow-sm);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: var(--space-1);
}

.pagination-page {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.pagination-page:hover {
  background-color: var(--bg-surface-hover);
}

.pagination-page.active {
  background-color: var(--primary-600);
  border-color: var(--primary-600);
  color: white;
}

.pagination-info {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

/* Responsive Design */
@media (max-width: 1023px) {
  .filters-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }

  .filter-group {
    flex-wrap: wrap;
  }

  .results-summary {
    flex-direction: column;
    gap: var(--space-3);
    align-items: flex-start;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-4);
  }

  .page-header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }

  .page-actions {
    justify-content: flex-start;
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: var(--text-3xl);
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .pagination-section {
    flex-direction: column;
    gap: var(--space-4);
  }

  .pagination-pages {
    flex-wrap: wrap;
    justify-content: center;
  }

  .page-actions {
    flex-direction: column;
    align-items: stretch;
  }
}
