import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostL<PERSON>ener, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NavigationService, NavigationItem } from '../../../services/navigation.service';
import { Subscription } from 'rxjs';
import {ICONS} from '../../../../assets';
import {ActionComponent} from '../../ui/action/action.component';

@Component({
  selector: 'app-navigation',
  standalone: true,
  imports: [CommonModule, RouterModule, ActionComponent],
  templateUrl: './navigation.component.html',
  styleUrl: './navigation.component.css'
})
export class NavigationComponent implements OnInit, OnDestroy {
  navigationItems: NavigationItem[] = [];
  isCollapsed = false;
  isMiniMode = false;
  isOriginallyMiniMode = false;
  private subscription = new Subscription();

  constructor(
    private navigationService: NavigationService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    if (isPlatformBrowser(this.platformId)) {
      this.checkScreenSize();
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    if (isPlatformBrowser(this.platformId)) {
      this.checkScreenSize();
    }
  }

  private checkScreenSize() {
    if (isPlatformBrowser(this.platformId)) {
      const width = window.innerWidth;
      this.isMiniMode = width >= 620 && width < 1024;
      this.isOriginallyMiniMode = width >= 620 && width < 1024;
    }
  }

  toggleMiniMode() {
    this.isMiniMode =! this.isMiniMode;
  }

  ngOnInit(): void {
    // Subscribe to navigation items
    this.subscription.add(
      this.navigationService.getNavigationItems().subscribe(items => {
        this.navigationItems = items;
      })
    );

    // Subscribe to sidebar state
    this.subscription.add(
      this.navigationService.getSidebarState().subscribe(state => {
        this.isCollapsed = state.isCollapsed;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onItemClick(item: NavigationItem): void {
    if (this.isMiniMode && item.children && item.children.length > 0) {
      // In mini mode, clicking a parent item with children should navigate to first child
      if (item.children[0].route) {
        this.navigationService.navigateTo(item.children[0]);
      }
    } else if (item.children && item.children.length > 0) {
      this.navigationService.toggleExpand(item);
    } else if (item.route) {
      this.navigationService.navigateTo(item);
    }
  }

  onChildClick(child: NavigationItem): void {
    if (child.route) {
      this.navigationService.navigateTo(child);
    }
  }

  trackByFn(index: number, item: NavigationItem): string {
    return item.label;
  }

  protected readonly ICONS = ICONS;
}
