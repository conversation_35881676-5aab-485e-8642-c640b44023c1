<!-- Navigation Menu -->
<nav class="nav-menu" [class.mini-mode]="isMiniMode">
  <div class="nav-section" [class.hidden]="!isOriginallyMiniMode || isCollapsed">
    <button
      class="nav-link toggle-mini-mode"
      (click)="toggleMiniMode()">
      <span class="nav-icon">{{isMiniMode ? '➡️' : '⬅️'}}</span>
    </button>
  </div>
  <div class="break-line"></div>
  <!-- Quick Actions -->
  <div class="nav-section" [class.hidden]="isCollapsed || isMiniMode">
    <div class="nav-section-title">Quick Actions</div>
    <div class="quick-actions">
      <app-action href="/products/create" icon="➕" text="Add Product" variant="info"></app-action>
      <app-action href="/sales/create" icon="💰" text="New Sale" variant="info"></app-action>
    </div>
  </div>

  <!-- Main Navigation -->
  <div class="nav-section">
    <div class="nav-section-title" [class.hidden]="isCollapsed || isMiniMode">Navigation</div>
    <ul class="nav-list">
      <li
        class="nav-item"
        *ngFor="let item of navigationItems; trackBy: trackByFn"
      >
        <!-- Parent Item -->
        <button
          class="nav-link"
          [class.active]="item.isActive"
          [class.expanded]="item.isExpanded"
          [class.has-children]="item.children && item.children.length > 0"
          [class.mini-mode]="isMiniMode"
          (click)="onItemClick(item)"
          [title]="item.label"
        >
          <span class="nav-icon">{{ item.icon }}</span>
          <span class="nav-label" [class.hidden]="isCollapsed || isMiniMode">{{ item.label }}</span>
          <span
            class="nav-badge"
            *ngIf="item.badge && !isCollapsed && !isMiniMode"
          >{{ item.badge }}</span>
          <span
            class="nav-arrow"
            *ngIf="item.children && item.children.length > 0 && !isCollapsed && !isMiniMode"
            [class.rotated]="item.isExpanded"
          >▶</span>
        </button>

        <!-- Children Items - Only show in full mode -->
        <ul
          class="nav-children"
          *ngIf="item.children && item.children.length > 0 && !isCollapsed && !isMiniMode"
          [class.expanded]="item.isExpanded"
        >
          <li
            class="nav-child-item"
            *ngFor="let child of item.children; trackBy: trackByFn"
          >
            <button
              class="nav-child-link"
              [class.active]="child.isActive"
              (click)="onChildClick(child)"
              [title]="child.label"
            >
              <span class="nav-child-icon">{{ child.icon }}</span>
              <span class="nav-child-label">{{ child.label }}</span>
              <span
                class="nav-badge"
                *ngIf="child.badge"
              >{{ child.badge }}</span>
            </button>
          </li>
        </ul>
      </li>
    </ul>
  </div>
  <div class="break-line"></div>
  <!-- User Section -->
  <div class="nav-section">
    <div class="nav-section-title">Account</div>
    <ul class="nav-list">
      <li class="nav-item">
        <button class="nav-link" [class.mini-mode]="isMiniMode" title="Profile">
          <span class="nav-icon">👤</span>
          <span class="nav-label" [class.hidden]="isCollapsed || isMiniMode">Profile</span>
        </button>
      </li>
      <li class="nav-item">
        <button class="nav-link" [class.mini-mode]="isMiniMode" title="Help & Support">
          <span class="nav-icon">❓</span>
          <span class="nav-label" [class.hidden]="isCollapsed || isMiniMode">Help</span>
        </button>
      </li>
    </ul>
  </div>
  <div class="break-line"></div>
  <div class="user-card" [class.collapsed]="isCollapsed" [class.mini-mode]="isMiniMode">
    <div class="user-avatar">
      <span>👤</span>
    </div>
    <div class="user-details" [class.hidden]="isCollapsed || isMiniMode">
      <div class="user-name">Admin User</div>
      <div class="user-status">Online</div>
    </div>
    <button class="user-menu-btn" [class.hidden]="isCollapsed || isMiniMode" title="User menu">
      <span>⋯</span>
    </button>
  </div>
</nav>
