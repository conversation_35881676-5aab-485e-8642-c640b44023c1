.nav-menu {
  flex: 1;
  padding: var(--space-4);
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.nav-section {
  margin-bottom: var(--space-6);
}

.nav-section-title {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-3);
  padding: 0 var(--space-2);
}

.quick-actions {
  display: flex;
  flex-direction: row;
  gap: var(--space-2);
}

/* Navigation List */
.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: var(--space-1);
}

.nav-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3) var(--space-2);
  background: none;
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
}

.nav-link:hover {
  background-color: var(--bg-surface-hover);
  color: var(--text-primary);
}

.nav-link.active {
  background-color: var(--primary-100);
  color: var(--primary-700);
  box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .nav-link.active {
  background-color: var(--primary-900);
  color: var(--primary-300);
}

.nav-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
  width: 20px;
  text-align: center;
}

.nav-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-badge {
  background-color: var(--error-500);
  color: white;
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.nav-arrow {
  font-size: var(--text-xs);
  color: var(--text-muted);
  transition: transform var(--transition-fast);
  flex-shrink: 0;
}

.nav-arrow.rotated {
  transform: rotate(90deg);
}

/* Children Navigation */
.nav-children {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-base);
}

.nav-children.expanded {
  max-height: 500px;
}

.nav-child-item {
  margin-bottom: var(--space-1);
}

.nav-child-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-2) var(--space-2) var(--space-2) var(--space-8);
  background: none;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-muted);
  text-decoration: none;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
}

.nav-child-link:hover {
  background-color: var(--bg-surface-hover);
  color: var(--text-secondary);
}

.nav-child-link.active {
  background-color: var(--primary-50);
  color: var(--primary-600);
}

[data-theme="dark"] .nav-child-link.active {
  background-color: var(--primary-950);
  color: var(--primary-400);
}

.nav-child-icon {
  font-size: var(--text-xs);
  flex-shrink: 0;
  width: 16px;
  text-align: center;
}

.nav-child-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

.user-card {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-xl);
  transition: all var(--transition-fast);
}

.user-card:hover {
  background-color: var(--bg-surface-hover);
}

.user-card.collapsed {
  justify-content: center;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--secondary-400), var(--primary-400));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  color: white;
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--space-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  font-size: var(--text-xs);
  color: var(--success-600);
  font-weight: var(--font-medium);
  line-height: 1;
}

.user-menu-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.user-menu-btn:hover {
  background-color: var(--bg-surface-hover);
  color: var(--text-primary);
}

/* Mini mode styles - controlled by class instead of media query */
.nav-menu.mini-mode  {
  width: fit-content;
  gap: var(--space-1);
  align-items: center;
  padding: 2px var(--space-4);
}

.mini-mode .nav-section {
  margin-bottom: 0;
}

.mini-mode .nav-section-title {
  display: none;
}

.mini-mode .break-line {
  height: 2px;
  width: 100%;
  background-color: var(--border-primary);
  border-radius: var(--radius-full);
  user-select: none;
}

.mini-mode .nav-icon {
  width: fit-content;
  height: fit-content;
  font-size: var(--text-3xl) !important;
  user-select: none;
}

.toggle-mini-mode .nav-icon{
  align-self: self-end;
  width: fit-content;
  height: fit-content;
  font-size: var(--text-3xl) !important;
  user-select: none;
}

.mini-mode .quick-action-btn {
  justify-content: center;
  padding: var(--space-2);
}

.mini-mode .quick-action-icon {
  margin-right: 0;
}

@media (min-width: 1024px) {
  .nav-menu {
    width: auto;
  }
}
