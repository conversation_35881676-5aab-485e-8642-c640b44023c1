.user-menu-wrapper {
  position: relative;
}

.user-menu-trigger {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-3);
  background-color: transparent;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-primary);
}

.user-menu-trigger:hover {
  background-color: var(--bg-surface-hover);
  box-shadow: var(--shadow-md);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-400), var(--secondary-400));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  color: white;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  line-height: 1;
}

.user-role {
  font-size: var(--text-xs);
  color: var(--text-muted);
  line-height: 1;
}

.dropdown-arrow {
  font-size: var(--text-xs);
  color: var(--text-muted);
  transition: transform var(--transition-fast);
}

.user-menu-trigger:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--space-2);
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-2);
  min-width: 200px;
  z-index: var(--z-dropdown);
}

.user-dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
}

.user-dropdown-item:hover {
  background-color: var(--bg-surface-hover);
  text-decoration: none;
}

.user-dropdown-divider {
  margin: var(--space-2) 0;
  border: none;
  border-top: 1px solid var(--border-primary);
}

@media (max-width: 767px) {
  .user-info {
    display: none;
  }

  .user-menu-trigger {
    padding: var(--space-2);
  }
}
