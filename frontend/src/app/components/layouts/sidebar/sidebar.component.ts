import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NavigationComponent } from '../../navigation/navigation/navigation.component';
import { NavigationService, NavigationItem } from '../../../services/navigation.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule, NavigationComponent],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.css'
})
export class SidebarComponent implements OnInit, OnDestroy {
  navigationItems: NavigationItem[] = [];
  isOpen = false;
  isCollapsed = false;
  private subscription: Subscription = new Subscription();

  constructor(private navigationService: NavigationService) {}

  ngOnInit(): void {
    // Subscribe to navigation items
    this.subscription.add(
      this.navigationService.getNavigationItems().subscribe(items => {
        this.navigationItems = items;
      })
    );

    // Subscribe to sidebar state
    this.subscription.add(
      this.navigationService.getSidebarState().subscribe(state => {
        this.isOpen = state.isOpen;
        this.isCollapsed = state.isCollapsed;
      })
    );

    this.navigationService.updateActiveStates();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  onOverlayClick(): void {
    this.navigationService.closeSidebar();
  }
}
