.sidebar {
  max-width: 280px;
  height: 100dvh;
  overflow-y: scroll;
  background-color: var(--bg-surface);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-base);
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-modal);
  transform: translateX(-100%);
}

.sidebar-overlay{
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
}

.sidebar-overlay.open{
  opacity: 1;
  visibility: visible;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-collapsed {
  width: 64px;
}

/* Desktop Sidebar */
@media (min-width: 1024px) {
  .sidebar {
    transform: translateX(0);
    z-index: auto;
  }
}


@media (min-width: 620px) {
  .sidebar {
    position: sticky;
    transform: translateX(0);
    z-index: auto;
  }
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  min-height: 64px;
}

.sidebar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.brand-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-base);
  color: white;
  flex-shrink: 0;
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-name {
  font-size: var(--text-base);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1;
}

.brand-version {
  font-size: var(--text-xs);
  color: var(--text-muted);
  font-weight: var(--font-medium);
}

.sidebar-close {
  background-color: transparent;
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
}

.sidebar-close:hover {
  background-color: var(--bg-surface-hover);
}


/* Responsive Design */
@media (max-width: 1023px) {
  .sidebar {
    box-shadow: var(--shadow-2xl);
  }
}

