import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ButtonComponent } from '../../ui/button/button.component';
import { LogoComponent } from '../../logo/logo.component';
import { SearchBarComponent } from '../../ui/search-bar/search-bar.component';
import { UserWidgetComponent } from '../../user-widget/user-widget.component';
import { ICONS } from '../../../../assets';
import { NavigationService } from '../../../services/navigation.service';
import { ThemeService } from '../../../services/theme.service';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-header',
  imports: [
    ButtonComponent,
    LogoComponent,
    SearchBarComponent,
    UserWidgetComponent
  ],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent implements OnInit, OnD<PERSON>roy {
  protected readonly ICONS = ICONS;
  private destroy$ = new Subject<void>();
  currentThemeIcon = ICONS.LIGHT;

  constructor(
    private navigationService: NavigationService,
    private themeService: ThemeService
  ) {}

  ngOnInit(): void {
    this.themeService.effectiveTheme$
      .pipe(takeUntil(this.destroy$))
      .subscribe(theme => {
        this.currentThemeIcon = theme === 'dark' ? ICONS.LIGHT : ICONS.DARK;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleNavigation() {
    this.navigationService.toggleSidebar();
  }

  toggleTheme() {
    this.themeService.toggleTheme();
  }
}
