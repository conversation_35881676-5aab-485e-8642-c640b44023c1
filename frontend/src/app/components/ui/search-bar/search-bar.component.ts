import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { FormControl, ReactiveFormsModule } from "@angular/forms";
import { NgIf } from '@angular/common';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';


@Component({
  selector: 'app-search-bar',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NgIf
  ],
  templateUrl: './search-bar.component.html',
  styleUrl: './search-bar.component.css'
})
export class SearchBarComponent implements OnInit, OnDestroy {

  @Input() placeholder: string = 'Search...';
  @Input() debounceTime: number = 0;
  @Input() minLength: number = 0;
  @Input() showClearButton?: boolean = true;

  @Input() loading = false;

  @Output() search = new EventEmitter<string>();
  @Output() clear = new EventEmitter<void>();

  searchControl = new FormControl('');

  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.searchControl.valueChanges
      .pipe(
        debounceTime(this.debounceTime),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        const searchTerm = value?.trim() || '';
        if (searchTerm.length >= this.minLength) {
          this.search.emit(searchTerm);
        } else if (searchTerm.length === 0) {
          this.search.emit('');
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onClear(): void {
    this.searchControl.setValue('');
    this.clear.emit();
  }

  get hasValue(): boolean {
    return !!(this.searchControl.value?.trim());
  }
}
