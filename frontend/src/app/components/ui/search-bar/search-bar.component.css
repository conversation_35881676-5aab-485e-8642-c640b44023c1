.search-bar-container {
  position: relative;
  width: 100%;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--bg-surface, #ffffff);
  border: 1px solid var(--border-primary, #e0e0e0);
  border-radius: 8px;
  padding: 0 12px;
  transition: all 0.2s ease;
}

.search-input-wrapper:focus-within {
  border-color: var(--primary-color, #007bff);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.search-icon {
  display: flex;
  align-items: center;
  color: var(--text-secondary, #666);
  margin-right: 8px;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 0;
  font-size: 14px;
  background: transparent;
  color: var(--text-primary, #333);
}

.search-input::placeholder {
  color: var(--text-secondary, #999);
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.loading-indicator {
  display: flex;
  align-items: center;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-primary, #e0e0e0);
  border-top: 2px solid var(--primary-color, #007bff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-secondary, #666);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 18px;
}

.clear-button:hover {
  background: var(--bg-hover, #f5f5f5);
  color: var(--text-primary, #333);
}
