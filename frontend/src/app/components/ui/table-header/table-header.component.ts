import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataTableColumn, DataTableSort } from '../../../models';

@Component({
  selector: 'app-table-header',
  standalone: true,
  imports: [CommonModule],
  template: `
    <thead class="table-header" role="rowgroup">
      <tr class="header-row" role="row">
        <!-- Selection Column -->
        <th 
          *ngIf="selectable"
          class="header-cell selection-cell"
          role="columnheader"
          scope="col">
          <div class="header-content">
            <input
              type="checkbox"
              class="selection-checkbox"
              [checked]="isAllSelected"
              [indeterminate]="isIndeterminate"
              (change)="onSelectAll($event)"
              [attr.aria-label]="getSelectAllLabel()"
              role="checkbox"
              [attr.aria-checked]="getSelectAllAriaChecked()">
          </div>
        </th>

        <!-- Data Columns -->
        <th
          *ngFor="let column of visibleColumns; trackBy: trackByColumn"
          class="header-cell"
          [class]="getHeaderCellClass(column)"
          [style.width]="column.width"
          [style.min-width]="column.minWidth"
          [style.max-width]="column.maxWidth"
          [style.text-align]="column.align"
          role="columnheader"
          scope="col"
          [attr.aria-sort]="getAriaSortValue(column)">
          
          <div class="header-content" [class.sortable]="column.sortable">
            <!-- Custom Header Template -->
            <ng-container *ngIf="column.headerTemplate; else defaultHeader">
              <ng-container *ngTemplateOutlet="column.headerTemplate; context: { column: column }"></ng-container>
            </ng-container>

            <!-- Default Header -->
            <ng-template #defaultHeader>
              <button
                *ngIf="column.sortable; else staticHeader"
                type="button"
                class="sort-button"
                (click)="onSort(column)"
                [attr.aria-label]="getSortLabel(column)"
                [attr.title]="getSortLabel(column)">
                
                <span class="header-label">{{ column.label }}</span>
                <span class="sort-indicator" [class]="getSortIndicatorClass(column)">
                  {{ getSortIcon(column) }}
                </span>
              </button>

              <ng-template #staticHeader>
                <span class="header-label">{{ column.label }}</span>
              </ng-template>
            </ng-template>
          </div>
        </th>

        <!-- Actions Column -->
        <th 
          *ngIf="hasActions"
          class="header-cell actions-cell"
          [style.width]="actionsColumnWidth"
          [style.text-align]="actionsColumnAlign"
          role="columnheader"
          scope="col">
          <div class="header-content">
            <span class="header-label">{{ actionsColumnLabel }}</span>
          </div>
        </th>
      </tr>
    </thead>
  `,
  styles: [`
    .table-header {
      background: var(--bg-secondary, #f9fafb);
      border-bottom: 2px solid var(--border-primary, #e5e7eb);
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .header-row {
      border: none;
    }

    .header-cell {
      padding: var(--space-3, 12px) var(--space-4, 16px);
      font-weight: var(--font-semibold, 600);
      font-size: var(--text-sm, 14px);
      color: var(--text-secondary, #6b7280);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      border-right: 1px solid var(--border-secondary, #f3f4f6);
      vertical-align: middle;
      white-space: nowrap;
      position: relative;
    }

    .header-cell:last-child {
      border-right: none;
    }

    .header-cell.sticky {
      position: sticky;
      background: var(--bg-secondary, #f9fafb);
      z-index: 11;
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .header-content.sortable {
      cursor: pointer;
    }

    .sort-button {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      background: none;
      border: none;
      padding: 0;
      font: inherit;
      color: inherit;
      cursor: pointer;
      text-align: inherit;
      transition: color var(--transition-fast, 150ms);
    }

    .sort-button:hover {
      color: var(--text-primary, #111827);
    }

    .sort-button:focus-visible {
      outline: 2px solid var(--border-focus, #3b82f6);
      outline-offset: 2px;
      border-radius: var(--radius-sm, 4px);
    }

    .header-label {
      flex: 1;
      text-align: inherit;
    }

    .sort-indicator {
      margin-left: var(--space-2, 8px);
      font-size: var(--text-xs, 12px);
      opacity: 0.5;
      transition: opacity var(--transition-fast, 150ms);
    }

    .sort-indicator.active {
      opacity: 1;
      color: var(--interactive-primary, #3b82f6);
    }

    .sort-indicator.asc::after {
      content: '↑';
    }

    .sort-indicator.desc::after {
      content: '↓';
    }

    .sort-indicator.none::after {
      content: '↕';
    }

    .selection-cell {
      width: 48px;
      text-align: center;
    }

    .selection-checkbox {
      width: 16px;
      height: 16px;
      cursor: pointer;
      accent-color: var(--interactive-primary, #3b82f6);
    }

    .actions-cell {
      text-align: center;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .header-cell {
        padding: var(--space-2, 8px) var(--space-3, 12px);
        font-size: var(--text-xs, 12px);
      }

      .sort-indicator {
        margin-left: var(--space-1, 4px);
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .table-header {
        background: var(--bg-secondary-dark, #111827);
        border-bottom-color: var(--border-primary-dark, #374151);
      }

      .header-cell {
        color: var(--text-secondary-dark, #9ca3af);
        border-right-color: var(--border-secondary-dark, #374151);
      }

      .header-cell.sticky {
        background: var(--bg-secondary-dark, #111827);
      }

      .sort-button:hover {
        color: var(--text-primary-dark, #f9fafb);
      }
    }

    /* High contrast mode */
    @media (prefers-contrast: high) {
      .header-cell {
        border-right-color: var(--border-high-contrast, #000000);
      }

      .table-header {
        border-bottom-color: var(--border-high-contrast, #000000);
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TableHeaderComponent {
  @Input() columns: DataTableColumn[] = [];
  @Input() currentSort?: DataTableSort;
  @Input() selectable: boolean = false;
  @Input() isAllSelected: boolean = false;
  @Input() isIndeterminate: boolean = false;
  @Input() hasActions: boolean = false;
  @Input() actionsColumnWidth: string = '120px';
  @Input() actionsColumnLabel: string = 'Actions';
  @Input() actionsColumnAlign: 'left' | 'center' | 'right' = 'center';

  @Output() sort = new EventEmitter<DataTableSort>();
  @Output() selectAll = new EventEmitter<boolean>();

  get visibleColumns(): DataTableColumn[] {
    return this.columns.filter(col => col.visible !== false);
  }

  trackByColumn(index: number, column: DataTableColumn): string {
    return column.key;
  }

  onSort(column: DataTableColumn): void {
    if (!column.sortable) return;

    const currentDirection = this.currentSort?.column === column.key ? this.currentSort.direction : null;
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';

    this.sort.emit({
      column: column.key,
      direction: newDirection
    });
  }

  onSelectAll(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.selectAll.emit(target.checked);
  }

  getHeaderCellClass(column: DataTableColumn): string {
    const classes = ['data-column'];
    
    if (column.headerCssClass) {
      classes.push(column.headerCssClass);
    }
    
    if (column.sticky) {
      classes.push('sticky');
    }
    
    if (column.sortable) {
      classes.push('sortable');
    }

    return classes.join(' ');
  }

  getSortIndicatorClass(column: DataTableColumn): string {
    if (this.currentSort?.column === column.key) {
      return `active ${this.currentSort.direction}`;
    }
    return 'none';
  }

  getSortIcon(column: DataTableColumn): string {
    if (this.currentSort?.column === column.key) {
      return this.currentSort.direction === 'asc' ? '↑' : '↓';
    }
    return '↕';
  }

  getSortLabel(column: DataTableColumn): string {
    const direction = this.currentSort?.column === column.key ? this.currentSort.direction : 'none';
    const nextDirection = direction === 'asc' ? 'descending' : 'ascending';
    return `Sort ${column.label} ${nextDirection}`;
  }

  getAriaSortValue(column: DataTableColumn): string | null {
    if (!column.sortable) return null;
    
    if (this.currentSort?.column === column.key) {
      return this.currentSort.direction === 'asc' ? 'ascending' : 'descending';
    }
    return 'none';
  }

  getSelectAllLabel(): string {
    if (this.isAllSelected) {
      return 'Deselect all rows';
    } else if (this.isIndeterminate) {
      return 'Select all rows';
    }
    return 'Select all rows';
  }

  getSelectAllAriaChecked(): string {
    if (this.isAllSelected) return 'true';
    if (this.isIndeterminate) return 'mixed';
    return 'false';
  }
}
