import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { TableHeaderComponent } from './table-header.component';
import { DataTableColumn, DataTableSort } from '../../../models';

describe('TableHeaderComponent', () => {
  let component: TableHeaderComponent;
  let fixture: ComponentFixture<TableHeaderComponent>;
  let compiled: HTMLElement;

  const mockColumns: DataTableColumn[] = [
    { key: 'id', label: 'ID', sortable: true, width: '80px' },
    { key: 'name', label: 'Name', sortable: true, filterable: true },
    { key: 'email', label: 'Email', sortable: false, filterable: true },
    { key: 'age', label: 'Age', sortable: true, align: 'right' },
    { key: 'status', label: 'Status', sortable: true, sticky: true, visible: false }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TableHeaderComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(TableHeaderComponent);
    component = fixture.componentInstance;
    compiled = fixture.nativeElement;
  });

  describe('Component Initialization', () => {
    it('ShouldCreateComponent', () => {
      expect(component).toBeTruthy();
    });

    it('ShouldHaveDefaultValues', () => {
      expect(component.columns).toEqual([]);
      expect(component.selectable).toBe(false);
      expect(component.isAllSelected).toBe(false);
      expect(component.isIndeterminate).toBe(false);
      expect(component.hasActions).toBe(false);
      expect(component.actionsColumnWidth).toBe('120px');
      expect(component.actionsColumnLabel).toBe('Actions');
      expect(component.actionsColumnAlign).toBe('center');
    });
  });

  describe('Column Rendering', () => {
    beforeEach(() => {
      component.columns = mockColumns;
      fixture.detectChanges();
    });

    it('ShouldRenderVisibleColumnsOnly', () => {
      const headerCells = compiled.querySelectorAll('.header-cell.data-column');
      const visibleColumns = mockColumns.filter(col => col.visible !== false);
      expect(headerCells.length).toBe(visibleColumns.length);
    });

    it('ShouldDisplayColumnLabels', () => {
      const headerLabels = compiled.querySelectorAll('.header-label');
      const visibleColumns = component.visibleColumns;
      
      headerLabels.forEach((label, index) => {
        expect(label.textContent?.trim()).toBe(visibleColumns[index].label);
      });
    });

    it('ShouldApplyColumnWidths', () => {
      const firstHeaderCell = compiled.querySelector('.header-cell.data-column') as HTMLElement;
      expect(firstHeaderCell.style.width).toBe('80px');
    });

    it('ShouldApplyColumnAlignment', () => {
      const ageHeaderCell = compiled.querySelectorAll('.header-cell.data-column')[3] as HTMLElement;
      expect(ageHeaderCell.style.textAlign).toBe('right');
    });

    it('ShouldApplyStickyClass', () => {
      // Status column is sticky but not visible, so we need to make it visible for this test
      component.columns = mockColumns.map(col => 
        col.key === 'status' ? { ...col, visible: true } : col
      );
      fixture.detectChanges();
      
      const stickyCell = compiled.querySelector('.header-cell.sticky');
      expect(stickyCell).toBeTruthy();
    });
  });

  describe('Selection Column', () => {
    beforeEach(() => {
      component.columns = mockColumns;
      component.selectable = true;
      fixture.detectChanges();
    });

    it('ShouldShowSelectionColumn', () => {
      const selectionCell = compiled.querySelector('.selection-cell');
      expect(selectionCell).toBeTruthy();
    });

    it('ShouldShowSelectAllCheckbox', () => {
      const selectAllCheckbox = compiled.querySelector('.selection-checkbox') as HTMLInputElement;
      expect(selectAllCheckbox).toBeTruthy();
      expect(selectAllCheckbox.type).toBe('checkbox');
    });

    it('ShouldReflectAllSelectedState', () => {
      component.isAllSelected = true;
      fixture.detectChanges();
      
      const selectAllCheckbox = compiled.querySelector('.selection-checkbox') as HTMLInputElement;
      expect(selectAllCheckbox.checked).toBe(true);
    });

    it('ShouldReflectIndeterminateState', () => {
      component.isIndeterminate = true;
      fixture.detectChanges();
      
      const selectAllCheckbox = compiled.querySelector('.selection-checkbox') as HTMLInputElement;
      expect(selectAllCheckbox.indeterminate).toBe(true);
    });

    it('ShouldEmitSelectAllEvent', () => {
      spyOn(component.selectAll, 'emit');
      
      const selectAllCheckbox = compiled.querySelector('.selection-checkbox') as HTMLInputElement;
      selectAllCheckbox.click();
      
      expect(component.selectAll.emit).toHaveBeenCalledWith(true);
    });
  });

  describe('Actions Column', () => {
    beforeEach(() => {
      component.columns = mockColumns;
      component.hasActions = true;
      component.actionsColumnWidth = '150px';
      component.actionsColumnLabel = 'Operations';
      component.actionsColumnAlign = 'left';
      fixture.detectChanges();
    });

    it('ShouldShowActionsColumn', () => {
      const actionsCell = compiled.querySelector('.actions-cell');
      expect(actionsCell).toBeTruthy();
    });

    it('ShouldApplyActionsColumnWidth', () => {
      const actionsCell = compiled.querySelector('.actions-cell') as HTMLElement;
      expect(actionsCell.style.width).toBe('150px');
    });

    it('ShouldApplyActionsColumnAlignment', () => {
      const actionsCell = compiled.querySelector('.actions-cell') as HTMLElement;
      expect(actionsCell.style.textAlign).toBe('left');
    });

    it('ShouldDisplayActionsColumnLabel', () => {
      const actionsLabel = compiled.querySelector('.actions-cell .header-label');
      expect(actionsLabel?.textContent?.trim()).toBe('Operations');
    });
  });

  describe('Sorting Functionality', () => {
    beforeEach(() => {
      component.columns = mockColumns;
      fixture.detectChanges();
    });

    it('ShouldShowSortButtonsForSortableColumns', () => {
      const sortButtons = compiled.querySelectorAll('.sort-button');
      const sortableColumns = mockColumns.filter(col => col.sortable && col.visible !== false);
      expect(sortButtons.length).toBe(sortableColumns.length);
    });

    it('ShouldNotShowSortButtonForNonSortableColumns', () => {
      const emailHeaderContent = compiled.querySelectorAll('.header-content')[2]; // Email column
      const sortButton = emailHeaderContent.querySelector('.sort-button');
      expect(sortButton).toBeFalsy();
    });

    it('ShouldEmitSortEvent', () => {
      spyOn(component.sort, 'emit');
      
      const firstSortButton = compiled.querySelector('.sort-button') as HTMLButtonElement;
      firstSortButton.click();
      
      expect(component.sort.emit).toHaveBeenCalledWith({
        column: 'id',
        direction: 'asc'
      });
    });

    it('ShouldToggleSortDirection', () => {
      spyOn(component.sort, 'emit');
      component.currentSort = { column: 'id', direction: 'asc' };
      
      component.onSort(mockColumns[0]);
      
      expect(component.sort.emit).toHaveBeenCalledWith({
        column: 'id',
        direction: 'desc'
      });
    });

    it('ShouldShowCorrectSortIndicator', () => {
      component.currentSort = { column: 'id', direction: 'asc' };
      fixture.detectChanges();
      
      const sortIndicator = compiled.querySelector('.sort-indicator');
      expect(sortIndicator).toHaveClass('active');
      expect(sortIndicator).toHaveClass('asc');
    });

    it('ShouldShowCorrectSortIcon', () => {
      component.currentSort = { column: 'id', direction: 'desc' };
      
      const icon = component.getSortIcon(mockColumns[0]);
      expect(icon).toBe('↓');
    });

    it('ShouldGenerateCorrectSortLabel', () => {
      component.currentSort = { column: 'id', direction: 'asc' };
      
      const label = component.getSortLabel(mockColumns[0]);
      expect(label).toBe('Sort ID descending');
    });
  });

  describe('Aria Attributes', () => {
    beforeEach(() => {
      component.columns = mockColumns;
      component.selectable = true;
      fixture.detectChanges();
    });

    it('ShouldHaveProperTableHeaderRole', () => {
      const thead = compiled.querySelector('.table-header');
      expect(thead?.getAttribute('role')).toBe('rowgroup');
    });

    it('ShouldHaveProperRowRole', () => {
      const headerRow = compiled.querySelector('.header-row');
      expect(headerRow?.getAttribute('role')).toBe('row');
    });

    it('ShouldHaveProperColumnHeaderRoles', () => {
      const headerCells = compiled.querySelectorAll('.header-cell');
      headerCells.forEach(cell => {
        expect(cell.getAttribute('role')).toBe('columnheader');
        expect(cell.getAttribute('scope')).toBe('col');
      });
    });

    it('ShouldHaveProperAriaSortValues', () => {
      component.currentSort = { column: 'id', direction: 'asc' };
      fixture.detectChanges();
      
      const firstHeaderCell = compiled.querySelector('.header-cell.data-column');
      expect(firstHeaderCell?.getAttribute('aria-sort')).toBe('ascending');
    });

    it('ShouldHaveProperCheckboxAriaLabels', () => {
      const selectAllCheckbox = compiled.querySelector('.selection-checkbox');
      expect(selectAllCheckbox?.getAttribute('aria-label')).toContain('Select all');
    });

    it('ShouldUpdateCheckboxAriaChecked', () => {
      component.isAllSelected = true;
      fixture.detectChanges();
      
      const selectAllCheckbox = compiled.querySelector('.selection-checkbox');
      expect(selectAllCheckbox?.getAttribute('aria-checked')).toBe('true');
    });

    it('ShouldShowMixedAriaCheckedForIndeterminate', () => {
      component.isIndeterminate = true;
      fixture.detectChanges();
      
      const selectAllCheckbox = compiled.querySelector('.selection-checkbox');
      expect(selectAllCheckbox?.getAttribute('aria-checked')).toBe('mixed');
    });
  });

  describe('Track By Functions', () => {
    it('ShouldTrackColumnsByKey', () => {
      const column = mockColumns[0];
      const result = component.trackByColumn(0, column);
      expect(result).toBe('id');
    });
  });

  describe('CSS Classes', () => {
    beforeEach(() => {
      component.columns = mockColumns;
      fixture.detectChanges();
    });

    it('ShouldApplyHeaderCellClass', () => {
      const column = { ...mockColumns[0], headerCssClass: 'custom-header' };
      const cssClass = component.getHeaderCellClass(column);
      expect(cssClass).toContain('custom-header');
    });

    it('ShouldApplySortableClass', () => {
      const cssClass = component.getHeaderCellClass(mockColumns[0]);
      expect(cssClass).toContain('sortable');
    });

    it('ShouldApplyStickyClass', () => {
      const stickyColumn = { ...mockColumns[0], sticky: true };
      const cssClass = component.getHeaderCellClass(stickyColumn);
      expect(cssClass).toContain('sticky');
    });

    it('ShouldGetCorrectSortIndicatorClass', () => {
      component.currentSort = { column: 'id', direction: 'desc' };
      const cssClass = component.getSortIndicatorClass(mockColumns[0]);
      expect(cssClass).toBe('active desc');
    });

    it('ShouldReturnNoneForUnsortedColumn', () => {
      component.currentSort = { column: 'name', direction: 'asc' };
      const cssClass = component.getSortIndicatorClass(mockColumns[0]);
      expect(cssClass).toBe('none');
    });
  });

  describe('Edge Cases', () => {
    it('ShouldHandleEmptyColumns', () => {
      component.columns = [];
      fixture.detectChanges();
      
      const headerCells = compiled.querySelectorAll('.header-cell.data-column');
      expect(headerCells.length).toBe(0);
    });

    it('ShouldHandleColumnsWithoutSortable', () => {
      const columnsWithoutSortable = [
        { key: 'test', label: 'Test' } // No sortable property
      ];
      component.columns = columnsWithoutSortable;
      fixture.detectChanges();
      
      const sortButton = compiled.querySelector('.sort-button');
      expect(sortButton).toBeFalsy();
    });

    it('ShouldHandleNullCurrentSort', () => {
      component.currentSort = undefined;
      
      const ariaSortValue = component.getAriaSortValue(mockColumns[0]);
      expect(ariaSortValue).toBe('none');
    });

    it('ShouldNotEmitSortForNonSortableColumn', () => {
      spyOn(component.sort, 'emit');
      
      const nonSortableColumn = { ...mockColumns[0], sortable: false };
      component.onSort(nonSortableColumn);
      
      expect(component.sort.emit).not.toHaveBeenCalled();
    });
  });

  describe('Computed Properties', () => {
    it('ShouldFilterVisibleColumns', () => {
      component.columns = mockColumns;
      const visibleColumns = component.visibleColumns;
      
      expect(visibleColumns.length).toBe(4); // Status column is not visible
      expect(visibleColumns.find(col => col.key === 'status')).toBeFalsy();
    });

    it('ShouldIncludeColumnsWithUndefinedVisible', () => {
      const columnsWithUndefinedVisible = [
        { key: 'test', label: 'Test' } // visible is undefined
      ];
      component.columns = columnsWithUndefinedVisible;
      
      const visibleColumns = component.visibleColumns;
      expect(visibleColumns.length).toBe(1);
    });
  });
});
