import {Component, EventEmitter, Input, Output} from '@angular/core';
import {NgClass, NgIf} from '@angular/common';
import {Router} from '@angular/router';

@Component({
  selector: 'app-button',
  imports: [
    NgC<PERSON>,
    NgIf,
  ],
  templateUrl: './button.component.html',
  styleUrl: './button.component.css'
})
export class ButtonComponent {
  @Input() text: string = '';
  @Input() icon: string = '';
  @Input() size: string | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' = 'md';
  @Input() variant: string | 'ghost' | 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info' = 'ghost';
  @Input() hover: string | 'none' | 'translate' | 'rotate' = 'translate';
  @Input() disabled: boolean = false;
  @Input() href?: string;
  @Input() title: string = '';
  @Output() onClick: EventEmitter<any> = new EventEmitter<any>();

  constructor(private router: Router) {
  }
  handleClick() {
    this.onClick.emit();
    if (this.href) {
      this.router.navigateByUrl(this.href).then();
    }
  }
}
