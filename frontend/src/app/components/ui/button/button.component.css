.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-8) var(--space-6);
  font-family: inherit;
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  line-height: 1;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Button Variants */
.btn-ghost {
  background-color: transparent;
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-ghost:hover {
  background-color: var(--bg-surface-hover);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.btn-primary:hover {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--bg-surface);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover {
  background-color: var(--bg-surface-hover);
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background-color: var(--success-500);
  color: white;
  border-color: var(--success-500);
}

.btn-success:hover {
  background-color: var(--success-700);
  border-color: var(--success-700);
  box-shadow: var(--shadow-md);
}

.btn-warning {
  background-color: var(--warning-500);
  color: white;
  border-color: var(--warning-500);
}

.btn-warning:hover {
  background-color: var(--warning-700);
  border-color: var(--warning-700);
  box-shadow: var(--shadow-md);
}

.btn-error {
  background-color: var(--error-500);
  color: white;
  border-color: var(--error-500);
}

.btn-error:hover {
  background-color: var(--error-700);
  border-color: var(--error-700);
  box-shadow: var(--shadow-md);
}

.btn-info {
  background-color: var(--info-500);
  color: white;
  border-color: var(--info-500);
}

.btn-info:hover {
  background-color: var(--info-700);
  border-color: var(--info-700);
  box-shadow: var(--shadow-md);
}

/* Button Sizes */
.btn-xs {
  padding: var(--space-3) var(--space-3);
  font-weight: var(--font-normal);
}

.btn-sm {
  padding: var(--space-4) var(--space-4);
  font-weight: var(--font-normal);
}

.btn-md {
  padding: var(--space-8) var(--space-6);
  font-weight: var(--font-medium);
}

.btn-lg {
  padding: var(--space-8) var(--space-8);
  font-size: var(--text-lg);
}

.btn-xl {
  padding: var(--space-10) var(--space-8);
  font-size: var(--text-xl);
}

.btn-2xl {
  padding: var(--space-12) var(--space-10);
  font-size: var(--text-xl);
}

.btn-icon {
  padding: var(--space-3);
  aspect-ratio: 1;
}


.btn-translate:hover {
  transform: translateY(-1px);
}

.btn-rotate:hover {
  transform: rotate(15deg);
}
