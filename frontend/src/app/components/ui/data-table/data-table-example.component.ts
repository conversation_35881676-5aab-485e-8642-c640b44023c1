import { Component, OnInit, TemplateRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DataTableComponent } from './data-table.component';
import { 
  DataTableColumn, 
  DataTableConfig, 
  DataTableAction, 
  DataTableSort, 
  DataTableFilter, 
  DataTableSelection,
  PagedResult 
} from '../../../models';

interface ExampleUser {
  id: number;
  name: string;
  email: string;
  age: number;
  department: string;
  salary: number;
  active: boolean;
  joinDate: Date;
  avatar?: string;
  skills: string[];
}

@Component({
  selector: 'app-data-table-example',
  standalone: true,
  imports: [CommonModule, FormsModule, DataTableComponent],
  template: `
    <div class="example-container">
      <h1>Data Table Example</h1>
      
      <!-- Configuration Controls -->
      <div class="config-panel">
        <h3>Configuration</h3>
        <div class="config-grid">
          <label>
            <input type="checkbox" [(ngModel)]="config.selectable" (change)="updateConfig()">
            Selectable
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="config.multiSelect" (change)="updateConfig()">
            Multi Select
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="config.sortable" (change)="updateConfig()">
            Sortable
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="config.filterable" (change)="updateConfig()">
            Filterable
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="config.paginated" (change)="updateConfig()">
            Paginated
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="config.striped" (change)="updateConfig()">
            Striped
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="config.bordered" (change)="updateConfig()">
            Bordered
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="config.hover" (change)="updateConfig()">
            Hover
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="config.dense" (change)="updateConfig()">
            Dense
          </label>
          <label>
            <input type="checkbox" [(ngModel)]="loading" (change)="updateConfig()">
            Loading
          </label>
        </div>
        
        <div class="actions-panel">
          <button (click)="addRandomUser()" class="btn btn-primary">Add Random User</button>
          <button (click)="clearData()" class="btn btn-secondary">Clear Data</button>
          <button (click)="loadSampleData()" class="btn btn-secondary">Load Sample Data</button>
          <button (click)="toggleServerMode()" class="btn btn-outline">
            {{ serverMode ? 'Client Mode' : 'Server Mode' }}
          </button>
        </div>
      </div>

      <!-- Selection Info -->
      <div class="selection-info" *ngIf="selection.selected.length > 0">
        <p>Selected {{ selection.selected.length }} user(s)</p>
        <button (click)="clearSelection()" class="btn btn-sm">Clear Selection</button>
      </div>

      <!-- Data Table -->
      <app-data-table
        [data]="serverMode ? [] : users"
        [pagedResult]="serverMode ? pagedResult : undefined"
        [columns]="columns"
        [config]="config"
        [actions]="actions"
        [loading]="loading"
        [currentSort]="currentSort"
        [currentFilters]="currentFilters"
        [selectedItems]="selection.selected"
        [showAddButton]="true"
        [addButtonLabel]="'Add New User'"
        (sortChange)="onSort($event)"
        (filtersChange)="onFiltersChange($event)"
        (pageChange)="onPageChange($event)"
        (pageSizeChange)="onPageSizeChange($event)"
        (selectionChange)="onSelectionChange($event)"
        (actionClick)="onActionClick($event)"
        (addItemClick)="onAddUser()"
        (stateChange)="onStateChange($event)">
      </app-data-table>

      <!-- Debug Info -->
      <div class="debug-panel" *ngIf="showDebug">
        <h3>Debug Information</h3>
        <div class="debug-grid">
          <div>
            <h4>Current Sort</h4>
            <pre>{{ currentSort | json }}</pre>
          </div>
          <div>
            <h4>Current Filters</h4>
            <pre>{{ currentFilters | json }}</pre>
          </div>
          <div>
            <h4>Selection</h4>
            <pre>{{ selection | json }}</pre>
          </div>
          <div>
            <h4>Paged Result</h4>
            <pre>{{ pagedResult | json }}</pre>
          </div>
        </div>
      </div>

      <button (click)="showDebug = !showDebug" class="debug-toggle">
        {{ showDebug ? 'Hide' : 'Show' }} Debug Info
      </button>
    </div>

    <!-- Custom Templates -->
    <ng-template #avatarTemplate let-user let-value="value">
      <div class="avatar-cell">
        <img [src]="user.avatar || getDefaultAvatar(user.name)" 
             [alt]="user.name + ' avatar'"
             class="avatar">
        <span>{{ user.name }}</span>
      </div>
    </ng-template>

    <ng-template #salaryTemplate let-user let-value="value">
      <span class="salary-cell" [class]="getSalaryClass(value)">
        {{ value | currency }}
      </span>
    </ng-template>

    <ng-template #skillsTemplate let-user let-value="value">
      <div class="skills-cell">
        <span *ngFor="let skill of value" class="skill-tag">
          {{ skill }}
        </span>
      </div>
    </ng-template>

    <ng-template #statusTemplate let-user let-value="value">
      <span class="status-badge" [class]="value ? 'status-active' : 'status-inactive'">
        {{ value ? 'Active' : 'Inactive' }}
      </span>
    </ng-template>
  `,
  styles: [`
    .example-container {
      padding: var(--space-6, 24px);
      max-width: 1200px;
      margin: 0 auto;
    }

    .config-panel {
      background: var(--bg-secondary, #f9fafb);
      padding: var(--space-4, 16px);
      border-radius: var(--radius-md, 8px);
      margin-bottom: var(--space-6, 24px);
    }

    .config-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: var(--space-3, 12px);
      margin-bottom: var(--space-4, 16px);
    }

    .config-grid label {
      display: flex;
      align-items: center;
      gap: var(--space-2, 8px);
      font-size: var(--text-sm, 14px);
    }

    .actions-panel {
      display: flex;
      gap: var(--space-3, 12px);
      flex-wrap: wrap;
    }

    .btn {
      padding: var(--space-2, 8px) var(--space-4, 16px);
      border-radius: var(--radius-sm, 4px);
      border: 1px solid;
      cursor: pointer;
      font-size: var(--text-sm, 14px);
      transition: all var(--transition-fast, 150ms);
    }

    .btn-primary {
      background: var(--interactive-primary, #3b82f6);
      color: white;
      border-color: var(--interactive-primary, #3b82f6);
    }

    .btn-secondary {
      background: var(--bg-secondary, #f9fafb);
      color: var(--text-secondary, #6b7280);
      border-color: var(--border-primary, #e5e7eb);
    }

    .btn-outline {
      background: transparent;
      color: var(--interactive-primary, #3b82f6);
      border-color: var(--interactive-primary, #3b82f6);
    }

    .btn-sm {
      padding: var(--space-1, 4px) var(--space-2, 8px);
      font-size: var(--text-xs, 12px);
    }

    .selection-info {
      background: var(--bg-selected, #eff6ff);
      padding: var(--space-3, 12px);
      border-radius: var(--radius-sm, 4px);
      margin-bottom: var(--space-4, 16px);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .debug-panel {
      background: var(--bg-tertiary, #f3f4f6);
      padding: var(--space-4, 16px);
      border-radius: var(--radius-md, 8px);
      margin-top: var(--space-6, 24px);
    }

    .debug-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--space-4, 16px);
    }

    .debug-grid pre {
      background: var(--bg-primary, #ffffff);
      padding: var(--space-2, 8px);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--text-xs, 12px);
      overflow-x: auto;
    }

    .debug-toggle {
      position: fixed;
      bottom: var(--space-4, 16px);
      right: var(--space-4, 16px);
      background: var(--interactive-primary, #3b82f6);
      color: white;
      border: none;
      padding: var(--space-2, 8px) var(--space-3, 12px);
      border-radius: var(--radius-sm, 4px);
      cursor: pointer;
      font-size: var(--text-sm, 14px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Custom Cell Styles */
    .avatar-cell {
      display: flex;
      align-items: center;
      gap: var(--space-2, 8px);
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      object-fit: cover;
    }

    .salary-cell.high {
      color: var(--status-success, #10b981);
      font-weight: var(--font-semibold, 600);
    }

    .salary-cell.medium {
      color: var(--status-warning, #f59e0b);
    }

    .salary-cell.low {
      color: var(--status-error, #ef4444);
    }

    .skills-cell {
      display: flex;
      gap: var(--space-1, 4px);
      flex-wrap: wrap;
    }

    .skill-tag {
      background: var(--bg-secondary, #f9fafb);
      color: var(--text-secondary, #6b7280);
      padding: var(--space-1, 4px) var(--space-2, 8px);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--text-xs, 12px);
    }

    .status-badge {
      padding: var(--space-1, 4px) var(--space-2, 8px);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--text-xs, 12px);
      font-weight: var(--font-medium, 500);
    }

    .status-active {
      background: var(--status-success-bg, #d1fae5);
      color: var(--status-success, #10b981);
    }

    .status-inactive {
      background: var(--status-error-bg, #fee2e2);
      color: var(--status-error, #ef4444);
    }

    /* Responsive */
    @media (max-width: 768px) {
      .config-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      }

      .actions-panel {
        flex-direction: column;
      }

      .selection-info {
        flex-direction: column;
        gap: var(--space-2, 8px);
        align-items: stretch;
      }
    }
  `]
})
export class DataTableExampleComponent implements OnInit, AfterViewInit {
  @ViewChild('avatarTemplate') avatarTemplate!: TemplateRef<any>;
  @ViewChild('salaryTemplate') salaryTemplate!: TemplateRef<any>;
  @ViewChild('skillsTemplate') skillsTemplate!: TemplateRef<any>;
  @ViewChild('statusTemplate') statusTemplate!: TemplateRef<any>;

  users: ExampleUser[] = [];
  columns: DataTableColumn<ExampleUser>[] = [];
  config: DataTableConfig = {
    selectable: true,
    multiSelect: true,
    sortable: true,
    filterable: true,
    paginated: true,
    striped: false,
    bordered: false,
    hover: true,
    dense: false,
    pageSize: 10,
    pageSizeOptions: [5, 10, 25, 50, 100],
    actionColumn: true
  };

  actions: DataTableAction<ExampleUser>[] = [
    {
      label: 'View',
      icon: '👁️',
      action: (user) => this.viewUser(user),
      visible: () => true,
      disabled: () => false
    },
    {
      label: 'Edit',
      icon: '✏️',
      action: (user) => this.editUser(user),
      visible: () => true,
      disabled: () => false
    },
    {
      label: 'Delete',
      icon: '🗑️',
      action: (user) => this.deleteUser(user),
      visible: (user) => user.active,
      disabled: () => false,
      confirmMessage: 'Are you sure you want to delete this user?'
    }
  ];

  loading = false;
  serverMode = false;
  showDebug = false;

  // State
  currentSort?: DataTableSort;
  currentFilters: DataTableFilter[] = [];
  selection: DataTableSelection<ExampleUser> = {
    selected: [],
    isAllSelected: false,
    isIndeterminate: false
  };
  pagedResult?: PagedResult<ExampleUser>;

  ngOnInit() {
    this.loadSampleData();
  }

  ngAfterViewInit() {
    this.setupColumns();
  }

  private setupColumns() {
    this.columns = [
      {
        key: 'id',
        label: 'ID',
        type: 'number',
        sortable: true,
        width: '80px'
      },
      {
        key: 'name',
        label: 'User',
        type: 'custom',
        sortable: true,
        filterable: true,
        customTemplate: this.avatarTemplate,
        width: '200px'
      },
      {
        key: 'email',
        label: 'Email',
        type: 'text',
        sortable: true,
        filterable: true
      },
      {
        key: 'age',
        label: 'Age',
        type: 'number',
        sortable: true,
        filterable: true,
        align: 'right',
        width: '80px'
      },
      {
        key: 'department',
        label: 'Department',
        type: 'text',
        sortable: true,
        filterable: true
      },
      {
        key: 'salary',
        label: 'Salary',
        type: 'custom',
        sortable: true,
        filterable: true,
        customTemplate: this.salaryTemplate,
        align: 'right',
        width: '120px'
      },
      {
        key: 'skills',
        label: 'Skills',
        type: 'custom',
        sortable: false,
        filterable: false,
        customTemplate: this.skillsTemplate,
        width: '200px'
      },
      {
        key: 'active',
        label: 'Status',
        type: 'custom',
        sortable: true,
        filterable: true,
        customTemplate: this.statusTemplate,
        width: '100px'
      },
      {
        key: 'joinDate',
        label: 'Join Date',
        type: 'date',
        sortable: true,
        width: '120px'
      }
    ];
  }

  loadSampleData() {
    this.users = this.generateSampleUsers(50);
    if (this.serverMode) {
      this.updatePagedResult();
    }
  }

  private generateSampleUsers(count: number): ExampleUser[] {
    const departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations'];
    const skills = ['JavaScript', 'TypeScript', 'Angular', 'React', 'Node.js', 'Python', 'Java', 'C#', 'SQL', 'AWS'];
    const names = ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Brown', 'Charlie Wilson', 'Diana Davis', 'Eve Miller', 'Frank Garcia'];

    return Array.from({ length: count }, (_, i) => ({
      id: i + 1,
      name: names[i % names.length] + ` ${i + 1}`,
      email: `user${i + 1}@example.com`,
      age: 22 + Math.floor(Math.random() * 40),
      department: departments[Math.floor(Math.random() * departments.length)],
      salary: 40000 + Math.floor(Math.random() * 120000),
      active: Math.random() > 0.2,
      joinDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28)),
      skills: skills.slice(0, 2 + Math.floor(Math.random() * 4))
    }));
  }

  addRandomUser() {
    const newUser = this.generateSampleUsers(1)[0];
    newUser.id = Math.max(...this.users.map(u => u.id)) + 1;
    this.users = [...this.users, newUser];
    
    if (this.serverMode) {
      this.updatePagedResult();
    }
  }

  clearData() {
    this.users = [];
    this.selection.selected = [];
    if (this.serverMode) {
      this.updatePagedResult();
    }
  }

  clearSelection() {
    this.selection.selected = [];
    this.updateSelectionState();
  }

  toggleServerMode() {
    this.serverMode = !this.serverMode;
    if (this.serverMode) {
      this.updatePagedResult();
    }
  }

  updateConfig() {
    // Force change detection
    this.config = { ...this.config };
  }

  // Event Handlers
  onSort(sort: DataTableSort) {
    this.currentSort = sort;
    console.log('Sort changed:', sort);
    
    if (this.serverMode) {
      this.loadServerData();
    }
  }

  onFiltersChange(filters: DataTableFilter[]) {
    this.currentFilters = filters;
    console.log('Filters changed:', filters);
    
    if (this.serverMode) {
      this.loadServerData();
    }
  }

  onPageChange(page: number) {
    console.log('Page changed:', page);
    
    if (this.serverMode) {
      this.loadServerData(page);
    }
  }

  onPageSizeChange(pageSize: number) {
    console.log('Page size changed:', pageSize);
    this.config.pageSize = pageSize;
    
    if (this.serverMode) {
      this.loadServerData();
    }
  }

  onSelectionChange(selection: DataTableSelection<ExampleUser>) {
    this.selection = selection;
    console.log('Selection changed:', selection);
  }

  onActionClick(event: { action: DataTableAction<ExampleUser>; item: ExampleUser }) {
    console.log('Action clicked:', event);
  }

  onAddUser() {
    console.log('Add user clicked');
    this.addRandomUser();
  }

  onStateChange(state: any) {
    console.log('State changed:', state);
  }

  // Action Methods
  viewUser(user: ExampleUser) {
    alert(`Viewing user: ${user.name}`);
  }

  editUser(user: ExampleUser) {
    alert(`Editing user: ${user.name}`);
  }

  deleteUser(user: ExampleUser) {
    this.users = this.users.filter(u => u.id !== user.id);
    this.selection.selected = this.selection.selected.filter(u => u.id !== user.id);
    
    if (this.serverMode) {
      this.updatePagedResult();
    }
  }

  // Helper Methods
  getDefaultAvatar(name: string): string {
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=3b82f6&color=fff`;
  }

  getSalaryClass(salary: number): string {
    if (salary >= 100000) return 'high';
    if (salary >= 70000) return 'medium';
    return 'low';
  }

  private updateSelectionState() {
    const displayData = this.serverMode ? (this.pagedResult?.items || []) : this.users;
    this.selection.isAllSelected = this.selection.selected.length > 0 && this.selection.selected.length === displayData.length;
    this.selection.isIndeterminate = this.selection.selected.length > 0 && this.selection.selected.length < displayData.length;
  }

  private updatePagedResult() {
    const pageSize = this.config.pageSize || 10;
    const totalCount = this.users.length;
    const totalPages = Math.ceil(totalCount / pageSize);
    const currentPage = 1;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, totalCount);

    this.pagedResult = {
      items: this.users.slice(startIndex, endIndex),
      totalCount,
      pageNumber: currentPage,
      pageSize,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1
    };
  }

  private async loadServerData(page: number = 1) {
    this.loading = true;
    
    // Simulate server delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const pageSize = this.config.pageSize || 10;
    let filteredUsers = [...this.users];

    // Apply filters
    this.currentFilters.forEach(filter => {
      filteredUsers = filteredUsers.filter(user => {
        const value = (user as any)[filter.column];
        if (typeof value === 'string') {
          return value.toLowerCase().includes(filter.value.toLowerCase());
        }
        return value === filter.value;
      });
    });

    // Apply sorting
    if (this.currentSort) {
      filteredUsers.sort((a, b) => {
        const aValue = (a as any)[this.currentSort!.column];
        const bValue = (b as any)[this.currentSort!.column];
        
        if (aValue < bValue) return this.currentSort!.direction === 'asc' ? -1 : 1;
        if (aValue > bValue) return this.currentSort!.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    const totalCount = filteredUsers.length;
    const totalPages = Math.ceil(totalCount / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, totalCount);

    this.pagedResult = {
      items: filteredUsers.slice(startIndex, endIndex),
      totalCount,
      pageNumber: page,
      pageSize,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    };

    this.loading = false;
  }
}
