import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  ChangeDetectionStrategy, 
  OnInit, 
  OnDestroy,
  TemplateRef,
  ContentChild,
  ViewChild,
  ElementRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { 
  DataTableColumn, 
  DataTableConfig, 
  DataTableSort, 
  DataTableFilter, 
  DataTableSelection, 
  DataTableAction,
  DataTableState,
  PagedResult,
  QueryParams
} from '../../../models';

import { TableSkeletonComponent } from '../table-skeleton/table-skeleton.component';
import { TableHeaderComponent } from '../table-header/table-header.component';
import { TableFiltersComponent } from '../table-filters/table-filters.component';
import { TablePaginationComponent } from '../table-pagination/table-pagination.component';
import { TableEmptyComponent } from '../table-empty/table-empty.component';
import { LoadingSpinnerComponent } from '../loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-data-table',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableSkeletonComponent,
    TableHeaderComponent,
    TableFiltersComponent,
    TablePaginationComponent,
    TableEmptyComponent,
    LoadingSpinnerComponent
  ],
  template: `
    <div class="data-table-container" [class]="getContainerClass()">
      <!-- Filters -->
      <app-table-filters
        *ngIf="config.filterable && !config.loading"
        [columns]="columns"
        [filters]="currentFilters"
        [debounceTime]="filterDebounceTime"
        (filtersChange)="onFiltersChange($event)">
      </app-table-filters>

      <!-- Table Wrapper -->
      <div class="table-wrapper" [style.max-height]="config.maxHeight">
        <!-- Loading Skeleton -->
        <app-table-skeleton
          *ngIf="config.loading && showSkeleton"
          [rows]="skeletonRows"
          [columns]="columns.length + (config.selectable ? 1 : 0) + (config.actionColumn ? 1 : 0)"
          [columnWidths]="getSkeletonColumnWidths()"
          [columnTypes]="getSkeletonColumnTypes()"
          [loadingMessage]="config.loadingMessage || 'Loading table data'">
        </app-table-skeleton>

        <!-- Main Table -->
        <table 
          *ngIf="!config.loading || !showSkeleton"
          class="data-table"
          [class]="getTableClass()"
          role="table"
          [attr.aria-label]="tableAriaLabel"
          [attr.aria-rowcount]="totalRows"
          [attr.aria-colcount]="totalColumns">

          <!-- Table Header -->
          <app-table-header
            [columns]="columns"
            [currentSort]="currentSort"
            [selectable]="config.selectable || false"
            [isAllSelected]="selection.isAllSelected"
            [isIndeterminate]="selection.isIndeterminate"
            [hasActions]="config.actionColumn || false"
            [actionsColumnWidth]="config.actionColumnWidth || '120px'"
            [actionsColumnLabel]="config.actionColumnLabel || 'Actions'"
            [actionsColumnAlign]="config.actionColumnAlign || 'center'"
            (sort)="onSort($event)"
            (selectAll)="onSelectAll($event)">
          </app-table-header>

          <!-- Table Body -->
          <tbody class="table-body" role="rowgroup">
            <tr
              *ngFor="let item of displayData; trackBy: getTrackByFn(); let i = index"
              class="table-row"
              [class]="getRowClass(item, i)"
              [attr.aria-rowindex]="i + 2"
              [attr.aria-selected]="isItemSelected(item)"
              role="row">

              <!-- Selection Column -->
              <td 
                *ngIf="config.selectable"
                class="table-cell selection-cell"
                role="gridcell">
                <input
                  type="checkbox"
                  class="selection-checkbox"
                  [checked]="isItemSelected(item)"
                  (change)="onItemSelect(item, $event)"
                  [attr.aria-label]="'Select row ' + (i + 1)">
              </td>

              <!-- Data Columns -->
              <td
                *ngFor="let column of visibleColumns; trackBy: trackByColumn"
                class="table-cell"
                [class]="getCellClass(column, item)"
                [style.width]="column.width"
                [style.min-width]="column.minWidth"
                [style.max-width]="column.maxWidth"
                [style.text-align]="column.align"
                role="gridcell">

                <!-- Custom Cell Template -->
                <ng-container *ngIf="column.customTemplate; else defaultCell">
                  <ng-container *ngTemplateOutlet="column.customTemplate; context: { 
                    $implicit: item, 
                    column: column, 
                    value: getCellValue(item, column),
                    index: i 
                  }"></ng-container>
                </ng-container>

                <!-- Default Cell Content -->
                <ng-template #defaultCell>
                  <span class="cell-content">
                    {{ formatCellValue(getCellValue(item, column), column) }}
                  </span>
                </ng-template>
              </td>

              <!-- Actions Column -->
              <td 
                *ngIf="config.actionColumn"
                class="table-cell actions-cell"
                [style.width]="config.actionColumnWidth"
                [style.text-align]="config.actionColumnAlign"
                role="gridcell">
                
                <div class="actions-container">
                  <button
                    *ngFor="let action of getVisibleActions(item); trackBy: trackByAction"
                    type="button"
                    class="action-btn"
                    [class]="action.cssClass"
                    [disabled]="isActionDisabled(action, item)"
                    [title]="action.tooltip || action.label"
                    (click)="onActionClick(action, item)"
                    [attr.aria-label]="action.label + ' for row ' + (i + 1)">
                    
                    <span *ngIf="action.icon" class="action-icon">{{ action.icon }}</span>
                    <span class="action-label">{{ action.label }}</span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Empty State -->
        <app-table-empty
          *ngIf="!config.loading && isEmpty"
          [message]="getEmptyMessage()"
          [title]="getEmptyTitle()"
          [description]="getEmptyDescription()"
          [emptyType]="getEmptyType()"
          [showClearFilters]="hasActiveFilters"
          [primaryAction]="showAddButton"
          [primaryActionLabel]="addButtonLabel"
          (clearFiltersClick)="onClearFilters()"
          (primaryActionClick)="onAddItem()">
        </app-table-empty>
      </div>

      <!-- Pagination -->
      <app-table-pagination
        *ngIf="config.paginated && !config.loading && !isEmpty"
        [currentPage]="currentPage"
        [totalPages]="totalPages"
        [totalItems]="totalItems"
        [pageSize]="currentPageSize"
        [pageSizeOptions]="config.pageSizeOptions || [5, 10, 25, 50, 100]"
        [showInfo]="config.showPaginationInfo !== false"
        [showPageSizeSelector]="config.showPageSizeSelector !== false"
        (pageChange)="onPageChange($event)"
        (pageSizeChange)="onPageSizeChange($event)">
      </app-table-pagination>
    </div>
  `,
  styles: [`
    .data-table-container {
      display: flex;
      flex-direction: column;
      gap: var(--space-4, 16px);
      width: 100%;
    }

    .table-wrapper {
      position: relative;
      overflow: auto;
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-md, 8px);
      background: var(--bg-primary, #ffffff);
    }

    .data-table {
      width: 100%;
      border-collapse: collapse;
      font-size: var(--text-sm, 14px);
      line-height: var(--leading-normal, 1.5);
    }

    .table-body {
      background: var(--bg-primary, #ffffff);
    }

    .table-row {
      border-bottom: 1px solid var(--border-secondary, #f3f4f6);
      transition: background-color var(--transition-fast, 150ms);
    }

    .table-row:hover {
      background: var(--bg-hover, #f9fafb);
    }

    .table-row.selected {
      background: var(--bg-selected, #eff6ff);
    }

    .table-row.striped:nth-child(even) {
      background: var(--bg-striped, #f8fafc);
    }

    .table-cell {
      padding: var(--space-3, 12px) var(--space-4, 16px);
      border-right: 1px solid var(--border-secondary, #f3f4f6);
      vertical-align: middle;
    }

    .table-cell:last-child {
      border-right: none;
    }

    .selection-cell {
      width: 48px;
      text-align: center;
    }

    .selection-checkbox {
      width: 16px;
      height: 16px;
      cursor: pointer;
      accent-color: var(--interactive-primary, #3b82f6);
    }

    .cell-content {
      display: block;
      word-break: break-word;
    }

    .actions-cell {
      white-space: nowrap;
    }

    .actions-container {
      display: flex;
      gap: var(--space-2, 8px);
      align-items: center;
      justify-content: center;
    }

    .action-btn {
      display: inline-flex;
      align-items: center;
      gap: var(--space-1, 4px);
      padding: var(--space-1, 4px) var(--space-2, 8px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-sm, 4px);
      background: var(--bg-primary, #ffffff);
      color: var(--text-secondary, #6b7280);
      font-size: var(--text-xs, 12px);
      cursor: pointer;
      transition: all var(--transition-fast, 150ms);
    }

    .action-btn:hover:not(:disabled) {
      background: var(--bg-secondary, #f9fafb);
      color: var(--text-primary, #111827);
      border-color: var(--border-secondary, #d1d5db);
    }

    .action-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .action-icon {
      font-size: var(--text-xs, 12px);
    }

    .action-label {
      font-weight: var(--font-medium, 500);
    }

    /* Table Variants */
    .data-table.striped .table-row:nth-child(even) {
      background: var(--bg-striped, #f8fafc);
    }

    .data-table.bordered {
      border: 2px solid var(--border-primary, #e5e7eb);
    }

    .data-table.bordered .table-cell {
      border: 1px solid var(--border-primary, #e5e7eb);
    }

    .data-table.hover .table-row:hover {
      background: var(--bg-hover, #f9fafb);
    }

    .data-table.dense .table-cell {
      padding: var(--space-2, 8px) var(--space-3, 12px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .table-cell {
        padding: var(--space-2, 8px) var(--space-3, 12px);
        font-size: var(--text-xs, 12px);
      }

      .actions-container {
        flex-direction: column;
        gap: var(--space-1, 4px);
      }

      .action-btn {
        padding: var(--space-1, 4px);
        min-width: auto;
      }

      .action-label {
        display: none;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .table-wrapper {
        border-color: var(--border-primary-dark, #374151);
        background: var(--bg-primary-dark, #1f2937);
      }

      .data-table,
      .table-body {
        background: var(--bg-primary-dark, #1f2937);
      }

      .table-row {
        border-bottom-color: var(--border-secondary-dark, #374151);
      }

      .table-row:hover {
        background: var(--bg-hover-dark, #111827);
      }

      .table-row.selected {
        background: var(--bg-selected-dark, #1e3a8a);
      }

      .table-cell {
        border-right-color: var(--border-secondary-dark, #374151);
      }

      .action-btn {
        background: var(--bg-secondary-dark, #111827);
        border-color: var(--border-primary-dark, #374151);
        color: var(--text-secondary-dark, #9ca3af);
      }

      .action-btn:hover:not(:disabled) {
        background: var(--bg-tertiary-dark, #374151);
        color: var(--text-primary-dark, #f9fafb);
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DataTableComponent<T = any> implements OnInit, OnDestroy {
  @Input() data: T[] = [];
  @Input() columns: DataTableColumn<T>[] = [];
  @Input() config: DataTableConfig = {};
  @Input() actions: DataTableAction<T>[] = [];
  @Input() loading: boolean = false;
  @Input() error: string | null = null;
  
  // Pagination
  @Input() pagedResult?: PagedResult<T>;
  @Input() currentPage: number = 1;
  @Input() pageSize: number = 10;
  
  // State
  @Input() currentSort?: DataTableSort;
  @Input() currentFilters: DataTableFilter[] = [];
  @Input() selectedItems: T[] = [];
  
  // Configuration
  @Input() tableAriaLabel: string = 'Data table';
  @Input() filterDebounceTime: number = 300;
  @Input() showSkeleton: boolean = true;
  @Input() skeletonRows: number = 5;
  @Input() showAddButton: boolean = false;
  @Input() addButtonLabel: string = 'Add Item';

  // Templates
  @ContentChild('cellTemplate') cellTemplate?: TemplateRef<any>;
  @ContentChild('headerTemplate') headerTemplate?: TemplateRef<any>;
  @ContentChild('emptyTemplate') emptyTemplate?: TemplateRef<any>;

  @ViewChild('tableElement') tableElement?: ElementRef<HTMLTableElement>;

  // Events
  @Output() sortChange = new EventEmitter<DataTableSort>();
  @Output() filtersChange = new EventEmitter<DataTableFilter[]>();
  @Output() pageChange = new EventEmitter<number>();
  @Output() pageSizeChange = new EventEmitter<number>();
  @Output() selectionChange = new EventEmitter<DataTableSelection<T>>();
  @Output() actionClick = new EventEmitter<{ action: DataTableAction<T>; item: T }>();
  @Output() addItemClick = new EventEmitter<void>();
  @Output() stateChange = new EventEmitter<DataTableState>();

  private destroy$ = new Subject<void>();

  // Computed properties
  selection: DataTableSelection<T> = {
    selected: [],
    isAllSelected: false,
    isIndeterminate: false
  };

  ngOnInit(): void {
    this.updateSelection();
    this.updateConfig();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Getters
  get displayData(): T[] {
    return this.pagedResult?.items || this.data;
  }

  get totalItems(): number {
    return this.pagedResult?.totalCount || this.data.length;
  }

  get totalPages(): number {
    return this.pagedResult?.totalPages || Math.ceil(this.totalItems / this.currentPageSize);
  }

  get currentPageSize(): number {
    return this.pagedResult?.pageSize || this.pageSize;
  }

  get visibleColumns(): DataTableColumn<T>[] {
    return this.columns.filter(col => col.visible !== false);
  }

  get isEmpty(): boolean {
    return this.displayData.length === 0;
  }

  get hasActiveFilters(): boolean {
    return this.currentFilters.length > 0;
  }

  get totalRows(): number {
    return this.displayData.length + 1; // +1 for header
  }

  get totalColumns(): number {
    let count = this.visibleColumns.length;
    if (this.config.selectable) count++;
    if (this.config.actionColumn) count++;
    return count;
  }

  // Track by functions
  getTrackByFn(): (index: number, item: T) => any {
    return this.config.trackByFn || ((index: number, item: T) => index);
  }

  trackByColumn(index: number, column: DataTableColumn<T>): string {
    return column.key;
  }

  trackByAction(index: number, action: DataTableAction<T>): string {
    return action.label;
  }

  // Event handlers
  onSort(sort: DataTableSort): void {
    this.sortChange.emit(sort);
    this.emitStateChange();
  }

  onFiltersChange(filters: DataTableFilter[]): void {
    this.filtersChange.emit(filters);
    this.emitStateChange();
  }

  onPageChange(page: number): void {
    this.pageChange.emit(page);
    this.emitStateChange();
  }

  onPageSizeChange(size: number): void {
    this.pageSizeChange.emit(size);
    this.emitStateChange();
  }

  onSelectAll(selected: boolean): void {
    const newSelection = selected ? [...this.displayData] : [];
    this.updateSelectionState(newSelection);
    this.selectionChange.emit(this.selection);
    this.emitStateChange();
  }

  onItemSelect(item: T, event: Event): void {
    const target = event.target as HTMLInputElement;
    let newSelection = [...this.selectedItems];

    if (target.checked) {
      if (!this.isItemSelected(item)) {
        newSelection.push(item);
      }
    } else {
      newSelection = newSelection.filter(selected => selected !== item);
    }

    this.updateSelectionState(newSelection);
    this.selectionChange.emit(this.selection);
    this.emitStateChange();
  }

  onActionClick(action: DataTableAction<T>, item: T): void {
    if (action.confirmMessage) {
      if (confirm(action.confirmMessage)) {
        action.action(item);
        this.actionClick.emit({ action, item });
      }
    } else {
      action.action(item);
      this.actionClick.emit({ action, item });
    }
  }

  onClearFilters(): void {
    this.filtersChange.emit([]);
    this.emitStateChange();
  }

  onAddItem(): void {
    this.addItemClick.emit();
  }

  // Helper methods
  private updateConfig(): void {
    this.config = {
      selectable: false,
      multiSelect: true,
      sortable: true,
      filterable: true,
      paginated: true,
      striped: false,
      bordered: false,
      hover: true,
      dense: false,
      loading: this.loading,
      pageSize: this.pageSize,
      pageSizeOptions: [5, 10, 25, 50, 100],
      showPageSizeSelector: true,
      showPaginationInfo: true,
      actionColumn: this.actions.length > 0,
      ...this.config
    };
  }

  private updateSelection(): void {
    this.updateSelectionState(this.selectedItems);
  }

  private updateSelectionState(selected: T[]): void {
    this.selection = {
      selected,
      isAllSelected: selected.length > 0 && selected.length === this.displayData.length,
      isIndeterminate: selected.length > 0 && selected.length < this.displayData.length
    };
  }

  private emitStateChange(): void {
    const state: DataTableState = {
      sort: this.currentSort,
      filters: this.currentFilters,
      pageNumber: this.currentPage,
      pageSize: this.currentPageSize,
      selectedItems: this.selection.selected,
      expandedItems: [] // TODO: Implement expandable rows
    };
    this.stateChange.emit(state);
  }

  // Utility methods
  isItemSelected(item: T): boolean {
    return this.selectedItems.includes(item);
  }

  getCellValue(item: T, column: DataTableColumn<T>): any {
    if (column.getValue) {
      return column.getValue(item);
    }
    return (item as any)[column.key];
  }

  formatCellValue(value: any, column: DataTableColumn<T>): string {
    if (value === null || value === undefined) {
      return '';
    }

    switch (column.type) {
      case 'date':
        return new Date(value).toLocaleDateString();
      case 'currency':
        return new Intl.NumberFormat('en-US', { 
          style: 'currency', 
          currency: 'USD' 
        }).format(value);
      case 'number':
        return new Intl.NumberFormat().format(value);
      case 'boolean':
        return value ? 'Yes' : 'No';
      default:
        return String(value);
    }
  }

  getVisibleActions(item: T): DataTableAction<T>[] {
    return this.actions.filter(action => 
      !action.visible || action.visible(item)
    );
  }

  isActionDisabled(action: DataTableAction<T>, item: T): boolean {
    return action.disabled ? action.disabled(item) : false;
  }

  getContainerClass(): string {
    const classes = ['data-table-wrapper'];
    if (this.config.dense) classes.push('dense');
    return classes.join(' ');
  }

  getTableClass(): string {
    const classes = ['table'];
    if (this.config.striped) classes.push('striped');
    if (this.config.bordered) classes.push('bordered');
    if (this.config.hover) classes.push('hover');
    if (this.config.dense) classes.push('dense');
    return classes.join(' ');
  }

  getRowClass(item: T, index: number): string {
    const classes = ['data-row'];
    if (this.isItemSelected(item)) classes.push('selected');
    if (this.config.striped) classes.push('striped');
    return classes.join(' ');
  }

  getCellClass(column: DataTableColumn<T>, item: T): string {
    const classes = ['data-cell'];
    if (column.cellCssClass) classes.push(column.cellCssClass);
    if (column.cssClass) classes.push(column.cssClass);
    return classes.join(' ');
  }

  getSkeletonColumnWidths(): string[] {
    return this.columns.map(col => col.width || 'auto');
  }

  getSkeletonColumnTypes(): string[] {
    return this.columns.map(col => col.type || 'text');
  }

  getEmptyMessage(): string {
    if (this.hasActiveFilters) {
      return 'No results found for the current filters';
    }
    return this.config.emptyMessage || 'No data available';
  }

  getEmptyTitle(): string {
    if (this.hasActiveFilters) {
      return 'No Results';
    }
    return 'No Data';
  }

  getEmptyDescription(): string {
    if (this.hasActiveFilters) {
      return 'Try adjusting your filters to see more results.';
    }
    return 'There are no items to display at this time.';
  }

  getEmptyType(): 'no-data' | 'no-results' | 'error' | 'loading' {
    if (this.error) return 'error';
    if (this.hasActiveFilters) return 'no-results';
    return 'no-data';
  }
}
