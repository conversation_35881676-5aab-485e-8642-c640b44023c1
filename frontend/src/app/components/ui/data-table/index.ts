// Main Data Table Component
export { DataTableComponent } from './data-table.component';

// Sub-components
export { TableSkeletonComponent } from '../table-skeleton/table-skeleton.component';
export { TableHeaderComponent } from '../table-header/table-header.component';
export { TableFiltersComponent } from '../table-filters/table-filters.component';
export { TablePaginationComponent } from '../table-pagination/table-pagination.component';
export { TableEmptyComponent } from '../table-empty/table-empty.component';

// Example Component
export { DataTableExampleComponent } from './data-table-example.component';

// Re-export models for convenience
export * from '../../../models/data-table.models';
