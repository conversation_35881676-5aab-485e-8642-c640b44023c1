# Angular Data Table Library

A comprehensive, accessible, and highly customizable data table library for Angular applications with TypeScript generics support.

## Features

- 🔧 **TypeScript Generics** - Full type safety for your data
- 📱 **Responsive Design** - Mobile-first approach with adaptive layouts
- ♿ **Accessibility** - WCAG compliant with proper ARIA attributes
- 🎨 **Themeable** - CSS variables for easy customization
- 🔍 **Filtering** - Column-specific filters with multiple operators
- 📊 **Sorting** - Multi-column sorting with visual indicators
- 📄 **Pagination** - Configurable page sizes and navigation
- ✅ **Selection** - Single and multi-row selection
- 🎭 **Actions** - Conditional row actions with permissions
- 💀 **Loading States** - Skeleton UI for better UX
- 🚫 **Empty States** - Customizable empty state messaging
- 🎯 **OnPush Strategy** - Optimized change detection

## Components

### Main Components
- `DataTableComponent<T>` - Main table component with generics
- `TableSkeletonComponent` - Loading state visualization
- `TableHeaderComponent` - Column headers with sorting
- `TableFiltersComponent` - Column-specific filtering
- `TablePaginationComponent` - Page navigation and size selection
- `TableEmptyComponent` - Customizable empty state messaging

## Basic Usage

```typescript
import { Component } from '@angular/core';
import { DataTableComponent } from './components/ui/data-table/data-table.component';
import { DataTableColumn, DataTableConfig, DataTableAction } from './models';

interface User {
  id: number;
  name: string;
  email: string;
  age: number;
  active: boolean;
  createdAt: Date;
}

@Component({
  selector: 'app-users',
  standalone: true,
  imports: [DataTableComponent],
  template: `
    <app-data-table
      [data]="users"
      [columns]="columns"
      [config]="config"
      [actions]="actions"
      [loading]="loading"
      (sortChange)="onSort($event)"
      (filtersChange)="onFiltersChange($event)"
      (pageChange)="onPageChange($event)"
      (selectionChange)="onSelectionChange($event)"
      (actionClick)="onActionClick($event)">
    </app-data-table>
  `
})
export class UsersComponent {
  users: User[] = [
    {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      age: 30,
      active: true,
      createdAt: new Date('2023-01-01')
    }
    // ... more users
  ];

  columns: DataTableColumn<User>[] = [
    {
      key: 'id',
      label: 'ID',
      type: 'number',
      sortable: true,
      width: '80px'
    },
    {
      key: 'name',
      label: 'Name',
      type: 'text',
      sortable: true,
      filterable: true
    },
    {
      key: 'email',
      label: 'Email',
      type: 'text',
      sortable: true,
      filterable: true
    },
    {
      key: 'age',
      label: 'Age',
      type: 'number',
      sortable: true,
      filterable: true,
      align: 'right'
    },
    {
      key: 'active',
      label: 'Active',
      type: 'boolean',
      sortable: true,
      filterable: true
    },
    {
      key: 'createdAt',
      label: 'Created',
      type: 'date',
      sortable: true
    }
  ];

  config: DataTableConfig = {
    selectable: true,
    multiSelect: true,
    sortable: true,
    filterable: true,
    paginated: true,
    striped: true,
    hover: true,
    pageSize: 10,
    pageSizeOptions: [5, 10, 25, 50],
    actionColumn: true
  };

  actions: DataTableAction<User>[] = [
    {
      label: 'Edit',
      icon: '✏️',
      action: (user) => this.editUser(user),
      visible: () => true,
      disabled: () => false
    },
    {
      label: 'Delete',
      icon: '🗑️',
      action: (user) => this.deleteUser(user),
      visible: (user) => user.active,
      disabled: () => false,
      confirmMessage: 'Are you sure you want to delete this user?'
    }
  ];

  loading = false;

  onSort(sort: DataTableSort) {
    console.log('Sort changed:', sort);
    // Implement sorting logic
  }

  onFiltersChange(filters: DataTableFilter[]) {
    console.log('Filters changed:', filters);
    // Implement filtering logic
  }

  onPageChange(page: number) {
    console.log('Page changed:', page);
    // Implement pagination logic
  }

  onSelectionChange(selection: DataTableSelection<User>) {
    console.log('Selection changed:', selection);
    // Handle selection changes
  }

  onActionClick(event: { action: DataTableAction<User>; item: User }) {
    console.log('Action clicked:', event);
    // Handle action clicks
  }

  editUser(user: User) {
    // Implement edit logic
  }

  deleteUser(user: User) {
    // Implement delete logic
  }
}
```

## Advanced Usage

### Custom Cell Templates

```typescript
@Component({
  template: `
    <app-data-table [data]="data" [columns]="columns">
      <ng-template #statusTemplate let-item let-column="column" let-value="value">
        <span [class]="'status-' + value.toLowerCase()">
          {{ value }}
        </span>
      </ng-template>
    </app-data-table>
  `
})
export class AdvancedTableComponent {
  @ViewChild('statusTemplate') statusTemplate!: TemplateRef<any>;

  ngAfterViewInit() {
    this.columns = [
      // ... other columns
      {
        key: 'status',
        label: 'Status',
        customTemplate: this.statusTemplate
      }
    ];
  }
}
```

### Server-Side Data with Pagination

```typescript
@Component({
  template: `
    <app-data-table
      [pagedResult]="pagedResult"
      [columns]="columns"
      [config]="config"
      [loading]="loading"
      (sortChange)="loadData($event)"
      (filtersChange)="loadData(undefined, $event)"
      (pageChange)="loadData(undefined, undefined, $event)">
    </app-data-table>
  `
})
export class ServerSideTableComponent {
  pagedResult?: PagedResult<User>;
  loading = false;

  async loadData(
    sort?: DataTableSort,
    filters?: DataTableFilter[],
    page?: number
  ) {
    this.loading = true;
    
    const params: QueryParams = {
      pageNumber: page || 1,
      pageSize: 10,
      sortBy: sort?.column,
      sortDirection: sort?.direction,
      filters: filters?.reduce((acc, filter) => {
        acc[filter.column] = filter.value;
        return acc;
      }, {} as Record<string, any>)
    };

    try {
      this.pagedResult = await this.userService.getUsers(params);
    } finally {
      this.loading = false;
    }
  }
}
```

## Configuration Options

### DataTableConfig

```typescript
interface DataTableConfig {
  selectable?: boolean;           // Enable row selection
  multiSelect?: boolean;          // Allow multiple row selection
  sortable?: boolean;             // Enable column sorting
  filterable?: boolean;           // Enable column filtering
  paginated?: boolean;            // Enable pagination
  resizable?: boolean;            // Enable column resizing
  striped?: boolean;              // Alternate row colors
  bordered?: boolean;             // Add borders
  hover?: boolean;                // Hover effects
  dense?: boolean;                // Compact row height
  loading?: boolean;              // Show loading state
  emptyMessage?: string;          // Custom empty message
  loadingMessage?: string;        // Custom loading message
  pageSize?: number;              // Default page size
  pageSizeOptions?: number[];     // Available page sizes
  showPageSizeSelector?: boolean; // Show page size dropdown
  showPaginationInfo?: boolean;   // Show pagination info
  maxHeight?: string;             // Table max height
  stickyHeader?: boolean;         // Sticky header
  actionColumn?: boolean;         // Show actions column
  actionColumnWidth?: string;     // Actions column width
  actionColumnLabel?: string;     // Actions column label
  actionColumnAlign?: 'left' | 'center' | 'right';
}
```

### DataTableColumn

```typescript
interface DataTableColumn<T = any> {
  key: string;                    // Property key
  label: string;                  // Display label
  sortable?: boolean;             // Enable sorting
  filterable?: boolean;           // Enable filtering
  width?: string;                 // Column width
  minWidth?: string;              // Minimum width
  maxWidth?: string;              // Maximum width
  align?: 'left' | 'center' | 'right'; // Text alignment
  type?: 'text' | 'number' | 'date' | 'boolean' | 'currency' | 'custom';
  format?: string;                // Format string
  customTemplate?: TemplateRef<any>; // Custom cell template
  headerTemplate?: TemplateRef<any>; // Custom header template
  filterTemplate?: TemplateRef<any>; // Custom filter template
  getValue?: (item: T) => any;    // Custom value getter
  sortValue?: (item: T) => any;   // Custom sort value
  cssClass?: string;              // Cell CSS class
  headerCssClass?: string;        // Header CSS class
  cellCssClass?: string;          // Cell CSS class
  visible?: boolean;              // Column visibility
  resizable?: boolean;            // Column resizable
  sticky?: boolean;               // Sticky column
}
```

## Styling and Theming

The data table uses CSS variables for theming. You can customize the appearance by overriding these variables:

```css
:root {
  /* Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --border-primary: #e5e7eb;
  --border-secondary: #f3f4f6;
  --interactive-primary: #3b82f6;
  --status-success: #10b981;
  --status-warning: #f59e0b;
  --status-error: #ef4444;

  /* Spacing */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-6: 24px;
  --space-8: 32px;

  /* Typography */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --font-medium: 500;
  --font-semibold: 600;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;

  /* Transitions */
  --transition-fast: 150ms;
}
```

## Accessibility

The data table is built with accessibility in mind:

- **ARIA Labels**: Proper labeling for screen readers
- **Keyboard Navigation**: Full keyboard support
- **Focus Management**: Visible focus indicators
- **Screen Reader Support**: Semantic HTML structure
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects user motion preferences

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance

- **OnPush Change Detection**: Optimized for performance
- **Virtual Scrolling**: Support for large datasets (optional)
- **Lazy Loading**: Efficient data loading
- **Track By Functions**: Optimized list rendering
- **Debounced Filtering**: Reduced API calls

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
