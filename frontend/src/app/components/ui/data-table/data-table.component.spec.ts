import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';
import { DataTableComponent } from './data-table.component';
import { DataTableColumn, DataTableConfig, DataTableAction } from '../../../models';

interface TestItem {
  id: number;
  name: string;
  email: string;
  age: number;
  active: boolean;
  createdAt: Date;
}

describe('DataTableComponent', () => {
  let component: DataTableComponent<TestItem>;
  let fixture: ComponentFixture<DataTableComponent<TestItem>>;
  let compiled: HTMLElement;

  const mockData: TestItem[] = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', age: 30, active: true, createdAt: new Date('2023-01-01') },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', age: 25, active: false, createdAt: new Date('2023-01-02') },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', age: 35, active: true, createdAt: new Date('2023-01-03') }
  ];

  const mockColumns: DataTableColumn<TestItem>[] = [
    { key: 'id', label: 'ID', type: 'number', sortable: true, width: '80px' },
    { key: 'name', label: 'Name', type: 'text', sortable: true, filterable: true },
    { key: 'email', label: 'Email', type: 'text', sortable: true, filterable: true },
    { key: 'age', label: 'Age', type: 'number', sortable: true, filterable: true },
    { key: 'active', label: 'Active', type: 'boolean', sortable: true, filterable: true },
    { key: 'createdAt', label: 'Created', type: 'date', sortable: true }
  ];

  const mockActions: DataTableAction<TestItem>[] = [
    {
      label: 'Edit',
      icon: '✏️',
      action: jasmine.createSpy('editAction'),
      visible: () => true,
      disabled: () => false
    },
    {
      label: 'Delete',
      icon: '🗑️',
      action: jasmine.createSpy('deleteAction'),
      visible: (item) => item.active,
      disabled: () => false,
      confirmMessage: 'Are you sure?'
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DataTableComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(DataTableComponent<TestItem>);
    component = fixture.componentInstance;
    compiled = fixture.nativeElement;
  });

  describe('Component Initialization', () => {
    it('ShouldCreateComponent', () => {
      expect(component).toBeTruthy();
    });

    it('ShouldInitializeWithDefaultConfig', () => {
      component.ngOnInit();
      
      expect(component.config.selectable).toBe(false);
      expect(component.config.sortable).toBe(true);
      expect(component.config.filterable).toBe(true);
      expect(component.config.paginated).toBe(true);
    });

    it('ShouldMergeCustomConfig', () => {
      const customConfig: DataTableConfig = {
        selectable: true,
        striped: true,
        dense: true
      };
      
      component.config = customConfig;
      component.ngOnInit();
      
      expect(component.config.selectable).toBe(true);
      expect(component.config.striped).toBe(true);
      expect(component.config.dense).toBe(true);
      expect(component.config.sortable).toBe(true); // Should keep default
    });
  });

  describe('Data Display', () => {
    beforeEach(() => {
      component.data = mockData;
      component.columns = mockColumns;
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('ShouldDisplayTableWithCorrectData', () => {
      const tableRows = compiled.querySelectorAll('.table-row');
      expect(tableRows.length).toBe(mockData.length);
    });

    it('ShouldDisplayCorrectColumnHeaders', () => {
      const headerCells = compiled.querySelectorAll('th.header-cell:not(.selection-cell):not(.actions-cell)');
      expect(headerCells.length).toBe(mockColumns.length);
      
      mockColumns.forEach((column, index) => {
        expect(headerCells[index].textContent?.trim()).toContain(column.label);
      });
    });

    it('ShouldDisplayCellValuesCorrectly', () => {
      const firstRowCells = compiled.querySelectorAll('.table-row:first-child .table-cell:not(.selection-cell):not(.actions-cell)');
      
      expect(firstRowCells[0].textContent?.trim()).toBe('1'); // ID
      expect(firstRowCells[1].textContent?.trim()).toBe('John Doe'); // Name
      expect(firstRowCells[2].textContent?.trim()).toBe('<EMAIL>'); // Email
    });

    it('ShouldFormatDifferentDataTypes', () => {
      const firstRowCells = compiled.querySelectorAll('.table-row:first-child .table-cell:not(.selection-cell):not(.actions-cell)');
      
      // Boolean should be formatted as Yes/No
      expect(firstRowCells[4].textContent?.trim()).toBe('Yes');
      
      // Date should be formatted
      const dateCell = firstRowCells[5];
      expect(dateCell.textContent?.trim()).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
    });
  });

  describe('Selection Functionality', () => {
    beforeEach(() => {
      component.data = mockData;
      component.columns = mockColumns;
      component.config = { selectable: true };
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('ShouldShowSelectionColumn', () => {
      const selectionCells = compiled.querySelectorAll('.selection-cell');
      expect(selectionCells.length).toBe(mockData.length + 1); // +1 for header
    });

    it('ShouldSelectIndividualItems', () => {
      spyOn(component.selectionChange, 'emit');
      
      const firstCheckbox = compiled.querySelector('.table-row:first-child .selection-checkbox') as HTMLInputElement;
      firstCheckbox.click();
      
      expect(component.selection.selected.length).toBe(1);
      expect(component.selection.selected[0]).toBe(mockData[0]);
      expect(component.selectionChange.emit).toHaveBeenCalled();
    });

    it('ShouldSelectAllItems', () => {
      spyOn(component.selectionChange, 'emit');
      
      const selectAllCheckbox = compiled.querySelector('th.selection-cell .selection-checkbox') as HTMLInputElement;
      selectAllCheckbox.click();
      
      expect(component.selection.selected.length).toBe(mockData.length);
      expect(component.selection.isAllSelected).toBe(true);
      expect(component.selectionChange.emit).toHaveBeenCalled();
    });

    it('ShouldShowIndeterminateState', () => {
      // Select first item
      component.onItemSelect(mockData[0], { target: { checked: true } } as any);
      fixture.detectChanges();
      
      expect(component.selection.isIndeterminate).toBe(true);
      expect(component.selection.isAllSelected).toBe(false);
    });
  });

  describe('Sorting Functionality', () => {
    beforeEach(() => {
      component.data = mockData;
      component.columns = mockColumns;
      component.config = { sortable: true };
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('ShouldShowSortableHeaders', () => {
      const sortButtons = compiled.querySelectorAll('.sort-button');
      const sortableColumns = mockColumns.filter(col => col.sortable);
      expect(sortButtons.length).toBe(sortableColumns.length);
    });

    it('ShouldEmitSortEvent', () => {
      spyOn(component.sortChange, 'emit');
      
      const firstSortButton = compiled.querySelector('.sort-button') as HTMLButtonElement;
      firstSortButton.click();
      
      expect(component.sortChange.emit).toHaveBeenCalledWith({
        column: 'id',
        direction: 'asc'
      });
    });

    it('ShouldToggleSortDirection', () => {
      spyOn(component.sortChange, 'emit');
      component.currentSort = { column: 'id', direction: 'asc' };
      fixture.detectChanges();
      
      const firstSortButton = compiled.querySelector('.sort-button') as HTMLButtonElement;
      firstSortButton.click();
      
      expect(component.sortChange.emit).toHaveBeenCalledWith({
        column: 'id',
        direction: 'desc'
      });
    });
  });

  describe('Actions Functionality', () => {
    beforeEach(() => {
      component.data = mockData;
      component.columns = mockColumns;
      component.actions = mockActions;
      component.config = { actionColumn: true };
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('ShouldShowActionsColumn', () => {
      const actionsCells = compiled.querySelectorAll('.actions-cell');
      expect(actionsCells.length).toBe(mockData.length + 1); // +1 for header
    });

    it('ShouldDisplayActionButtons', () => {
      const firstRowActions = compiled.querySelectorAll('.table-row:first-child .action-btn');
      expect(firstRowActions.length).toBe(2); // Edit and Delete actions
    });

    it('ShouldExecuteActionOnClick', () => {
      const editButton = compiled.querySelector('.table-row:first-child .action-btn') as HTMLButtonElement;
      editButton.click();
      
      expect(mockActions[0].action).toHaveBeenCalledWith(mockData[0]);
    });

    it('ShouldHideActionsBasedOnVisibility', () => {
      // Second item has active: false, so Delete action should be hidden
      const secondRowActions = compiled.querySelectorAll('.table-row:nth-child(2) .action-btn');
      expect(secondRowActions.length).toBe(1); // Only Edit action
    });
  });

  describe('Empty State', () => {
    beforeEach(() => {
      component.data = [];
      component.columns = mockColumns;
      component.config = { loading: false };
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('ShouldShowEmptyState', () => {
      const emptyComponent = compiled.querySelector('app-table-empty');
      expect(emptyComponent).toBeTruthy();
    });

    it('ShouldNotShowTableWhenEmpty', () => {
      const table = compiled.querySelector('.data-table');
      expect(table).toBeFalsy();
    });
  });

  describe('Loading State', () => {
    beforeEach(() => {
      component.data = mockData;
      component.columns = mockColumns;
      component.config = { loading: true };
      component.showSkeleton = true;
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('ShouldShowSkeletonWhenLoading', () => {
      const skeleton = compiled.querySelector('app-table-skeleton');
      expect(skeleton).toBeTruthy();
    });

    it('ShouldHideTableWhenLoadingWithSkeleton', () => {
      const table = compiled.querySelector('.data-table');
      expect(table).toBeFalsy();
    });
  });

  describe('Pagination', () => {
    beforeEach(() => {
      component.data = mockData;
      component.columns = mockColumns;
      component.config = { paginated: true };
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('ShouldShowPaginationComponent', () => {
      const pagination = compiled.querySelector('app-table-pagination');
      expect(pagination).toBeTruthy();
    });

    it('ShouldEmitPageChangeEvent', () => {
      spyOn(component.pageChange, 'emit');
      
      component.onPageChange(2);
      
      expect(component.pageChange.emit).toHaveBeenCalledWith(2);
    });

    it('ShouldEmitPageSizeChangeEvent', () => {
      spyOn(component.pageSizeChange, 'emit');
      
      component.onPageSizeChange(25);
      
      expect(component.pageSizeChange.emit).toHaveBeenCalledWith(25);
    });
  });

  describe('Filtering', () => {
    beforeEach(() => {
      component.data = mockData;
      component.columns = mockColumns;
      component.config = { filterable: true };
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('ShouldShowFiltersComponent', () => {
      const filters = compiled.querySelector('app-table-filters');
      expect(filters).toBeTruthy();
    });

    it('ShouldEmitFiltersChangeEvent', () => {
      spyOn(component.filtersChange, 'emit');
      
      const newFilters = [{ column: 'name', value: 'John', operator: 'contains' as const }];
      component.onFiltersChange(newFilters);
      
      expect(component.filtersChange.emit).toHaveBeenCalledWith(newFilters);
    });
  });

  describe('Utility Methods', () => {
    beforeEach(() => {
      component.data = mockData;
      component.columns = mockColumns;
      component.ngOnInit();
    });

    it('ShouldGetCellValueCorrectly', () => {
      const nameColumn = mockColumns.find(col => col.key === 'name')!;
      const value = component.getCellValue(mockData[0], nameColumn);
      expect(value).toBe('John Doe');
    });

    it('ShouldFormatCellValueCorrectly', () => {
      const ageColumn = mockColumns.find(col => col.key === 'age')!;
      const formatted = component.formatCellValue(30, ageColumn);
      expect(formatted).toBe('30');
    });

    it('ShouldFormatBooleanValues', () => {
      const activeColumn = mockColumns.find(col => col.key === 'active')!;
      expect(component.formatCellValue(true, activeColumn)).toBe('Yes');
      expect(component.formatCellValue(false, activeColumn)).toBe('No');
    });

    it('ShouldFormatDateValues', () => {
      const dateColumn = mockColumns.find(col => col.key === 'createdAt')!;
      const date = new Date('2023-01-01');
      const formatted = component.formatCellValue(date, dateColumn);
      expect(formatted).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
    });

    it('ShouldCheckItemSelection', () => {
      component.selectedItems = [mockData[0]];
      component.updateSelection();
      
      expect(component.isItemSelected(mockData[0])).toBe(true);
      expect(component.isItemSelected(mockData[1])).toBe(false);
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      component.data = mockData;
      component.columns = mockColumns;
      component.config = { selectable: true };
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('ShouldHaveProperAriaLabels', () => {
      const table = compiled.querySelector('.data-table');
      expect(table?.getAttribute('aria-label')).toBe('Data table');
      expect(table?.getAttribute('role')).toBe('table');
    });

    it('ShouldHaveProperRowAttributes', () => {
      const firstRow = compiled.querySelector('.table-row');
      expect(firstRow?.getAttribute('role')).toBe('row');
      expect(firstRow?.getAttribute('aria-rowindex')).toBe('2'); // 1-based, +1 for header
    });

    it('ShouldHaveProperCellAttributes', () => {
      const firstCell = compiled.querySelector('.table-cell');
      expect(firstCell?.getAttribute('role')).toBe('gridcell');
    });

    it('ShouldHaveProperCheckboxLabels', () => {
      const selectAllCheckbox = compiled.querySelector('th.selection-cell .selection-checkbox');
      expect(selectAllCheckbox?.getAttribute('aria-label')).toContain('Select all');
      
      const firstRowCheckbox = compiled.querySelector('.table-row:first-child .selection-checkbox');
      expect(firstRowCheckbox?.getAttribute('aria-label')).toContain('Select row 1');
    });
  });
});
