import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { TablePaginationComponent } from './table-pagination.component';

describe('TablePaginationComponent', () => {
  let component: TablePaginationComponent;
  let fixture: ComponentFixture<TablePaginationComponent>;
  let compiled: HTMLElement;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TablePaginationComponent, FormsModule]
    }).compileComponents();

    fixture = TestBed.createComponent(TablePaginationComponent);
    component = fixture.componentInstance;
    compiled = fixture.nativeElement;
  });

  describe('Component Initialization', () => {
    it('ShouldCreateComponent', () => {
      expect(component).toBeTruthy();
    });

    it('ShouldHaveDefaultValues', () => {
      expect(component.currentPage).toBe(1);
      expect(component.totalPages).toBe(1);
      expect(component.totalItems).toBe(0);
      expect(component.pageSize).toBe(10);
      expect(component.pageSizeOptions).toEqual([5, 10, 25, 50, 100]);
      expect(component.showInfo).toBe(true);
      expect(component.showPageSizeSelector).toBe(true);
      expect(component.maxVisiblePages).toBe(7);
    });
  });

  describe('Pagination Display', () => {
    beforeEach(() => {
      component.totalItems = 100;
      component.pageSize = 10;
      component.totalPages = 10;
      component.currentPage = 1;
      fixture.detectChanges();
    });

    it('ShouldShowPaginationWhenTotalItemsGreaterThanZero', () => {
      const pagination = compiled.querySelector('.table-pagination');
      expect(pagination).toBeTruthy();
    });

    it('ShouldNotShowPaginationWhenTotalItemsIsZero', () => {
      component.totalItems = 0;
      fixture.detectChanges();
      
      const pagination = compiled.querySelector('.table-pagination');
      expect(pagination).toBeFalsy();
    });

    it('ShouldShowPaginationInfo', () => {
      const info = compiled.querySelector('.pagination-info');
      expect(info).toBeTruthy();
      expect(info?.textContent?.trim()).toContain('Showing 1 to 10 of 100 entries');
    });

    it('ShouldShowPageSizeSelector', () => {
      const selector = compiled.querySelector('.page-size-selector');
      expect(selector).toBeTruthy();
    });

    it('ShouldShowPaginationControlsWhenMultiplePages', () => {
      const controls = compiled.querySelector('.pagination-controls');
      expect(controls).toBeTruthy();
    });

    it('ShouldNotShowPaginationControlsWhenSinglePage', () => {
      component.totalPages = 1;
      fixture.detectChanges();
      
      const controls = compiled.querySelector('.pagination-controls');
      expect(controls).toBeFalsy();
    });
  });

  describe('Pagination Info Calculation', () => {
    it('ShouldCalculateStartItemCorrectly', () => {
      component.currentPage = 3;
      component.pageSize = 10;
      component.totalItems = 100;
      
      expect(component.startItem).toBe(21);
    });

    it('ShouldCalculateEndItemCorrectly', () => {
      component.currentPage = 3;
      component.pageSize = 10;
      component.totalItems = 100;
      
      expect(component.endItem).toBe(30);
    });

    it('ShouldCalculateEndItemForLastPage', () => {
      component.currentPage = 10;
      component.pageSize = 10;
      component.totalItems = 95;
      
      expect(component.endItem).toBe(95);
    });

    it('ShouldReturnZeroForStartItemWhenNoItems', () => {
      component.totalItems = 0;
      
      expect(component.startItem).toBe(0);
    });
  });

  describe('Visible Pages Calculation', () => {
    beforeEach(() => {
      component.maxVisiblePages = 7;
    });

    it('ShouldShowAllPagesWhenTotalPagesLessThanMax', () => {
      component.totalPages = 5;
      component.currentPage = 3;
      
      const visiblePages = component.visiblePages;
      expect(visiblePages).toEqual([1, 2, 3, 4, 5]);
    });

    it('ShouldShowEllipsisWhenTotalPagesGreaterThanMax', () => {
      component.totalPages = 20;
      component.currentPage = 10;
      
      const visiblePages = component.visiblePages;
      expect(visiblePages).toContain(-1); // Ellipsis
      expect(visiblePages).toContain(1);  // First page
      expect(visiblePages).toContain(20); // Last page
    });

    it('ShouldShowCorrectPagesAroundCurrentPage', () => {
      component.totalPages = 20;
      component.currentPage = 10;
      
      const visiblePages = component.visiblePages;
      expect(visiblePages).toContain(10); // Current page
      expect(visiblePages).toContain(9);  // Previous page
      expect(visiblePages).toContain(11); // Next page
    });

    it('ShouldHandleCurrentPageAtBeginning', () => {
      component.totalPages = 20;
      component.currentPage = 1;
      
      const visiblePages = component.visiblePages;
      expect(visiblePages[0]).toBe(1);
      expect(visiblePages).toContain(2);
      expect(visiblePages).toContain(3);
    });

    it('ShouldHandleCurrentPageAtEnd', () => {
      component.totalPages = 20;
      component.currentPage = 20;
      
      const visiblePages = component.visiblePages;
      expect(visiblePages).toContain(18);
      expect(visiblePages).toContain(19);
      expect(visiblePages[visiblePages.length - 1]).toBe(20);
    });
  });

  describe('Navigation Buttons', () => {
    beforeEach(() => {
      component.totalItems = 100;
      component.pageSize = 10;
      component.totalPages = 10;
      component.currentPage = 5;
      fixture.detectChanges();
    });

    it('ShouldShowAllNavigationButtons', () => {
      const firstBtn = compiled.querySelector('.pagination-btn-first');
      const prevBtn = compiled.querySelector('.pagination-btn-prev');
      const nextBtn = compiled.querySelector('.pagination-btn-next');
      const lastBtn = compiled.querySelector('.pagination-btn-last');
      
      expect(firstBtn).toBeTruthy();
      expect(prevBtn).toBeTruthy();
      expect(nextBtn).toBeTruthy();
      expect(lastBtn).toBeTruthy();
    });

    it('ShouldDisableFirstAndPrevButtonsOnFirstPage', () => {
      component.currentPage = 1;
      fixture.detectChanges();
      
      const firstBtn = compiled.querySelector('.pagination-btn-first') as HTMLButtonElement;
      const prevBtn = compiled.querySelector('.pagination-btn-prev') as HTMLButtonElement;
      
      expect(firstBtn.disabled).toBe(true);
      expect(prevBtn.disabled).toBe(true);
    });

    it('ShouldDisableNextAndLastButtonsOnLastPage', () => {
      component.currentPage = 10;
      fixture.detectChanges();
      
      const nextBtn = compiled.querySelector('.pagination-btn-next') as HTMLButtonElement;
      const lastBtn = compiled.querySelector('.pagination-btn-last') as HTMLButtonElement;
      
      expect(nextBtn.disabled).toBe(true);
      expect(lastBtn.disabled).toBe(true);
    });

    it('ShouldEmitPageChangeOnFirstButtonClick', () => {
      spyOn(component.pageChange, 'emit');
      
      const firstBtn = compiled.querySelector('.pagination-btn-first') as HTMLButtonElement;
      firstBtn.click();
      
      expect(component.pageChange.emit).toHaveBeenCalledWith(1);
    });

    it('ShouldEmitPageChangeOnPrevButtonClick', () => {
      spyOn(component.pageChange, 'emit');
      
      const prevBtn = compiled.querySelector('.pagination-btn-prev') as HTMLButtonElement;
      prevBtn.click();
      
      expect(component.pageChange.emit).toHaveBeenCalledWith(4);
    });

    it('ShouldEmitPageChangeOnNextButtonClick', () => {
      spyOn(component.pageChange, 'emit');
      
      const nextBtn = compiled.querySelector('.pagination-btn-next') as HTMLButtonElement;
      nextBtn.click();
      
      expect(component.pageChange.emit).toHaveBeenCalledWith(6);
    });

    it('ShouldEmitPageChangeOnLastButtonClick', () => {
      spyOn(component.pageChange, 'emit');
      
      const lastBtn = compiled.querySelector('.pagination-btn-last') as HTMLButtonElement;
      lastBtn.click();
      
      expect(component.pageChange.emit).toHaveBeenCalledWith(10);
    });
  });

  describe('Page Number Buttons', () => {
    beforeEach(() => {
      component.totalItems = 100;
      component.pageSize = 10;
      component.totalPages = 10;
      component.currentPage = 5;
      fixture.detectChanges();
    });

    it('ShouldShowPageNumberButtons', () => {
      const pageButtons = compiled.querySelectorAll('.page-number');
      expect(pageButtons.length).toBeGreaterThan(0);
    });

    it('ShouldHighlightCurrentPage', () => {
      const activeButton = compiled.querySelector('.page-number.active');
      expect(activeButton).toBeTruthy();
      expect(activeButton?.textContent?.trim()).toBe('5');
    });

    it('ShouldEmitPageChangeOnPageButtonClick', () => {
      spyOn(component.pageChange, 'emit');
      
      const pageButton = compiled.querySelector('.page-number:not(.active)') as HTMLButtonElement;
      if (pageButton && pageButton.textContent?.trim() !== '...') {
        pageButton.click();
        expect(component.pageChange.emit).toHaveBeenCalled();
      }
    });

    it('ShouldNotEmitPageChangeOnEllipsisClick', () => {
      spyOn(component.pageChange, 'emit');
      
      const ellipsisButton = compiled.querySelector('.page-number.ellipsis') as HTMLButtonElement;
      if (ellipsisButton) {
        ellipsisButton.click();
        expect(component.pageChange.emit).not.toHaveBeenCalled();
      }
    });
  });

  describe('Page Size Selector', () => {
    beforeEach(() => {
      component.totalItems = 100;
      component.pageSize = 10;
      component.pageSizeOptions = [5, 10, 25, 50];
      fixture.detectChanges();
    });

    it('ShouldShowPageSizeSelector', () => {
      const selector = compiled.querySelector('.page-size-select');
      expect(selector).toBeTruthy();
    });

    it('ShouldShowAllPageSizeOptions', () => {
      const options = compiled.querySelectorAll('.page-size-select option');
      expect(options.length).toBe(4);
    });

    it('ShouldSelectCurrentPageSize', () => {
      const select = compiled.querySelector('.page-size-select') as HTMLSelectElement;
      expect(select.value).toBe('10');
    });

    it('ShouldEmitPageSizeChangeOnSelection', () => {
      spyOn(component.pageSizeChange, 'emit');
      
      const select = compiled.querySelector('.page-size-select') as HTMLSelectElement;
      select.value = '25';
      select.dispatchEvent(new Event('change'));
      
      expect(component.pageSizeChange.emit).toHaveBeenCalledWith(25);
    });

    it('ShouldNotShowPageSizeSelectorWhenDisabled', () => {
      component.showPageSizeSelector = false;
      fixture.detectChanges();
      
      const selector = compiled.querySelector('.page-size-selector');
      expect(selector).toBeFalsy();
    });

    it('ShouldNotShowPageSizeSelectorWhenOnlyOneOption', () => {
      component.pageSizeOptions = [10];
      fixture.detectChanges();
      
      const selector = compiled.querySelector('.page-size-selector');
      expect(selector).toBeFalsy();
    });
  });

  describe('Mobile Pagination', () => {
    beforeEach(() => {
      component.totalItems = 100;
      component.pageSize = 10;
      component.totalPages = 10;
      component.currentPage = 5;
      fixture.detectChanges();
    });

    it('ShouldShowMobilePagination', () => {
      const mobilePagination = compiled.querySelector('.mobile-pagination');
      expect(mobilePagination).toBeTruthy();
    });

    it('ShouldShowMobilePageInfo', () => {
      const pageInfo = compiled.querySelector('.mobile-page-info');
      expect(pageInfo?.textContent?.trim()).toBe('Page 5 of 10');
    });

    it('ShouldShowMobilePrevButton', () => {
      const prevBtn = compiled.querySelector('.mobile-btn-prev');
      expect(prevBtn).toBeTruthy();
    });

    it('ShouldShowMobileNextButton', () => {
      const nextBtn = compiled.querySelector('.mobile-btn-next');
      expect(nextBtn).toBeTruthy();
    });

    it('ShouldDisableMobilePrevButtonOnFirstPage', () => {
      component.currentPage = 1;
      fixture.detectChanges();
      
      const prevBtn = compiled.querySelector('.mobile-btn-prev') as HTMLButtonElement;
      expect(prevBtn.disabled).toBe(true);
    });

    it('ShouldDisableMobileNextButtonOnLastPage', () => {
      component.currentPage = 10;
      fixture.detectChanges();
      
      const nextBtn = compiled.querySelector('.mobile-btn-next') as HTMLButtonElement;
      expect(nextBtn.disabled).toBe(true);
    });
  });

  describe('Track By Functions', () => {
    it('ShouldTrackPagesByValue', () => {
      const result = component.trackByPage(0, 5);
      expect(result).toBe(5);
    });
  });

  describe('Utility Methods', () => {
    it('ShouldGoToValidPage', () => {
      spyOn(component.pageChange, 'emit');
      component.totalPages = 10;
      component.currentPage = 5;
      
      component.goToPage(7);
      
      expect(component.pageChange.emit).toHaveBeenCalledWith(7);
    });

    it('ShouldNotGoToInvalidPage', () => {
      spyOn(component.pageChange, 'emit');
      component.totalPages = 10;
      component.currentPage = 5;
      
      component.goToPage(15); // Invalid page
      
      expect(component.pageChange.emit).not.toHaveBeenCalled();
    });

    it('ShouldNotGoToCurrentPage', () => {
      spyOn(component.pageChange, 'emit');
      component.totalPages = 10;
      component.currentPage = 5;
      
      component.goToPage(5); // Current page
      
      expect(component.pageChange.emit).not.toHaveBeenCalled();
    });

    it('ShouldGetCorrectPageLabel', () => {
      expect(component.getPageLabel(5)).toBe('Go to page 5');
      expect(component.getPageLabel(-1)).toBe('More pages');
    });
  });

  describe('Edge Cases', () => {
    it('ShouldHandleZeroTotalItems', () => {
      component.totalItems = 0;
      fixture.detectChanges();
      
      const pagination = compiled.querySelector('.table-pagination');
      expect(pagination).toBeFalsy();
    });

    it('ShouldHandleSinglePage', () => {
      component.totalItems = 5;
      component.pageSize = 10;
      component.totalPages = 1;
      fixture.detectChanges();
      
      const controls = compiled.querySelector('.pagination-controls');
      expect(controls).toBeFalsy();
    });

    it('ShouldHandleLargeNumberOfPages', () => {
      component.totalPages = 1000;
      component.currentPage = 500;
      
      const visiblePages = component.visiblePages;
      expect(visiblePages.length).toBeLessThanOrEqual(component.maxVisiblePages + 4); // +4 for first, last, and ellipses
    });

    it('ShouldHandleEmptyPageSizeOptions', () => {
      component.pageSizeOptions = [];
      fixture.detectChanges();
      
      const selector = compiled.querySelector('.page-size-selector');
      expect(selector).toBeFalsy();
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      component.totalItems = 100;
      component.pageSize = 10;
      component.totalPages = 10;
      component.currentPage = 5;
      fixture.detectChanges();
    });

    it('ShouldHaveProperAriaLabels', () => {
      const firstBtn = compiled.querySelector('.pagination-btn-first');
      const prevBtn = compiled.querySelector('.pagination-btn-prev');
      const nextBtn = compiled.querySelector('.pagination-btn-next');
      const lastBtn = compiled.querySelector('.pagination-btn-last');
      
      expect(firstBtn?.getAttribute('aria-label')).toBe('Go to first page');
      expect(prevBtn?.getAttribute('aria-label')).toBe('Go to previous page');
      expect(nextBtn?.getAttribute('aria-label')).toBe('Go to next page');
      expect(lastBtn?.getAttribute('aria-label')).toBe('Go to last page');
    });

    it('ShouldHaveProperAriaCurrentForActivePage', () => {
      const activeButton = compiled.querySelector('.page-number.active');
      expect(activeButton?.getAttribute('aria-current')).toBe('page');
    });

    it('ShouldHaveProperPageSizeSelectLabel', () => {
      const select = compiled.querySelector('.page-size-select');
      expect(select?.getAttribute('aria-label')).toBe('Items per page');
    });
  });
});
