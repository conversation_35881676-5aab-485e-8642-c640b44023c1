import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DataTablePaginationInfo } from '../../../models';

@Component({
  selector: 'app-table-pagination',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="table-pagination" *ngIf="totalItems > 0">
      <!-- Pagination Info -->
      <div class="pagination-info" *ngIf="showInfo">
        <span class="info-text">
          Showing {{ startItem }} to {{ endItem }} of {{ totalItems }} entries
        </span>
      </div>

      <!-- Page Size Selector -->
      <div class="page-size-selector" *ngIf="showPageSizeSelector && pageSizeOptions.length > 1">
        <label for="page-size-select" class="page-size-label">
          Show:
        </label>
        <select
          id="page-size-select"
          class="page-size-select"
          [value]="pageSize"
          (change)="onPageSizeChange($event)"
          [attr.aria-label]="'Items per page'">
          <option *ngFor="let size of pageSizeOptions" [value]="size">
            {{ size }}
          </option>
        </select>
        <span class="page-size-suffix">per page</span>
      </div>

      <!-- Pagination Controls -->
      <div class="pagination-controls" *ngIf="totalPages > 1">
        <!-- First Page -->
        <button
          type="button"
          class="pagination-btn pagination-btn-first"
          [disabled]="currentPage === 1"
          (click)="goToPage(1)"
          [attr.aria-label]="'Go to first page'"
          title="First page">
          ⟪
        </button>

        <!-- Previous Page -->
        <button
          type="button"
          class="pagination-btn pagination-btn-prev"
          [disabled]="currentPage === 1"
          (click)="goToPage(currentPage - 1)"
          [attr.aria-label]="'Go to previous page'"
          title="Previous page">
          ⟨
        </button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <button
            *ngFor="let page of visiblePages; trackBy: trackByPage"
            type="button"
            class="pagination-btn page-number"
            [class.active]="page === currentPage"
            [class.ellipsis]="page === -1"
            [disabled]="page === -1"
            (click)="goToPage(page)"
            [attr.aria-label]="getPageLabel(page)"
            [attr.aria-current]="page === currentPage ? 'page' : null">
            {{ page === -1 ? '...' : page }}
          </button>
        </div>

        <!-- Next Page -->
        <button
          type="button"
          class="pagination-btn pagination-btn-next"
          [disabled]="currentPage === totalPages"
          (click)="goToPage(currentPage + 1)"
          [attr.aria-label]="'Go to next page'"
          title="Next page">
          ⟩
        </button>

        <!-- Last Page -->
        <button
          type="button"
          class="pagination-btn pagination-btn-last"
          [disabled]="currentPage === totalPages"
          (click)="goToPage(totalPages)"
          [attr.aria-label]="'Go to last page'"
          title="Last page">
          ⟫
        </button>
      </div>

      <!-- Mobile Pagination -->
      <div class="mobile-pagination" *ngIf="totalPages > 1">
        <button
          type="button"
          class="mobile-btn mobile-btn-prev"
          [disabled]="currentPage === 1"
          (click)="goToPage(currentPage - 1)"
          [attr.aria-label]="'Previous page'">
          ← Previous
        </button>

        <span class="mobile-page-info">
          Page {{ currentPage }} of {{ totalPages }}
        </span>

        <button
          type="button"
          class="mobile-btn mobile-btn-next"
          [disabled]="currentPage === totalPages"
          (click)="goToPage(currentPage + 1)"
          [attr.aria-label]="'Next page'">
          Next →
        </button>
      </div>
    </div>
  `,
  styles: [`
    .table-pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: var(--space-4, 16px);
      padding: var(--space-4, 16px);
      background: var(--bg-primary, #ffffff);
      border-top: 1px solid var(--border-primary, #e5e7eb);
      border-radius: 0 0 var(--radius-md, 8px) var(--radius-md, 8px);
    }

    .pagination-info {
      flex-shrink: 0;
    }

    .info-text {
      font-size: var(--text-sm, 14px);
      color: var(--text-secondary, #6b7280);
    }

    .page-size-selector {
      display: flex;
      align-items: center;
      gap: var(--space-2, 8px);
      flex-shrink: 0;
    }

    .page-size-label,
    .page-size-suffix {
      font-size: var(--text-sm, 14px);
      color: var(--text-secondary, #6b7280);
    }

    .page-size-select {
      padding: var(--space-1, 4px) var(--space-2, 8px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--text-sm, 14px);
      background: var(--bg-primary, #ffffff);
      cursor: pointer;
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: var(--space-1, 4px);
      flex: 1;
      justify-content: center;
    }

    .page-numbers {
      display: flex;
      align-items: center;
      gap: var(--space-1, 4px);
    }

    .pagination-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 32px;
      height: 32px;
      padding: var(--space-1, 4px) var(--space-2, 8px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-sm, 4px);
      background: var(--bg-primary, #ffffff);
      color: var(--text-secondary, #6b7280);
      font-size: var(--text-sm, 14px);
      cursor: pointer;
      transition: all var(--transition-fast, 150ms);
    }

    .pagination-btn:hover:not(:disabled) {
      background: var(--bg-secondary, #f9fafb);
      color: var(--text-primary, #111827);
      border-color: var(--border-secondary, #d1d5db);
    }

    .pagination-btn:focus-visible {
      outline: 2px solid var(--border-focus, #3b82f6);
      outline-offset: 2px;
    }

    .pagination-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: var(--bg-disabled, #f9fafb);
    }

    .pagination-btn.active {
      background: var(--interactive-primary, #3b82f6);
      color: white;
      border-color: var(--interactive-primary, #3b82f6);
    }

    .pagination-btn.ellipsis {
      border: none;
      background: transparent;
      cursor: default;
    }

    .pagination-btn.ellipsis:hover {
      background: transparent;
    }

    .mobile-pagination {
      display: none;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      gap: var(--space-4, 16px);
    }

    .mobile-btn {
      padding: var(--space-2, 8px) var(--space-3, 12px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-sm, 4px);
      background: var(--bg-primary, #ffffff);
      color: var(--text-secondary, #6b7280);
      font-size: var(--text-sm, 14px);
      cursor: pointer;
      transition: all var(--transition-fast, 150ms);
    }

    .mobile-btn:hover:not(:disabled) {
      background: var(--bg-secondary, #f9fafb);
      color: var(--text-primary, #111827);
    }

    .mobile-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .mobile-page-info {
      font-size: var(--text-sm, 14px);
      color: var(--text-secondary, #6b7280);
      text-align: center;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .table-pagination {
        flex-direction: column;
        gap: var(--space-3, 12px);
      }

      .pagination-controls {
        display: none;
      }

      .mobile-pagination {
        display: flex;
      }

      .pagination-info,
      .page-size-selector {
        order: 2;
      }

      .mobile-pagination {
        order: 1;
      }
    }

    @media (max-width: 480px) {
      .page-size-selector {
        flex-direction: column;
        text-align: center;
        gap: var(--space-1, 4px);
      }

      .pagination-info {
        text-align: center;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .table-pagination {
        background: var(--bg-primary-dark, #1f2937);
        border-top-color: var(--border-primary-dark, #374151);
      }

      .pagination-btn,
      .mobile-btn,
      .page-size-select {
        background: var(--bg-secondary-dark, #111827);
        border-color: var(--border-primary-dark, #374151);
        color: var(--text-secondary-dark, #9ca3af);
      }

      .pagination-btn:hover:not(:disabled),
      .mobile-btn:hover:not(:disabled) {
        background: var(--bg-tertiary-dark, #374151);
        color: var(--text-primary-dark, #f9fafb);
      }

      .pagination-btn.active {
        background: var(--interactive-primary-dark, #2563eb);
        border-color: var(--interactive-primary-dark, #2563eb);
      }
    }

    /* High contrast mode */
    @media (prefers-contrast: high) {
      .pagination-btn,
      .mobile-btn {
        border-width: 2px;
      }

      .pagination-btn.active {
        border-color: var(--border-high-contrast, #000000);
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TablePaginationComponent {
  @Input() currentPage: number = 1;
  @Input() totalPages: number = 1;
  @Input() totalItems: number = 0;
  @Input() pageSize: number = 10;
  @Input() pageSizeOptions: number[] = [5, 10, 25, 50, 100];
  @Input() showInfo: boolean = true;
  @Input() showPageSizeSelector: boolean = true;
  @Input() maxVisiblePages: number = 7;

  @Output() pageChange = new EventEmitter<number>();
  @Output() pageSizeChange = new EventEmitter<number>();

  get startItem(): number {
    return this.totalItems === 0 ? 0 : (this.currentPage - 1) * this.pageSize + 1;
  }

  get endItem(): number {
    return Math.min(this.currentPage * this.pageSize, this.totalItems);
  }

  get visiblePages(): number[] {
    const pages: number[] = [];
    const half = Math.floor(this.maxVisiblePages / 2);
    
    let start = Math.max(1, this.currentPage - half);
    let end = Math.min(this.totalPages, start + this.maxVisiblePages - 1);
    
    if (end - start + 1 < this.maxVisiblePages) {
      start = Math.max(1, end - this.maxVisiblePages + 1);
    }

    // Add first page and ellipsis if needed
    if (start > 1) {
      pages.push(1);
      if (start > 2) {
        pages.push(-1); // Ellipsis
      }
    }

    // Add visible pages
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    // Add ellipsis and last page if needed
    if (end < this.totalPages) {
      if (end < this.totalPages - 1) {
        pages.push(-1); // Ellipsis
      }
      pages.push(this.totalPages);
    }

    return pages;
  }

  trackByPage(index: number, page: number): number {
    return page;
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.pageChange.emit(page);
    }
  }

  onPageSizeChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const newPageSize = parseInt(target.value, 10);
    this.pageSizeChange.emit(newPageSize);
  }

  getPageLabel(page: number): string {
    if (page === -1) {
      return 'More pages';
    }
    return `Go to page ${page}`;
  }
}
