/* Loading Container */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.loading-container.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  z-index: var(--z-index-modal);
  backdrop-filter: blur(2px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-3);
}

/* Spinner Base */
.spinner {
  border-radius: 50%;
  border-style: solid;
  border-top-style: solid;
  animation: spin 1s linear infinite;
}

/* Spinner Sizes */
.spinner-sm {
  width: 1rem;
  height: 1rem;
  border-width: 2px;
}

.spinner-md {
  width: 1.5rem;
  height: 1.5rem;
  border-width: 2px;
}

.spinner-lg {
  width: 2rem;
  height: 2rem;
  border-width: 3px;
}

.spinner-xl {
  width: 3rem;
  height: 3rem;
  border-width: 4px;
}

/* Spinner Colors */
.spinner-primary {
  border-color: var(--border-primary);
  border-top-color: var(--interactive-primary);
}

.spinner-secondary {
  border-color: var(--border-secondary);
  border-top-color: var(--text-secondary);
}

.spinner-success {
  border-color: var(--status-success-border);
  border-top-color: var(--status-success);
}

.spinner-warning {
  border-color: var(--status-warning-border);
  border-top-color: var(--status-warning);
}

.spinner-error {
  border-color: var(--status-error-border);
  border-top-color: var(--status-error);
}

/* Loading Message */
.loading-message {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}

.overlay .loading-message {
  color: var(--text-inverse);
}

/* Animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
