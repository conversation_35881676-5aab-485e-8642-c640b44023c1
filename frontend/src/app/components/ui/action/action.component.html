<a
  [routerLink]="href"
  class="action-btn"
  [title]="text"
  [ngClass]="{
    'action-btn-icon': !text,
    'action-btn-sm': size === 'small',
    'action-btn-lg': size === 'large',
    'action-btn-translate': hover === 'translate',
    'action-btn-rotate': hover === 'rotate',
    'action-btn-ghost': variant === 'ghost',
    'action-btn-primary': variant === 'primary',
    'action-btn-secondary': variant === 'secondary',
    'action-btn-success': variant === 'success',
    'action-btn-error': variant === 'error',
    'action-btn-warning': variant === 'warning',
    'action-btn-info': variant === 'info'
  }"
>
  <span class="action-icon">{{icon}}</span>
  <span class="action-label">{{text}}</span>
</a>
