import {Component, Input} from '@angular/core';
import {RouterLink} from '@angular/router';
import {NgClass} from '@angular/common';

@Component({
  selector: 'app-action',
  imports: [
    RouterLink,
    NgClass
  ],
  templateUrl: './action.component.html',
  styleUrl: './action.component.css'
})
export class ActionComponent {
  @Input() text: string = '';
  @Input() icon: string = '';
  @Input() size: string | 'small' | 'medium' | 'large' = 'medium';
  @Input() variant: string | 'ghost' | 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info' = 'ghost';
  @Input() hover: string | 'none' | 'translate' | 'rotate' = 'translate';
  @Input() href: string = '';
}
