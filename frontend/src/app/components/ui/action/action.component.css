/* Action Button Base Styles */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3);
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-lg);
  color: var(--primary-700);
  text-decoration: none;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  transition: all var(--transition-fast);
}

.action-btn:hover {
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  border-color: var(--primary-300);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  text-decoration: none;
}

/* Icon and Label */
.action-icon {
  font-size: var(--text-sm);
}

.action-label {
  font-size: var(--text-xs);
}

/* Size Variants */
.action-btn-sm {
  padding: var(--space-2);
  font-size: var(--text-xs);
}

.action-btn-lg {
  padding: var(--space-4);
  font-size: var(--text-sm);
}

/* Icon-only variant */
.action-btn-icon {
  padding: var(--space-2);
  aspect-ratio: 1;
}

/* Hover Effects */
.action-btn-translate:hover {
  transform: translateY(-3px);
}

.action-btn-rotate:hover {
  transform: rotate(5deg);
}

/* Color Variants */
.action-btn-ghost {
  background: transparent;
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.action-btn-ghost:hover {
  background: var(--bg-surface-hover);
  border-color: var(--border-secondary);
}

.action-btn-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  border-color: var(--primary-600);
}

.action-btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  border-color: var(--primary-700);
}

.action-btn-secondary {
  background: linear-gradient(135deg, var(--bg-surface), var(--bg-surface-hover));
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.action-btn-secondary:hover {
  background: var(--bg-surface-hover);
  border-color: var(--border-secondary);
}

.action-btn-success {
  background: linear-gradient(135deg, var(--success-500), var(--color-success-600));
  color: white;
  border-color: var(--color-success-600);
}

.action-btn-success:hover {
  background: linear-gradient(135deg, var(--color-success-600), var(--success-700));
  border-color: var(--success-700);
}

.action-btn-error {
  background: linear-gradient(135deg, var(--error-500), var(--color-error-600));
  color: white;
  border-color: var(--color-error-600);
}

.action-btn-error:hover {
  background: linear-gradient(135deg, var(--color-error-600), var(--error-700));
  border-color: var(--error-700);
}

.action-btn-warning {
  background: linear-gradient(135deg, var(--warning-500), var(--color-warning-600));
  color: white;
  border-color: var(--color-warning-600);
}

.action-btn-warning:hover {
  background: linear-gradient(135deg, var(--color-warning-600), var(--warning-700));
  border-color: var(--warning-700);
}
