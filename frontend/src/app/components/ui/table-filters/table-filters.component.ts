import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { DataTableColumn, DataTableFilter } from '../../../models';

@Component({
  selector: 'app-table-filters',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="table-filters" *ngIf="hasFilterableColumns">
      <div class="filters-header">
        <h3 class="filters-title">Filters</h3>
        <button
          type="button"
          class="clear-filters-btn"
          (click)="clearAllFilters()"
          [disabled]="!hasActiveFilters"
          [attr.aria-label]="'Clear all filters'">
          Clear All
        </button>
      </div>

      <div class="filters-grid">
        <div
          *ngFor="let column of filterableColumns; trackBy: trackByColumn"
          class="filter-group"
          [class]="'filter-' + column.type">
          
          <label [for]="getFilterId(column)" class="filter-label">
            {{ column.label }}
          </label>

          <!-- Custom Filter Template -->
          <ng-container *ngIf="column.filterTemplate; else defaultFilter">
            <ng-container *ngTemplateOutlet="column.filterTemplate; context: { 
              column: column, 
              value: getFilterValue(column.key),
              onChange: getFilterChangeHandler(column.key)
            }"></ng-container>
          </ng-container>

          <!-- Default Filter Templates -->
          <ng-template #defaultFilter>
            <!-- Text Filter -->
            <div *ngIf="column.type === 'text' || !column.type" class="filter-input-group">
              <input
                type="text"
                [id]="getFilterId(column)"
                class="filter-input"
                [placeholder]="'Filter ' + column.label.toLowerCase()"
                [value]="getFilterValue(column.key)"
                (input)="onFilterChange(column.key, $event)"
                [attr.aria-label]="'Filter by ' + column.label">
              <span class="filter-icon">🔍</span>
            </div>

            <!-- Number Filter -->
            <div *ngIf="column.type === 'number'" class="filter-number-group">
              <select
                class="filter-operator"
                [value]="getFilterOperator(column.key)"
                (change)="onOperatorChange(column.key, $event)"
                [attr.aria-label]="'Filter operator for ' + column.label">
                <option value="equals">Equals</option>
                <option value="greaterThan">Greater than</option>
                <option value="lessThan">Less than</option>
                <option value="between">Between</option>
              </select>
              <input
                type="number"
                [id]="getFilterId(column)"
                class="filter-input"
                [placeholder]="getNumberPlaceholder(column.key)"
                [value]="getFilterValue(column.key)"
                (input)="onFilterChange(column.key, $event)"
                [attr.aria-label]="'Filter value for ' + column.label">
              <input
                *ngIf="getFilterOperator(column.key) === 'between'"
                type="number"
                class="filter-input filter-input-secondary"
                placeholder="To"
                [value]="getFilterSecondaryValue(column.key)"
                (input)="onSecondaryFilterChange(column.key, $event)"
                [attr.aria-label]="'Filter upper bound for ' + column.label">
            </div>

            <!-- Date Filter -->
            <div *ngIf="column.type === 'date'" class="filter-date-group">
              <select
                class="filter-operator"
                [value]="getFilterOperator(column.key)"
                (change)="onOperatorChange(column.key, $event)"
                [attr.aria-label]="'Date filter operator for ' + column.label">
                <option value="equals">On</option>
                <option value="greaterThan">After</option>
                <option value="lessThan">Before</option>
                <option value="between">Between</option>
              </select>
              <input
                type="date"
                [id]="getFilterId(column)"
                class="filter-input"
                [value]="getFilterValue(column.key)"
                (change)="onFilterChange(column.key, $event)"
                [attr.aria-label]="'Filter date for ' + column.label">
              <input
                *ngIf="getFilterOperator(column.key) === 'between'"
                type="date"
                class="filter-input filter-input-secondary"
                [value]="getFilterSecondaryValue(column.key)"
                (change)="onSecondaryFilterChange(column.key, $event)"
                [attr.aria-label]="'Filter end date for ' + column.label">
            </div>

            <!-- Boolean Filter -->
            <div *ngIf="column.type === 'boolean'" class="filter-boolean-group">
              <select
                [id]="getFilterId(column)"
                class="filter-select"
                [value]="getFilterValue(column.key)"
                (change)="onFilterChange(column.key, $event)"
                [attr.aria-label]="'Filter by ' + column.label">
                <option value="">All</option>
                <option value="true">Yes</option>
                <option value="false">No</option>
              </select>
            </div>
          </ng-template>

          <!-- Clear Individual Filter -->
          <button
            *ngIf="hasFilterValue(column.key)"
            type="button"
            class="clear-filter-btn"
            (click)="clearFilter(column.key)"
            [attr.aria-label]="'Clear ' + column.label + ' filter'">
            ✕
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .table-filters {
      background: var(--bg-primary, #ffffff);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-md, 8px);
      padding: var(--space-4, 16px);
      margin-bottom: var(--space-4, 16px);
    }

    .filters-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--space-4, 16px);
      padding-bottom: var(--space-3, 12px);
      border-bottom: 1px solid var(--border-secondary, #f3f4f6);
    }

    .filters-title {
      margin: 0;
      font-size: var(--text-lg, 18px);
      font-weight: var(--font-semibold, 600);
      color: var(--text-primary, #111827);
    }

    .clear-filters-btn {
      background: var(--bg-secondary, #f9fafb);
      color: var(--text-secondary, #6b7280);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-sm, 4px);
      padding: var(--space-2, 8px) var(--space-3, 12px);
      font-size: var(--text-sm, 14px);
      cursor: pointer;
      transition: all var(--transition-fast, 150ms);
    }

    .clear-filters-btn:hover:not(:disabled) {
      background: var(--bg-tertiary, #f3f4f6);
      color: var(--text-primary, #111827);
    }

    .clear-filters-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--space-4, 16px);
    }

    .filter-group {
      position: relative;
    }

    .filter-label {
      display: block;
      font-size: var(--text-sm, 14px);
      font-weight: var(--font-medium, 500);
      color: var(--text-secondary, #6b7280);
      margin-bottom: var(--space-2, 8px);
    }

    .filter-input-group {
      position: relative;
    }

    .filter-input {
      width: 100%;
      padding: var(--space-2, 8px) var(--space-3, 12px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--text-sm, 14px);
      background: var(--bg-primary, #ffffff);
      transition: border-color var(--transition-fast, 150ms);
    }

    .filter-input:focus {
      outline: none;
      border-color: var(--border-focus, #3b82f6);
      box-shadow: 0 0 0 3px var(--shadow-focus, rgba(59, 130, 246, 0.1));
    }

    .filter-input-secondary {
      margin-top: var(--space-2, 8px);
    }

    .filter-icon {
      position: absolute;
      right: var(--space-3, 12px);
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-tertiary, #9ca3af);
      pointer-events: none;
    }

    .filter-number-group,
    .filter-date-group {
      display: flex;
      flex-direction: column;
      gap: var(--space-2, 8px);
    }

    .filter-operator {
      padding: var(--space-2, 8px) var(--space-3, 12px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--text-sm, 14px);
      background: var(--bg-primary, #ffffff);
      cursor: pointer;
    }

    .filter-select {
      width: 100%;
      padding: var(--space-2, 8px) var(--space-3, 12px);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--text-sm, 14px);
      background: var(--bg-primary, #ffffff);
      cursor: pointer;
    }

    .clear-filter-btn {
      position: absolute;
      top: 0;
      right: 0;
      width: 20px;
      height: 20px;
      background: var(--status-error, #ef4444);
      color: white;
      border: none;
      border-radius: 50%;
      font-size: var(--text-xs, 12px);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: translate(50%, -50%);
      transition: background-color var(--transition-fast, 150ms);
    }

    .clear-filter-btn:hover {
      background: var(--status-error-hover, #dc2626);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .filters-grid {
        grid-template-columns: 1fr;
      }

      .filters-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-2, 8px);
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .table-filters {
        background: var(--bg-primary-dark, #1f2937);
        border-color: var(--border-primary-dark, #374151);
      }

      .filters-title {
        color: var(--text-primary-dark, #f9fafb);
      }

      .filter-input,
      .filter-operator,
      .filter-select {
        background: var(--bg-secondary-dark, #111827);
        border-color: var(--border-primary-dark, #374151);
        color: var(--text-primary-dark, #f9fafb);
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TableFiltersComponent implements OnInit, OnDestroy {
  @Input() columns: DataTableColumn[] = [];
  @Input() filters: DataTableFilter[] = [];
  @Input() debounceTime: number = 300;

  @Output() filtersChange = new EventEmitter<DataTableFilter[]>();

  private destroy$ = new Subject<void>();
  private filterSubjects = new Map<string, Subject<string>>();

  get filterableColumns(): DataTableColumn[] {
    return this.columns.filter(col => col.filterable && col.visible !== false);
  }

  get hasFilterableColumns(): boolean {
    return this.filterableColumns.length > 0;
  }

  get hasActiveFilters(): boolean {
    return this.filters.length > 0;
  }

  ngOnInit(): void {
    this.setupFilterSubjects();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.filterSubjects.forEach(subject => subject.complete());
  }

  private setupFilterSubjects(): void {
    this.filterableColumns.forEach(column => {
      const subject = new Subject<string>();
      this.filterSubjects.set(column.key, subject);

      subject.pipe(
        debounceTime(this.debounceTime),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      ).subscribe(value => {
        this.updateFilter(column.key, value);
      });
    });
  }

  trackByColumn(index: number, column: DataTableColumn): string {
    return column.key;
  }

  getFilterId(column: DataTableColumn): string {
    return `filter-${column.key}`;
  }

  getFilterValue(columnKey: string): any {
    const filter = this.filters.find(f => f.column === columnKey);
    return filter?.value || '';
  }

  getFilterSecondaryValue(columnKey: string): any {
    const filter = this.filters.find(f => f.column === columnKey);
    return filter?.value?.to || '';
  }

  getFilterOperator(columnKey: string): string {
    const filter = this.filters.find(f => f.column === columnKey);
    return filter?.operator || 'equals';
  }

  hasFilterValue(columnKey: string): boolean {
    const filter = this.filters.find(f => f.column === columnKey);
    return filter && filter.value !== '' && filter.value !== null && filter.value !== undefined;
  }

  onFilterChange(columnKey: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    const subject = this.filterSubjects.get(columnKey);
    if (subject) {
      subject.next(target.value);
    }
  }

  onSecondaryFilterChange(columnKey: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    const currentFilter = this.filters.find(f => f.column === columnKey);
    const currentValue = currentFilter?.value || {};
    
    this.updateFilter(columnKey, {
      ...currentValue,
      to: target.value
    });
  }

  onOperatorChange(columnKey: string, event: Event): void {
    const target = event.target as HTMLSelectElement;
    const currentFilter = this.filters.find(f => f.column === columnKey);
    
    if (currentFilter) {
      this.updateFilter(columnKey, currentFilter.value, target.value as any);
    }
  }

  private updateFilter(columnKey: string, value: any, operator: string = 'equals'): void {
    const newFilters = this.filters.filter(f => f.column !== columnKey);
    
    if (value !== '' && value !== null && value !== undefined) {
      newFilters.push({
        column: columnKey,
        value: value,
        operator: operator as any
      });
    }

    this.filtersChange.emit(newFilters);
  }

  clearFilter(columnKey: string): void {
    const newFilters = this.filters.filter(f => f.column !== columnKey);
    this.filtersChange.emit(newFilters);
  }

  clearAllFilters(): void {
    this.filtersChange.emit([]);
  }

  getNumberPlaceholder(columnKey: string): string {
    const operator = this.getFilterOperator(columnKey);
    switch (operator) {
      case 'greaterThan': return 'Greater than...';
      case 'lessThan': return 'Less than...';
      case 'between': return 'From...';
      default: return 'Enter number...';
    }
  }

  getFilterChangeHandler(columnKey: string): (value: any) => void {
    return (value: any) => {
      this.updateFilter(columnKey, value);
    };
  }
}
