import {
  Component, ContentChildren, QueryList, Output, EventEmitter, Input, AfterContentInit, HostListener, OnInit,
  Inject, PLATFORM_ID, forwardRef
} from '@angular/core';
import {SelectItemComponent} from '../select-item/select-item.component';
import {NgForOf, NgIf} from '@angular/common';
import {isPlatformBrowser} from '@angular/common';
import {ControlValueAccessor, NG_VALUE_ACCESSOR} from '@angular/forms';

@Component({
  selector: 'app-select',
  templateUrl: './select.component.html',
  imports: [
    NgIf,
    NgForOf
  ],
  styleUrl: './select.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SelectComponent),
      multi: true
    }
  ]
})
export class SelectComponent<T> implements AfterContentInit, OnInit, ControlValueAccessor {
  @ContentChildren(SelectItemComponent) items!: QueryList<SelectItemComponent<T>>;
  @Output() onSelect: EventEmitter<T> = new EventEmitter<T>();
  @Input() selectedValue?: T;
  @Input() defaultItem?: SelectItemComponent<T>;
  @Input() mobileDropDownEnabled: boolean = true;

  dropdownOpen = false;
  selectedItem?: SelectItemComponent<T>;
  isMobileView: boolean = false;
  disabled: boolean = false;

  private onChange: (value: T | undefined) => void = () => {
  };
  private onTouched: () => void = () => {
  };

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
  }

  writeValue(value: T): void {
    if (value !== undefined && this.items) {
      this.selectedValue = value;
      this.selectedItem = this.allItems.find(item => item.value === value) || this.defaultItem;
    }
  }

  registerOnChange(fn: (value: T | undefined) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  ngOnInit(): void {
    this.checkScreenSize();
  }

  @HostListener('window:resize')
  onResize() {
    this.checkScreenSize();
  }

  private checkScreenSize() {
    if (isPlatformBrowser(this.platformId)) {
      this.isMobileView = window.innerWidth < 768;
    }
  }

  get showMobileDropdown(): boolean {
    return this.mobileDropDownEnabled && this.isMobileView;
  }

  get allItems(): SelectItemComponent<T>[] {
    const items = this.items ? this.items.toArray() : [];
    return this.defaultItem ? [this.defaultItem, ...items] : items;
  }

  ngAfterContentInit(): void {
    if (this.selectedValue !== undefined) {
      this.selectedItem = this.allItems.find(item => item.value === this.selectedValue)
        || this.defaultItem;
    } else if (this.defaultItem) {
      this.selectedItem = this.defaultItem;
    }
  }

  toggleDropdown(): void {
    if (this.disabled) return;
    this.dropdownOpen = !this.dropdownOpen;
    this.onTouched();
  }

  selectItem(item: SelectItemComponent<T>): void {
    if (this.disabled) return;
    this.selectedItem = item;
    this.selectedValue = item.value;
    this.dropdownOpen = false;
    this.onChange(item.value);
    this.onTouched();
    this.onSelect.emit(item.value);
  }
}
