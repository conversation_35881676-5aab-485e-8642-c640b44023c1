<div class="select" [class.open]="dropdownOpen">
  <div class="selected" (click)="toggleDropdown()">
    <ng-container *ngIf="selectedItem; else placeholder">
      <ng-container *ngIf="selectedItem.image; else textOnlySelected">
        <img [src]="selectedItem.image" alt="{{ selectedItem.text }}">
      </ng-container>
      <ng-template #textOnlySelected></ng-template>
      <span>{{ selectedItem.text }}</span>
    </ng-container>
    <ng-template #placeholder>
      <span>Select an option</span>
    </ng-template>
    <i class="arrow" [class.open]="dropdownOpen"></i>
  </div>

  <!-- Regular dropdown for desktop -->
  <ul *ngIf="dropdownOpen && !showMobileDropdown" class="options">
    <li *ngFor="let item of allItems"
        [class.selected-option]="item === selectedItem"
        (click)="selectItem(item)">
      <ng-container *ngIf="item.image; else itemTextOnly">
        <img [src]="item.image" alt="{{ item.text }}">
      </ng-container>
      <ng-template #itemTextOnly></ng-template>
      <span>{{ item.text }}</span>
    </li>
  </ul>

  <!-- Mobile bottom sheet -->
  <div *ngIf="dropdownOpen && showMobileDropdown" class="mobile-options-overlay" (click)="toggleDropdown()">
    <div class="mobile-options-container" (click)="$event.stopPropagation()">
      <div class="mobile-options-header">
        <span>Select an option</span>
        <button class="close-button" (click)="toggleDropdown()">×</button>
      </div>
      <ul class="mobile-options">
        <li *ngFor="let item of allItems"
            [class.selected-option]="item === selectedItem"
            (click)="selectItem(item)">
          <ng-container *ngIf="item.image; else mobileItemTextOnly">
            <img [src]="item.image" alt="{{ item.text }}">
          </ng-container>
          <ng-template #mobileItemTextOnly></ng-template>
          <span>{{ item.text }}</span>
        </li>
      </ul>
    </div>
  </div>
</div>
