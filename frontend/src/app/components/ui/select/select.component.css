.select {
  position: relative;
  display: inline-block;
  user-select: none;
  width: 100%;
  min-width: 150px;
  font-family: inherit;
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.select.open{
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.selected {
  padding: 8px 0 8px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

img {
  width: 35px;
  height: auto;
  margin-right: 8px;
}

.arrow {
  margin: 10px;
  border: solid #666;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(45deg);
  transition: transform 0.2s ease;
}

.arrow.open {
  transform: rotate(-135deg);
}

.options {
  padding: 0;
  margin: 0;
  position: absolute;
  top: calc(100% + 2px);
  left: 0;
  right: 0;
  border: 1px solid var(--border-primary);
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  min-width: fit-content;
  width: 100%;
  z-index: var(--z-dropdown);
  box-sizing: border-box;
  color: var(--text-primary);
  background-color: var(--bg-surface);
}

.selected-option {
  background-color: var(--primary-100);
  color: var(--primary-900);
}

.options li {
  padding: 8px 10px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
}

.options li:not(.selected-option):hover {
  background-color: var(--primary-50);
  color: var(--primary-800);
}

span {
  white-space: nowrap;
}

/* Mobile bottom sheet styles */
.mobile-options-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-dropdown);
  display: flex;
  align-items: flex-end;
}

.mobile-options-container {
  width: 100%;
  max-height: 70vh;
  background-color: var(--bg-surface);
  border-top-left-radius: var(--radius-lg);
  border-top-right-radius: var(--radius-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slide-up 0.3s ease;
}

.mobile-options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-primary);
  font-weight: 500;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-primary);
  cursor: pointer;
}

.mobile-options {
  list-style: none;
  margin: 0;
  padding: 0;
  overflow-y: auto;
  max-height: calc(70vh - 60px);
}

.mobile-options li {
  padding: 16px;
  display: flex;
  align-items: center;
}

.mobile-options li:not(.selected-option):active {
  background-color: var(--primary-50);
}

.mobile-options .selected-option {
  background-color: var(--primary-100);
  color: var(--primary-900);
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
