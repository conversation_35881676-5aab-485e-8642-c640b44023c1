import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-table-empty',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="table-empty" [attr.aria-label]="message">
      <div class="empty-content">
        <!-- Custom Icon or Default -->
        <div class="empty-icon" *ngIf="showIcon">
          <ng-container *ngIf="customIcon; else defaultIcon">
            {{ customIcon }}
          </ng-container>
          <ng-template #defaultIcon>
            {{ getDefaultIcon() }}
          </ng-template>
        </div>

        <!-- Title -->
        <h3 class="empty-title" *ngIf="title">
          {{ title }}
        </h3>

        <!-- Message -->
        <p class="empty-message">
          {{ message }}
        </p>

        <!-- Description -->
        <p class="empty-description" *ngIf="description">
          {{ description }}
        </p>

        <!-- Action Buttons -->
        <div class="empty-actions" *ngIf="hasActions">
          <!-- Primary Action -->
          <button
            *ngIf="primaryAction"
            type="button"
            class="empty-btn empty-btn-primary"
            (click)="onPrimaryAction()"
            [disabled]="primaryActionDisabled">
            {{ primaryActionLabel }}
          </button>

          <!-- Secondary Action -->
          <button
            *ngIf="secondaryAction"
            type="button"
            class="empty-btn empty-btn-secondary"
            (click)="onSecondaryAction()"
            [disabled]="secondaryActionDisabled">
            {{ secondaryActionLabel }}
          </button>

          <!-- Clear Filters Action -->
          <button
            *ngIf="showClearFilters"
            type="button"
            class="empty-btn empty-btn-outline"
            (click)="onClearFilters()">
            Clear Filters
          </button>
        </div>

        <!-- Custom Content Projection -->
        <div class="empty-custom" *ngIf="allowCustomContent">
          <ng-content></ng-content>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .table-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;
      padding: var(--space-8, 32px) var(--space-4, 16px);
      background: var(--bg-primary, #ffffff);
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-md, 8px);
      text-align: center;
    }

    .empty-content {
      max-width: 400px;
      width: 100%;
    }

    .empty-icon {
      font-size: 4rem;
      margin-bottom: var(--space-4, 16px);
      opacity: 0.6;
      line-height: 1;
    }

    .empty-title {
      margin: 0 0 var(--space-3, 12px) 0;
      font-size: var(--text-xl, 20px);
      font-weight: var(--font-semibold, 600);
      color: var(--text-primary, #111827);
      line-height: var(--leading-tight, 1.25);
    }

    .empty-message {
      margin: 0 0 var(--space-2, 8px) 0;
      font-size: var(--text-base, 16px);
      font-weight: var(--font-medium, 500);
      color: var(--text-secondary, #6b7280);
      line-height: var(--leading-normal, 1.5);
    }

    .empty-description {
      margin: 0 0 var(--space-6, 24px) 0;
      font-size: var(--text-sm, 14px);
      color: var(--text-tertiary, #9ca3af);
      line-height: var(--leading-relaxed, 1.625);
    }

    .empty-actions {
      display: flex;
      flex-direction: column;
      gap: var(--space-3, 12px);
      align-items: center;
      margin-bottom: var(--space-4, 16px);
    }

    .empty-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: var(--space-3, 12px) var(--space-6, 24px);
      border-radius: var(--radius-md, 8px);
      font-size: var(--text-sm, 14px);
      font-weight: var(--font-medium, 500);
      text-decoration: none;
      cursor: pointer;
      transition: all var(--transition-fast, 150ms);
      border: 1px solid transparent;
      min-width: 120px;
    }

    .empty-btn:focus-visible {
      outline: 2px solid var(--border-focus, #3b82f6);
      outline-offset: 2px;
    }

    .empty-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }

    .empty-btn-primary {
      background: var(--interactive-primary, #3b82f6);
      color: white;
      border-color: var(--interactive-primary, #3b82f6);
    }

    .empty-btn-primary:hover:not(:disabled) {
      background: var(--interactive-primary-hover, #2563eb);
      border-color: var(--interactive-primary-hover, #2563eb);
    }

    .empty-btn-secondary {
      background: var(--bg-secondary, #f9fafb);
      color: var(--text-secondary, #6b7280);
      border-color: var(--border-primary, #e5e7eb);
    }

    .empty-btn-secondary:hover:not(:disabled) {
      background: var(--bg-tertiary, #f3f4f6);
      color: var(--text-primary, #111827);
      border-color: var(--border-secondary, #d1d5db);
    }

    .empty-btn-outline {
      background: transparent;
      color: var(--interactive-primary, #3b82f6);
      border-color: var(--interactive-primary, #3b82f6);
    }

    .empty-btn-outline:hover:not(:disabled) {
      background: var(--interactive-primary, #3b82f6);
      color: white;
    }

    .empty-custom {
      margin-top: var(--space-4, 16px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .table-empty {
        min-height: 250px;
        padding: var(--space-6, 24px) var(--space-3, 12px);
      }

      .empty-icon {
        font-size: 3rem;
        margin-bottom: var(--space-3, 12px);
      }

      .empty-title {
        font-size: var(--text-lg, 18px);
      }

      .empty-message {
        font-size: var(--text-sm, 14px);
      }

      .empty-actions {
        gap: var(--space-2, 8px);
      }

      .empty-btn {
        padding: var(--space-2, 8px) var(--space-4, 16px);
        font-size: var(--text-xs, 12px);
        min-width: 100px;
      }
    }

    @media (max-width: 480px) {
      .empty-actions {
        flex-direction: column;
        width: 100%;
      }

      .empty-btn {
        width: 100%;
        max-width: 200px;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .table-empty {
        background: var(--bg-primary-dark, #1f2937);
        border-color: var(--border-primary-dark, #374151);
      }

      .empty-title {
        color: var(--text-primary-dark, #f9fafb);
      }

      .empty-message {
        color: var(--text-secondary-dark, #9ca3af);
      }

      .empty-description {
        color: var(--text-tertiary-dark, #6b7280);
      }

      .empty-btn-secondary {
        background: var(--bg-secondary-dark, #111827);
        color: var(--text-secondary-dark, #9ca3af);
        border-color: var(--border-primary-dark, #374151);
      }

      .empty-btn-secondary:hover:not(:disabled) {
        background: var(--bg-tertiary-dark, #374151);
        color: var(--text-primary-dark, #f9fafb);
      }
    }

    /* High contrast mode */
    @media (prefers-contrast: high) {
      .empty-btn {
        border-width: 2px;
      }

      .empty-icon {
        opacity: 1;
      }
    }

    /* Reduced motion */
    @media (prefers-reduced-motion: reduce) {
      .empty-btn {
        transition: none;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TableEmptyComponent {
  @Input() message: string = 'No data available';
  @Input() title?: string;
  @Input() description?: string;
  @Input() showIcon: boolean = true;
  @Input() customIcon?: string;
  @Input() emptyType: 'no-data' | 'no-results' | 'error' | 'loading' = 'no-data';
  
  // Actions
  @Input() primaryAction: boolean = false;
  @Input() primaryActionLabel: string = 'Add Item';
  @Input() primaryActionDisabled: boolean = false;
  
  @Input() secondaryAction: boolean = false;
  @Input() secondaryActionLabel: string = 'Refresh';
  @Input() secondaryActionDisabled: boolean = false;
  
  @Input() showClearFilters: boolean = false;
  @Input() allowCustomContent: boolean = false;

  @Output() primaryActionClick = new EventEmitter<void>();
  @Output() secondaryActionClick = new EventEmitter<void>();
  @Output() clearFiltersClick = new EventEmitter<void>();

  get hasActions(): boolean {
    return this.primaryAction || this.secondaryAction || this.showClearFilters;
  }

  onPrimaryAction(): void {
    this.primaryActionClick.emit();
  }

  onSecondaryAction(): void {
    this.secondaryActionClick.emit();
  }

  onClearFilters(): void {
    this.clearFiltersClick.emit();
  }

  getDefaultIcon(): string {
    switch (this.emptyType) {
      case 'no-data':
        return '📋';
      case 'no-results':
        return '🔍';
      case 'error':
        return '⚠️';
      case 'loading':
        return '⏳';
      default:
        return '📋';
    }
  }
}
