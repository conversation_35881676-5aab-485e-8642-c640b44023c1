import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { TableSkeletonComponent } from './table-skeleton.component';

describe('TableSkeletonComponent', () => {
  let component: TableSkeletonComponent;
  let fixture: ComponentFixture<TableSkeletonComponent>;
  let compiled: HTMLElement;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TableSkeletonComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(TableSkeletonComponent);
    component = fixture.componentInstance;
    compiled = fixture.nativeElement;
  });

  describe('Component Initialization', () => {
    it('ShouldCreateComponent', () => {
      expect(component).toBeTruthy();
    });

    it('ShouldHaveDefaultValues', () => {
      expect(component.rows).toBe(5);
      expect(component.columns).toBe(4);
      expect(component.loadingMessage).toBe('Loading table data');
      expect(component.columnWidths).toEqual([]);
      expect(component.columnTypes).toEqual([]);
    });
  });

  describe('Skeleton Structure', () => {
    beforeEach(() => {
      component.rows = 3;
      component.columns = 4;
      fixture.detectChanges();
    });

    it('ShouldRenderCorrectNumberOfRows', () => {
      const skeletonRows = compiled.querySelectorAll('.skeleton-body .skeleton-row');
      expect(skeletonRows.length).toBe(3);
    });

    it('ShouldRenderCorrectNumberOfColumns', () => {
      const firstRowCells = compiled.querySelectorAll('.skeleton-body .skeleton-row:first-child .skeleton-cell');
      expect(firstRowCells.length).toBe(4);
    });

    it('ShouldRenderHeaderRow', () => {
      const headerRow = compiled.querySelector('.skeleton-header .skeleton-row');
      expect(headerRow).toBeTruthy();
      
      const headerCells = compiled.querySelectorAll('.skeleton-header .skeleton-cell');
      expect(headerCells.length).toBe(4);
    });

    it('ShouldHaveProperAriaLabel', () => {
      const skeleton = compiled.querySelector('.table-skeleton');
      expect(skeleton?.getAttribute('aria-label')).toBe('Loading table data');
    });
  });

  describe('Column Configuration', () => {
    beforeEach(() => {
      component.rows = 2;
      component.columns = 3;
      component.columnWidths = ['100px', '200px', 'auto'];
      component.columnTypes = ['text', 'number', 'date'];
      fixture.detectChanges();
    });

    it('ShouldApplyColumnWidths', () => {
      const firstRowCells = compiled.querySelectorAll('.skeleton-body .skeleton-row:first-child .skeleton-cell');
      
      expect((firstRowCells[0] as HTMLElement).style.width).toBe('100px');
      expect((firstRowCells[1] as HTMLElement).style.width).toBe('200px');
      expect((firstRowCells[2] as HTMLElement).style.width).toBe('auto');
    });

    it('ShouldApplyColumnTypeClasses', () => {
      const firstRowContents = compiled.querySelectorAll('.skeleton-body .skeleton-row:first-child .skeleton-content');
      
      expect(firstRowContents[0]).toHaveClass('skeleton-text');
      expect(firstRowContents[1]).toHaveClass('skeleton-number');
      expect(firstRowContents[2]).toHaveClass('skeleton-date');
    });

    it('ShouldHandleMissingColumnWidths', () => {
      component.columnWidths = ['100px']; // Less than columns count
      fixture.detectChanges();
      
      const firstRowCells = compiled.querySelectorAll('.skeleton-body .skeleton-row:first-child .skeleton-cell');
      expect((firstRowCells[0] as HTMLElement).style.width).toBe('100px');
      expect((firstRowCells[1] as HTMLElement).style.width).toBe('auto');
      expect((firstRowCells[2] as HTMLElement).style.width).toBe('auto');
    });

    it('ShouldHandleMissingColumnTypes', () => {
      component.columnTypes = ['number']; // Less than columns count
      fixture.detectChanges();
      
      const firstRowContents = compiled.querySelectorAll('.skeleton-body .skeleton-row:first-child .skeleton-content');
      expect(firstRowContents[0]).toHaveClass('skeleton-number');
      expect(firstRowContents[1]).toHaveClass('skeleton-text'); // Default type
      expect(firstRowContents[2]).toHaveClass('skeleton-text'); // Default type
    });
  });

  describe('Skeleton Content Types', () => {
    beforeEach(() => {
      component.rows = 1;
      component.columns = 5;
      component.columnTypes = ['text', 'number', 'date', 'boolean', 'action'];
      fixture.detectChanges();
    });

    it('ShouldApplyCorrectSkeletonClasses', () => {
      const contents = compiled.querySelectorAll('.skeleton-body .skeleton-content');
      
      expect(contents[0]).toHaveClass('skeleton-text');
      expect(contents[1]).toHaveClass('skeleton-number');
      expect(contents[2]).toHaveClass('skeleton-date');
      expect(contents[3]).toHaveClass('skeleton-boolean');
      expect(contents[4]).toHaveClass('skeleton-action');
    });

    it('ShouldReturnCorrectSkeletonClass', () => {
      expect(component.getSkeletonClass('text')).toBe('skeleton-text');
      expect(component.getSkeletonClass('number')).toBe('skeleton-number');
      expect(component.getSkeletonClass('date')).toBe('skeleton-date');
      expect(component.getSkeletonClass('boolean')).toBe('skeleton-boolean');
      expect(component.getSkeletonClass('action')).toBe('skeleton-action');
    });
  });

  describe('Computed Properties', () => {
    it('ShouldGenerateCorrectSkeletonRows', () => {
      component.rows = 3;
      const skeletonRows = component.skeletonRows;
      
      expect(skeletonRows).toEqual([0, 1, 2]);
      expect(skeletonRows.length).toBe(3);
    });

    it('ShouldGenerateCorrectSkeletonColumns', () => {
      component.columns = 2;
      component.columnWidths = ['100px'];
      component.columnTypes = ['number'];
      
      const skeletonColumns = component.skeletonColumns;
      
      expect(skeletonColumns).toEqual([
        { width: '100px', type: 'number' },
        { width: 'auto', type: 'text' }
      ]);
    });

    it('ShouldHandleEmptyColumnConfiguration', () => {
      component.columns = 2;
      component.columnWidths = [];
      component.columnTypes = [];
      
      const skeletonColumns = component.skeletonColumns;
      
      expect(skeletonColumns).toEqual([
        { width: 'auto', type: 'text' },
        { width: 'auto', type: 'text' }
      ]);
    });
  });

  describe('Track By Functions', () => {
    it('ShouldTrackByIndex', () => {
      const result = component.trackByIndex(5);
      expect(result).toBe(5);
    });

    it('ShouldReturnSameValueForSameIndex', () => {
      const index = 3;
      const result1 = component.trackByIndex(index);
      const result2 = component.trackByIndex(index);
      expect(result1).toBe(result2);
    });
  });

  describe('Styling and Animation', () => {
    beforeEach(() => {
      component.rows = 2;
      component.columns = 2;
      fixture.detectChanges();
    });

    it('ShouldHaveSkeletonAnimation', () => {
      const skeletonContents = compiled.querySelectorAll('.skeleton-content');
      
      skeletonContents.forEach(content => {
        const styles = window.getComputedStyle(content);
        expect(styles.animation).toContain('skeleton-loading');
      });
    });

    it('ShouldHaveAlternateRowStyling', () => {
      const rows = compiled.querySelectorAll('.skeleton-body .skeleton-row');
      
      expect(rows[0]).not.toHaveClass('skeleton-row-alternate');
      expect(rows[1]).toHaveClass('skeleton-row-alternate');
    });

    it('ShouldHaveProperTableStructure', () => {
      const container = compiled.querySelector('.table-skeleton');
      const header = compiled.querySelector('.skeleton-header');
      const body = compiled.querySelector('.skeleton-body');
      
      expect(container).toBeTruthy();
      expect(header).toBeTruthy();
      expect(body).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      component.loadingMessage = 'Loading user data';
      fixture.detectChanges();
    });

    it('ShouldHaveProperAriaLabel', () => {
      const skeleton = compiled.querySelector('.table-skeleton');
      expect(skeleton?.getAttribute('aria-label')).toBe('Loading user data');
    });

    it('ShouldUpdateAriaLabelWhenMessageChanges', () => {
      component.loadingMessage = 'Loading product information';
      fixture.detectChanges();
      
      const skeleton = compiled.querySelector('.table-skeleton');
      expect(skeleton?.getAttribute('aria-label')).toBe('Loading product information');
    });
  });

  describe('Edge Cases', () => {
    it('ShouldHandleZeroRows', () => {
      component.rows = 0;
      component.columns = 3;
      fixture.detectChanges();
      
      const bodyRows = compiled.querySelectorAll('.skeleton-body .skeleton-row');
      expect(bodyRows.length).toBe(0);
    });

    it('ShouldHandleZeroColumns', () => {
      component.rows = 3;
      component.columns = 0;
      fixture.detectChanges();
      
      const headerCells = compiled.querySelectorAll('.skeleton-header .skeleton-cell');
      const bodyCells = compiled.querySelectorAll('.skeleton-body .skeleton-cell');
      
      expect(headerCells.length).toBe(0);
      expect(bodyCells.length).toBe(0);
    });

    it('ShouldHandleLargeNumbers', () => {
      component.rows = 100;
      component.columns = 20;
      
      expect(component.skeletonRows.length).toBe(100);
      expect(component.skeletonColumns.length).toBe(20);
    });

    it('ShouldHandleNegativeNumbers', () => {
      component.rows = -5;
      component.columns = -3;
      
      // Array.from with negative length should create empty array
      expect(component.skeletonRows.length).toBe(0);
      expect(component.skeletonColumns.length).toBe(0);
    });
  });

  describe('Performance', () => {
    it('ShouldNotRecalculateSkeletonRowsUnnecessarily', () => {
      component.rows = 5;
      const firstCall = component.skeletonRows;
      const secondCall = component.skeletonRows;
      
      // Should return same array reference for same input
      expect(firstCall).toEqual(secondCall);
    });

    it('ShouldNotRecalculateSkeletonColumnsUnnecessarily', () => {
      component.columns = 3;
      component.columnWidths = ['100px'];
      component.columnTypes = ['text'];
      
      const firstCall = component.skeletonColumns;
      const secondCall = component.skeletonColumns;
      
      expect(firstCall).toEqual(secondCall);
    });
  });
});
