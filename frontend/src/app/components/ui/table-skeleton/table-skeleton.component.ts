import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-table-skeleton',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="table-skeleton" [attr.aria-label]="loadingMessage">
      <div class="skeleton-header">
        <div class="skeleton-row">
          <div
            *ngFor="let col of skeletonColumns; trackBy: trackByIndex"
            class="skeleton-cell skeleton-header-cell"
            [style.width]="col.width">
            <div class="skeleton-content"></div>
          </div>
        </div>
      </div>

      <div class="skeleton-body">
        <div
          *ngFor="let row of skeletonRows; trackBy: trackByIndex"
          class="skeleton-row"
          [class.skeleton-row-alternate]="row % 2 === 1">
          <div
            *ngFor="let col of skeletonColumns; trackBy: trackByIndex"
            class="skeleton-cell"
            [style.width]="col.width">
            <div class="skeleton-content" [class]="getSkeletonClass(col.type)"></div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .table-skeleton {
      width: 100%;
      border: 1px solid var(--border-primary, #e5e7eb);
      border-radius: var(--radius-md, 8px);
      overflow: hidden;
      background: var(--bg-primary, #ffffff);
    }

    .skeleton-header {
      background: var(--bg-secondary, #f9fafb);
      border-bottom: 1px solid var(--border-primary, #e5e7eb);
    }

    .skeleton-row {
      display: flex;
      width: 100%;
    }

    .skeleton-row-alternate {
      background: var(--bg-tertiary, #f8fafc);
    }

    .skeleton-cell {
      padding: var(--space-3, 12px) var(--space-4, 16px);
      border-right: 1px solid var(--border-secondary, #f3f4f6);
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }

    .skeleton-cell:last-child {
      border-right: none;
      flex: 1;
    }

    .skeleton-header-cell {
      font-weight: var(--font-semibold, 600);
      background: var(--bg-secondary, #f9fafb);
    }

    .skeleton-content {
      height: 16px;
      background: linear-gradient(
        90deg,
        var(--skeleton-base, #f3f4f6) 25%,
        var(--skeleton-highlight, #e5e7eb) 50%,
        var(--skeleton-base, #f3f4f6) 75%
      );
      background-size: 200% 100%;
      border-radius: var(--radius-sm, 4px);
      animation: skeleton-loading 1.5s infinite;
      width: 100%;
    }

    .skeleton-content.skeleton-text {
      width: 80%;
    }

    .skeleton-content.skeleton-number {
      width: 60%;
    }

    .skeleton-content.skeleton-date {
      width: 70%;
    }

    .skeleton-content.skeleton-boolean {
      width: 40%;
      height: 12px;
    }

    .skeleton-content.skeleton-action {
      width: 50%;
      height: 20px;
    }

    @keyframes skeleton-loading {
      0% {
        background-position: -200% 0;
      }
      100% {
        background-position: 200% 0;
      }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .skeleton-cell {
        padding: var(--space-2, 8px) var(--space-3, 12px);
      }

      .skeleton-content {
        height: 14px;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .table-skeleton {
        border-color: var(--border-primary-dark, #374151);
        background: var(--bg-primary-dark, #1f2937);
      }

      .skeleton-header {
        background: var(--bg-secondary-dark, #111827);
        border-bottom-color: var(--border-primary-dark, #374151);
      }

      .skeleton-row-alternate {
        background: var(--bg-tertiary-dark, #1f2937);
      }

      .skeleton-cell {
        border-right-color: var(--border-secondary-dark, #374151);
      }

      .skeleton-content {
        background: linear-gradient(
          90deg,
          var(--skeleton-base-dark, #374151) 25%,
          var(--skeleton-highlight-dark, #4b5563) 50%,
          var(--skeleton-base-dark, #374151) 75%
        );
      }
    }

    /* High contrast mode */
    @media (prefers-contrast: high) {
      .skeleton-content {
        background: var(--skeleton-high-contrast, #6b7280);
        animation: none;
      }
    }

    /* Reduced motion */
    @media (prefers-reduced-motion: reduce) {
      .skeleton-content {
        animation: none;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TableSkeletonComponent {
  @Input() rows: number = 5;
  @Input() columns: number = 4;
  @Input() loadingMessage: string = 'Loading table data';
  @Input() columnWidths: string[] = [];
  @Input() columnTypes: ('text' | 'number' | 'date' | 'boolean' | 'action' | 'string')[] = [];

  get skeletonRows(): number[] {
    return Array.from({ length: this.rows }, (_, i) => i);
  }

  get skeletonColumns(): { width: string; type: string }[] {
    return Array.from({ length: this.columns }, (_, i) => ({
      width: this.columnWidths[i] || 'auto',
      type: this.columnTypes[i] || 'text'
    }));
  }

  trackByIndex(index: number): number {
    return index;
  }

  getSkeletonClass(type: string): string {
    return `skeleton-${type}`;
  }
}
