export interface ErrorResponse {
  message: string;
  details?: string;
  statusCode: number;
}

export interface AppError {
  id: string;
  type: ErrorType;
  message: string;
  details?: any;
  timestamp: Date;
  userMessage: string;
  retryable: boolean;
}

export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  NETWORK = 'NETWORK',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN'
}
