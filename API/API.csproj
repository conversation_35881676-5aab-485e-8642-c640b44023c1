<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.10" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Application\Application.csproj" />
        <ProjectReference Include="..\Domain\Domain.csproj" />
        <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
        <ProjectReference Include="..\SharedKernel\SharedKernel.csproj" />
    </ItemGroup>

</Project>
