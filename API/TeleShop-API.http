### TeleShop API Testing - True PATCH Semantics Implemented
### Base URL
@baseUrl = http://localhost:5117

### ===========================================
### PRODUCTS API - TRUE PATCH SEMANTICS
### ===========================================

### Get all products
GET {{baseUrl}}/api/products
Accept: application/json

### Get product by ID
GET {{baseUrl}}/api/products/1
Accept: application/json

### Create new product
POST {{baseUrl}}/api/products
Content-Type: application/json

{
  "name": "New Test Product",
  "description": "A test product created via HTTP file",
  "categoryId": 1,
  "tagIds": [1, 2]
}

### PATCH product - Update only name (partial update)
PATCH {{baseUrl}}/api/products
Content-Type: application/json

{
  "id": 1,
  "name": "Updated Product Name Only"
}

### PATCH product - Update only description (partial update)
PATCH {{baseUrl}}/api/products
Content-Type: application/json

{
  "id": 1,
  "description": "Updated description only"
}

### PATCH product - Update only category (partial update)
PATCH {{baseUrl}}/api/products
Content-Type: application/json

{
  "id": 1,
  "categoryId": 2
}

### PATCH product - Update only tags (partial update)
PATCH {{baseUrl}}/api/products
Content-Type: application/json

{
  "id": 1,
  "tagIds": [1, 3, 5]
}

### PATCH product - Update multiple fields (partial update)
PATCH {{baseUrl}}/api/products
Content-Type: application/json

{
  "id": 1,
  "name": "Updated Product Name",
  "categoryId": 2
}

### Delete product
DELETE {{baseUrl}}/api/products/1
Accept: application/json

### Get products by category
GET {{baseUrl}}/api/products/category/1
Accept: application/json

### Get products by tag
GET {{baseUrl}}/api/products/tag/1
Accept: application/json

### Search products
GET {{baseUrl}}/api/products/search?term=laptop
Accept: application/json

### ===========================================
### CATEGORIES API - TRUE PATCH SEMANTICS
### ===========================================

### Get all categories
GET {{baseUrl}}/api/categories
Accept: application/json

### Get category by ID
GET {{baseUrl}}/api/categories/1
Accept: application/json

### Create new category
POST {{baseUrl}}/api/categories
Content-Type: application/json

"New Category Name"

### PATCH category - Update only name (partial update)
PATCH {{baseUrl}}/api/categories
Content-Type: application/json

{
  "id": 1,
  "name": "Updated Category Name"
}

### Delete category
DELETE {{baseUrl}}/api/categories/1
Accept: application/json

### ===========================================
### TAGS API - TRUE PATCH SEMANTICS
### ===========================================

### Get all tags
GET {{baseUrl}}/api/tags
Accept: application/json

### Get tag by ID
GET {{baseUrl}}/api/tags/1
Accept: application/json

### Get tag by slug
GET {{baseUrl}}/api/tags/slug/premium
Accept: application/json

### Create new tag
POST {{baseUrl}}/api/tags
Content-Type: application/json

"New Tag Name"

### PATCH tag - Update only name (partial update)
PATCH {{baseUrl}}/api/tags
Content-Type: application/json

{
  "id": 1,
  "name": "Updated Tag Name"
}

### Delete tag
DELETE {{baseUrl}}/api/tags/1
Accept: application/json

### Get tags by product
GET {{baseUrl}}/api/tags/product/1
Accept: application/json

### ===========================================
### SALES API - TRUE PATCH SEMANTICS
### ===========================================

### Get all sales
GET {{baseUrl}}/api/sales
Accept: application/json

### Get sale by ID
GET {{baseUrl}}/api/sales/019706b6-ecd1-7dfc-803a-4c7250255aab
Accept: application/json

### Get sales by status
GET {{baseUrl}}/api/sales/status/Pending
Accept: application/json

### Get sales by product
GET {{baseUrl}}/api/sales/product/1
Accept: application/json

### Get sales by date range
GET {{baseUrl}}/api/sales/date-range?startDate=2025-01-01&endDate=2025-12-31
Accept: application/json

### Create new sale
POST {{baseUrl}}/api/sales
Content-Type: application/json

{
  "discountAmount": 0,
  "currency": "USD",
  "items": [
    {
      "productId": 2,
      "quantity": 1
    },
    {
      "productId": 3,
      "quantity": 2
    }
  ]
}

### Add item to sale
POST {{baseUrl}}/api/sales/019706b6-ecd1-7dfc-803a-4c7250255aab/items
Content-Type: application/json

{
  "productId": 4,
  "quantity": 1
}

### Remove item from sale
DELETE {{baseUrl}}/api/sales/019706b6-ecd1-7dfc-803a-4c7250255aab/items/4
Accept: application/json

### PATCH sale discount - Update only amount (partial update)
PATCH {{baseUrl}}/api/sales/discount
Content-Type: application/json

{
  "saleId": "019706b6-ecd1-7dfc-803a-4c7250255aab",
  "amount": 50.0
}

### PATCH sale discount - Update only currency (partial update)
PATCH {{baseUrl}}/api/sales/discount
Content-Type: application/json

{
  "saleId": "019706b6-ecd1-7dfc-803a-4c7250255aab",
  "currency": "EUR"
}

### PATCH sale discount - Update both amount and currency (partial update)
PATCH {{baseUrl}}/api/sales/discount
Content-Type: application/json

{
  "saleId": "019706b6-ecd1-7dfc-803a-4c7250255aab",
  "amount": 75.0,
  "currency": "USD"
}

### Complete sale (Updated to POST)
POST {{baseUrl}}/api/sales/complete
Content-Type: application/json

{
  "saleId": "019706b6-ecd1-7dfc-803a-4c7250255aab",
  "currency": "USD"
}

### Cancel sale (Updated to POST)
POST {{baseUrl}}/api/sales/cancel
Content-Type: application/json

{
  "saleId": "019706b6-ecd1-7dfc-803a-4c7250255aab",
  "reason": "Customer requested cancellation"
}

### ===========================================
### INVENTORY API
### ===========================================

### Create new inventory
POST {{baseUrl}}/api/inventory
Content-Type: application/json

{
  "balance": 10000.0,
  "currency": "USD",
  "recordDate": "2025-05-25T00:00:00Z"
}

### Get current inventory
GET {{baseUrl}}/api/inventory/current
Accept: application/json

### Add item to inventory
POST {{baseUrl}}/api/inventory/items
Content-Type: application/json

{
  "productId": 1,
  "quantity": 50,
  "actualPrice": 10.0,
  "sellingPrice": 25.0,
  "currency": "USD",
  "threshold": 5
}

### Waste inventory item (Updated to POST)
POST {{baseUrl}}/api/inventory/items/waste
Content-Type: application/json

{
  "productId": 1,
  "quantity": 3,
  "reason": "Damaged during transport"
}

### ===========================================
### PARTIAL UPDATE EXAMPLES
### ===========================================

### Example 1: Update only product name
PATCH {{baseUrl}}/api/products
Content-Type: application/json

{
  "id": 1,
  "name": "New Product Name"
}

### Example 2: Update only product tags
PATCH {{baseUrl}}/api/products
Content-Type: application/json

{
  "id": 1,
  "tagIds": [2, 4, 6]
}

### Example 3: Update only category name
PATCH {{baseUrl}}/api/categories
Content-Type: application/json

{
  "id": 1,
  "name": "Electronics & Gadgets"
}

### Example 4: Update only tag name
PATCH {{baseUrl}}/api/tags
Content-Type: application/json

{
  "id": 1,
  "name": "Premium Quality"
}

### Example 5: Update only sale discount amount
PATCH {{baseUrl}}/api/sales/discount
Content-Type: application/json

{
  "saleId": "019706b6-ecd1-7dfc-803a-4c7250255aab",
  "amount": 25.0
}

### ===========================================
### NOTES
### ===========================================
### 
### TRUE PATCH SEMANTICS IMPLEMENTED:
### - All PATCH endpoints now support optional properties
### - Only provided fields will be updated
### - Unchanged fields remain unmodified
### - Supports partial updates for Products, Categories, Tags, and Sale Discounts
### 
### PATCH Examples:
### - Products: Can update name, description, categoryId, tagIds independently
### - Categories: Can update name independently  
### - Tags: Can update name independently
### - Sale Discounts: Can update amount, currency independently
### 
### All endpoints maintain proper HTTP PATCH semantics
### Replace placeholder IDs with actual values from responses
###
