using Application.DTOs.SaleDTOs;
using Application.Interfaces;
using Domain.Enums;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Tags("Sales")]
public class SalesController(ISaleService saleService) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType<List<ViewSaleDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAllSales(CancellationToken cancellationToken)
    {
        var sales = await saleService.GetAllSalesAsync(cancellationToken);
        return Ok(sales);
    }

    [HttpGet("{id}")]
    [ProducesResponseType<ViewSaleDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetSaleById(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var sale = await saleService.GetSaleByIdAsync(id, cancellationToken);
            return Ok(sale);
        }
        catch (Exception ex)
        {
            return NotFound(ex.Message);
        }
    }

    [HttpGet("status/{status}")]
    [ProducesResponseType<List<ViewSaleDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetSalesByStatus(string status, CancellationToken cancellationToken)
    {
        var sales = await saleService.GetSalesByStatusAsync(Enum.Parse<SaleStatus>(status), cancellationToken);
        return Ok(sales);
    }

    [HttpGet("product/{productId}")]
    [ProducesResponseType<List<ViewSaleDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetSalesByProduct(int productId, CancellationToken cancellationToken)
    {
        var sales = await saleService.GetSalesByProductIdAsync(productId, cancellationToken);
        return Ok(sales);
    }

    [HttpGet("date-range")]
    [ProducesResponseType<List<ViewSaleDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetSalesByDateRange([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, CancellationToken cancellationToken)
    {
        var dateRange = new DateRangeDto(startDate, endDate);
        var sales = await saleService.GetSalesByDateRangeAsync(dateRange, cancellationToken);
        return Ok(sales);
    }

    [HttpPost]
    [ProducesResponseType<Guid>(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateSale(CreateSaleDto saleDto, CancellationToken cancellationToken)
    {
        try
        {
            var saleId = await saleService.CreateSaleAsync(saleDto, cancellationToken);
            return Created($"/api/sales/{saleId}", saleId);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("{id}/items")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddItemToSale(Guid id, SaleItemDto itemDto, CancellationToken cancellationToken)
    {
        try
        {
            await saleService.AddItemToSaleAsync(id, itemDto, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpDelete("{id}/items/{productId}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RemoveItemFromSale(Guid id, int productId, CancellationToken cancellationToken)
    {
        try
        {
            await saleService.RemoveItemFromSaleAsync(id, productId, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut("discount")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ApplyDiscount(ApplySaleDiscountDto discountDto, CancellationToken cancellationToken)
    {
        try
        {
            await saleService.ApplyDiscountAsync(discountDto, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("complete")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CompleteSale(CompleteSaleDto completeDto, CancellationToken cancellationToken)
    {
        try
        {
            await saleService.CompleteSaleAsync(completeDto, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("cancel")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CancelSale(CancelSaleDto cancelDto, CancellationToken cancellationToken)
    {
        try
        {
            await saleService.CancelSaleAsync(cancelDto, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
