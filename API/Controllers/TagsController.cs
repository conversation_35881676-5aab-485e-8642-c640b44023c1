using Application.DTOs;
using Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Tags("Tags")]
public class TagsController(ITagService tagService) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType<List<TagDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAllTags(CancellationToken cancellationToken)
    {
        var tags = await tagService.GetAllTagsAsync(cancellationToken);
        return Ok(tags);
    }

    [HttpGet("{id}")]
    [ProducesResponseType<TagDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetTagById(int id, CancellationToken cancellationToken)
    {
        try
        {
            var tag = await tagService.GetTagByIdAsync(id, cancellationToken);
            return Ok(tag);
        }
        catch (Exception ex)
        {
            return NotFound(ex.Message);
        }
    }

    [HttpGet("slug/{slug}")]
    [ProducesResponseType<TagDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetTagBySlug(string slug, CancellationToken cancellationToken)
    {
        try
        {
            var tag = await tagService.GetTagBySlugAsync(slug, cancellationToken);
            return Ok(tag);
        }
        catch (Exception ex)
        {
            return NotFound(ex.Message);
        }
    }

    [HttpPost]
    [ProducesResponseType<int>(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateTag([FromBody] string name, CancellationToken cancellationToken)
    {
        try
        {
            var tagId = await tagService.CreateTagAsync(name, cancellationToken);
            return Created($"/api/tags/{tagId}", tagId);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPatch]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> PatchTag(PatchTagDto tagDto, CancellationToken cancellationToken)
    {
        try
        {
            await tagService.PatchTagAsync(tagDto, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> DeleteTag(int id, CancellationToken cancellationToken)
    {
        try
        {
            var result = await tagService.DeleteTagAsync(id, cancellationToken);
            return result ? NoContent() : NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpGet("product/{productId}")]
    [ProducesResponseType<List<TagDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetTagsByProduct(int productId, CancellationToken cancellationToken)
    {
        var tags = await tagService.GetTagsByProductIdAsync(productId, cancellationToken);
        return Ok(tags);
    }
}
