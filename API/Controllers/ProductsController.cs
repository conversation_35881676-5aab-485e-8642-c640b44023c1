using Application.DTOs.ProductDTOs;
using Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Tags("Products")]
public class ProductsController(IProductService productService) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType<List<ViewProductDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAllProducts(CancellationToken cancellationToken)
    {
        var products = await productService.GetAllProductsAsync(cancellationToken);
        return Ok(products);
    }

    [HttpGet("{id}")]
    [ProducesResponseType<ViewProductDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetProductById(int id, CancellationToken cancellationToken)
    {
        try
        {
            var product = await productService.GetProductByIdAsync(id, cancellationToken);
            return Ok(product);
        }
        catch (Exception ex)
        {
            return NotFound(ex.Message);
        }
    }

    [HttpPost]
    [ProducesResponseType<int>(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateProduct(CreateProductDto productDto, CancellationToken cancellationToken)
    {
        try
        {
            var productId = await productService.CreateProductAsync(productDto, cancellationToken);
            return Created($"/api/products/{productId}", productId);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPatch]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> PatchProduct(PatchProductDto productDto, CancellationToken cancellationToken)
    {
        try
        {
            await productService.PatchProductAsync(productDto, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> DeleteProduct(int id, CancellationToken cancellationToken)
    {
        try
        {
            var result = await productService.DeleteProductAsync(id, cancellationToken);
            return result ? NoContent() : NotFound();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpGet("category/{categoryId}")]
    [ProducesResponseType<List<ViewProductDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetProductsByCategory(int categoryId, CancellationToken cancellationToken)
    {
        var products = await productService.GetProductsByCategoryIdAsync(categoryId, cancellationToken);
        return Ok(products);
    }

    [HttpGet("tag/{tagId}")]
    [ProducesResponseType<List<ViewProductDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetProductsByTag(int tagId, CancellationToken cancellationToken)
    {
        var products = await productService.GetProductsByTagIdAsync(tagId, cancellationToken);
        return Ok(products);
    }

    [HttpGet("search")]
    [ProducesResponseType<List<ViewProductDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> SearchProducts([FromQuery] string term, CancellationToken cancellationToken)
    {
        var products = await productService.SearchProductsAsync(term, cancellationToken);
        return Ok(products);
    }
}
