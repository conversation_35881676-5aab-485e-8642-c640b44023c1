using Application.DTOs.InventoryDTOs;
using Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Tags("Inventory")]
public class InventoryController(IInventoryService inventoryService) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType<Guid>(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateInventory(CreateInventoryDto inventoryDto, CancellationToken cancellationToken)
    {
        try
        {
            var inventoryId = await inventoryService.CreateInventoryAsync(inventoryDto, cancellationToken);
            return Created($"/api/inventory/{inventoryId}", inventoryId);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpGet("current")]
    [ProducesResponseType<ViewInventoryDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetCurrentInventory(CancellationToken cancellationToken)
    {
        try
        {
            var inventory = await inventoryService.GetCurrentInventoryAsync(cancellationToken);
            return Ok(inventory);
        }
        catch (Exception ex)
        {
            return NotFound(ex.Message);
        }
    }

    [HttpPost("items")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddItem(AddInventoryItemDto request, CancellationToken cancellationToken)
    {
        try
        {
            await inventoryService.AddItemAsync(request, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("items/waste")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> WasteItem(WasteInventoryItemDto request, CancellationToken cancellationToken)
    {
        try
        {
            await inventoryService.WasteItemAsync(request, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
