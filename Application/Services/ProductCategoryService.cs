using Application.DTOs;
using Application.Interfaces;
using Domain.Interfaces;
using Domain.Managers;

namespace Application.Services;

public class ProductCategoryService(
    IProductCategoryRepository categoryRepository,
    IProductCategoryManager productCategoryManager,
    IUnitOfWork unitOfWork) : IProductCategoryService
{
    public async Task<ProductCategoryDto> GetCategoryByIdAsync(int categoryId, CancellationToken cancellationToken)
    {
        var category = await categoryRepository.GetByIdOrDefaultAsync(categoryId, cancellationToken)
            .ThrowIfNull(categoryId);

        return ProductCategoryDto.FromDomain(category);
    }

    public async Task<int> CreateCategoryAsync(string name, CancellationToken cancellationToken)
    {
        var category = await productCategoryManager.CreateAsync(name, cancellationToken);

        await categoryRepository.AddAsync(category, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return category.Id;
    }

    public async Task UpdateCategoryAsync(ProductCategoryDto productCategoryDto,
        CancellationToken cancellationToken = default)
    {
        var category = await categoryRepository.GetByIdOrDefaultAsync(productCategoryDto.Id, cancellationToken)
            .ThrowIfNull(productCategoryDto.Id);

        await productCategoryManager.UpdateAsync(category, productCategoryDto.Name, cancellationToken);

        await categoryRepository.UpdateAsync(category, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task PatchCategoryAsync(PatchProductCategoryDto productCategoryDto,
        CancellationToken cancellationToken = default)
    {
        var category = await categoryRepository.GetByIdOrDefaultAsync(productCategoryDto.Id, cancellationToken)
            .ThrowIfNull(productCategoryDto.Id);

        if (productCategoryDto.Name is null)
        {
            return;
        }
        await productCategoryManager.UpdateAsync(category, productCategoryDto.Name, cancellationToken);
            
        await categoryRepository.UpdateAsync(category, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task<bool> DeleteCategoryAsync(int categoryId, CancellationToken cancellationToken)
    {
        await productCategoryManager.TryToDeleteAsync(categoryId, cancellationToken);

        var success = await categoryRepository.DeleteAsync(categoryId, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return success;
    }

    public async Task<List<ProductCategoryDto>> GetAllCategoriesAsync(CancellationToken cancellationToken)
    {
        var categories = await categoryRepository.GetAllAsync(cancellationToken);
        return categories.Select(ProductCategoryDto.FromDomain).ToList();
    }

    public async Task<bool> IsCategoryInUseAsync(int categoryId, CancellationToken cancellationToken = default)
    {
        return await categoryRepository.IsCategoryInUseAsync(categoryId, cancellationToken);
    }

    public async Task<bool> IsCategoryNameUniqueAsync(string name, CancellationToken cancellationToken = default)
    {
        return await categoryRepository.IsCategoryNameUniqueAsync(name, cancellationToken);
    }
}