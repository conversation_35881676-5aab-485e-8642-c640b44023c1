using Application.DTOs.SaleDTOs;
using Application.Interfaces;
using Domain.Entities.InventoryAggregate;
using Domain.Entities.ProductAggregate;
using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Domain.Interfaces;
using Domain.Managers;
using Domain.ValueObjects;

namespace Application.Services;

public class SaleService(
    ISaleRepository saleRepository,
    IProductRepository productRepository,
    ISaleManager domainSaleManager,
    IInventoryManager inventoryManager,
    IUnitOfWork unitOfWork) : ISaleService
{
    private async Task<SaleItem> ConvertSaleItemDto(SaleItemDto saleItemDto,
        CancellationToken cancellationToken = default, Inventory? inventory = null, Product? product = null)
    {
        product ??= await productRepository.GetByIdOrDefaultAsync(saleItemDto.ProductId, cancellationToken)
            .ThrowIfNull(saleItemDto.ProductId);
        
        inventory ??= await inventoryManager.GetCurrentInventoryAsync(cancellationToken);
        return new SaleItem(saleItemDto.ProductId, product.Name, inventory.GetItemPrice(product.Id),
            saleItemDto.Quantity);
    }

    public async Task<Guid> CreateSaleAsync(CreateSaleDto saleDto, CancellationToken cancellationToken = default)
    {
        var inventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);
        List<SaleItem> saleItems = [];
        foreach (var saleItemDto in saleDto.Items ?? [])
        {
            var saleItem = await ConvertSaleItemDto(saleItemDto, cancellationToken, inventory: inventory);
            saleItems.Add(saleItem);
        }

        Money? discount = saleDto.DiscountAmount == 0 || saleDto.Currency is null
            ? null
            : new Money(saleDto.DiscountAmount, saleDto.Currency);

        var sale = await domainSaleManager.CreateSaleAsync(saleItems, discount, cancellationToken);

        await saleRepository.AddAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
        return sale.Id;
    }

    public async Task<List<ViewSaleDto>> GetAllSalesAsync(CancellationToken cancellationToken = default)
    {
        var sales = await saleRepository.GetAllAsync(cancellationToken);
        return sales.Select(ViewSaleDto.FromDomain).ToList();
    }

    public async Task<List<ViewSaleDto>> GetSalesByDateRangeAsync(DateRangeDto dateRange,
        CancellationToken cancellationToken = default)
    {
        var sales = await saleRepository.GetSalesByDateRangeAsync(dateRange.StartDate, dateRange.EndDate, cancellationToken);
        return sales.Select(ViewSaleDto.FromDomain).ToList();
    }

    public async Task<List<ViewSaleDto>> GetSalesByStatusAsync(SaleStatus status,
        CancellationToken cancellationToken = default)
    {
        var sales = await saleRepository.GetSalesByStatusAsync(status, cancellationToken);
        return sales.Select(ViewSaleDto.FromDomain).ToList();
    }

    public async Task<List<ViewSaleDto>> GetSalesByProductIdAsync(int productId,
        CancellationToken cancellationToken = default)
    {
        var sales = await saleRepository.GetSalesByProductIdAsync(productId, cancellationToken);
        return sales.Select(ViewSaleDto.FromDomain).ToList();
    }

    public async Task<ViewSaleDto> GetSaleByIdAsync(Guid saleId, CancellationToken cancellationToken = default)
    {
        var sale = await saleRepository.GetByIdOrDefaultAsync(saleId, cancellationToken)
            .ThrowIfNull(saleId);

        return ViewSaleDto.FromDomain(sale);
    }

    public async Task AddItemToSaleAsync(Guid saleId, SaleItemDto item, CancellationToken cancellationToken = default)
    {
        var sale = await saleRepository.GetByIdOrDefaultAsync(saleId, cancellationToken)
            .ThrowIfNull(saleId);
        
        var product = await productRepository.GetByIdOrDefaultAsync(item.ProductId, cancellationToken)
            .ThrowIfNull(item.ProductId);
        
        await domainSaleManager.AddItemAsync(sale, product.Id, product.Name, item.Quantity, cancellationToken);

        await saleRepository.UpdateAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task RemoveItemFromSaleAsync(Guid saleId, int productId, CancellationToken cancellationToken = default)
    {
        var sale = await saleRepository.GetByIdOrDefaultAsync(saleId, cancellationToken)
            .ThrowIfNull(saleId);
        
        await domainSaleManager.RemoveItemAsync(sale, productId, cancellationToken);

        await saleRepository.UpdateAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task ApplyDiscountAsync(ApplySaleDiscountDto discountDto, CancellationToken cancellationToken = default)
    {
        var sale = await saleRepository.GetByIdOrDefaultAsync(discountDto.SaleId, cancellationToken)
            .ThrowIfNull(discountDto.SaleId);

        var currency = discountDto.Currency;
        var discount = new Money(discountDto.Amount, currency);
        sale.ApplyDiscount(discount);

        await saleRepository.UpdateAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task CompleteSaleAsync(CompleteSaleDto completeDto,
        CancellationToken cancellationToken = default)
    {
        var sale = await saleRepository.GetByIdOrDefaultAsync(completeDto.SaleId, cancellationToken)
            .ThrowIfNull(completeDto.SaleId);
        
        var tagetCurrencyCode = Enum.Parse<CurrencyCode>(completeDto.Currency, ignoreCase: true);
        
        await domainSaleManager.CompleteSaleAsync(sale, tagetCurrencyCode, cancellationToken);

        await saleRepository.UpdateAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task CancelSaleAsync(CancelSaleDto cancelDto, CancellationToken cancellationToken = default)
    {
        var sale = await saleRepository.GetByIdOrDefaultAsync(cancelDto.SaleId, cancellationToken)
            .ThrowIfNull(cancelDto.SaleId);
        
        await domainSaleManager.CancelSaleAsync(sale, cancelDto.Reason, cancellationToken);
        
        await saleRepository.UpdateAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}