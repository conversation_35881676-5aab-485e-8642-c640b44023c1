using Application.DTOs.InventoryDTOs;
using Application.Interfaces;
using Domain.Entities.InventoryAggregate;
using Domain.Entities.ProductAggregate;
using Domain.Interfaces;
using Domain.Managers;

namespace Application.Services;

public class InventoryService(
    IInventoryRepository inventoryRepository,
    IProductRepository productRepository,
    IInventoryManager inventoryManager,
    IUnitOfWork unitOfWork) : IInventoryService
{
    public async Task<Guid> CreateInventoryAsync(CreateInventoryDto inventoryDto,
        CancellationToken cancellationToken = default)
    {
        var inventory = new Inventory(inventoryDto.ToMoney(), inventoryDto.RecordDate);

        await inventoryRepository.AddAsync(inventory, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return inventory.Id;
    }

    public async Task AddItemAsync(AddInventoryItemDto itemDto, CancellationToken cancellationToken = default)
    {
        var inventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);
        await productRepository.GetByIdOrDefaultAsync(itemDto.ProductId, cancellationToken)
            .ThrowIfNull(itemDto.ProductId);

        inventory.AddItem(itemDto.ProductId, itemDto.Quantity, itemDto.ToActualPriceMoney(),
            itemDto.ToSellingPriceMoney(), itemDto.Threshold);

        await inventoryRepository.UpdateAsync(inventory, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task WasteItemAsync(WasteInventoryItemDto wasteDto, CancellationToken cancellationToken = default)
    {
        var inventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);

        inventory.WasteItem(wasteDto.ProductId, wasteDto.Quantity, wasteDto.Reason);

        await inventoryRepository.UpdateAsync(inventory, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateItemPriceAsync(UpdateInventoryItemPriceDto priceDto,
        CancellationToken cancellationToken = default)
    {
        var inventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);

        inventory.UpdateItemPrice(priceDto.ProductId, priceDto.ToActualPriceMoney(), priceDto.ToSellingPriceMoney());

        await inventoryRepository.UpdateAsync(inventory, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task AddItemQuantityAsync(int productId, int quantity, CancellationToken cancellationToken = default)
    {
        var inventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);

        inventory.AddItemQuantity(productId, quantity);

        await inventoryRepository.UpdateAsync(inventory, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task DeActivateItemAsync(int productId, CancellationToken cancellationToken = default)
    {
        var inventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);

        inventory.DeActivateItem(productId);

        await inventoryRepository.UpdateAsync(inventory, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task<ViewInventoryDto> GetCurrentInventoryAsync(CancellationToken cancellationToken = default)
    {
        var inventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);

        var productIds = inventory.Items.Select(i => i.ProductId).ToList();

        List<Product> products = [];

        foreach (var productId in productIds)
        {
            var product = await productRepository.GetByIdOrDefaultAsync(productId, cancellationToken)
                .ThrowIfNull(productId);
            products.Add(product);
        }

        return ViewInventoryDto.FromDomain(inventory, products);
    }

    public async Task<List<ViewInventoryItemDto>> GetOutOfStockItemsAsync(CancellationToken cancellationToken = default)
    {
        var items = await inventoryRepository.GetOutOfStockItemsAsync(cancellationToken);

        List<ViewInventoryItemDto> products = [];

        foreach (var item in items)
        {
            var product = await productRepository.GetByIdOrDefaultAsync(item.ProductId, cancellationToken)
                .ThrowIfNull(item.ProductId);
            products.Add(ViewInventoryItemDto.FromDomain(item, product));
        }

        return products;
    }

    public async Task<List<ViewInventoryItemPriceDto>> GetPriceHistoryForProductAsync(int productId,
        CancellationToken cancellationToken = default)
    {
        var priceHistory = await inventoryRepository.GetPriceHistoryForProductAsync(productId, cancellationToken);

        return priceHistory.Select(ViewInventoryItemPriceDto.FromDomain).ToList();
    }

    public async Task<List<ViewWasteRecordDto>> GetWasteRecordsForProductAsync(int productId,
        CancellationToken cancellationToken = default)
    {
        var wasteRecords = await inventoryRepository.GetWasteRecordsForProductAsync(productId, cancellationToken);


        return wasteRecords.Select(ViewWasteRecordDto.FromDomain).ToList();
    }
}