using Application.DTOs.ProductDTOs;
using Application.Interfaces;
using Domain.Entities.ProductAggregate;
using Domain.Interfaces;

namespace Application.Services;

public class ProductService(
    IProductRepository productRepository,
    ITagRepository tagRepository,
    IProductCategoryRepository categoryRepository,
    IUnitOfWork unitOfWork) : IProductService
{
    public async Task<List<ViewProductDto>> GetAllProductsAsync(CancellationToken cancellationToken = default)
    {
        var products = await productRepository.GetAllAsync(cancellationToken);
        List<ViewProductDto> productDtos = [];

        foreach (var product in products)
        {
            productDtos.Add(await GetViewProductDtoAsync(product, cancellationToken));
        }

        return productDtos;
    }

    public async Task<ViewProductDto> GetProductByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        var product = await productRepository.GetByIdOrDefaultAsync(id, cancellationToken)
            .ThrowIfNull(id);
        
        return await GetViewProductDtoAsync(product, cancellationToken);
    }

    private async Task CategoryExists(int id, CancellationToken cancellationToken = default)
    {
        _ = await categoryRepository.GetByIdOrDefaultAsync(id, cancellationToken)
            .ThrowIfNull(id);
    }

    private async Task TagExists(int id, CancellationToken cancellationToken = default)
    {
        _ = await tagRepository.GetByIdOrDefaultAsync(id, cancellationToken)
            .ThrowIfNull(id);
    }

    public async Task<int> CreateProductAsync(CreateProductDto productDto, CancellationToken cancellationToken = default)
    {
        await CategoryExists(productDto.CategoryId, cancellationToken);
        foreach (var tagId in productDto.TagIds ?? [])
        {
            await TagExists(tagId, cancellationToken);
        }
        var product = new Product(productDto.Name, productDto.Description, productDto.CategoryId, productDto.TagIds ?? []);

        await productRepository.AddAsync(product, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return product.Id;
    }

    public async Task UpdateProductAsync(UpdateProductDto productDto, CancellationToken cancellationToken = default)
    {
        var product = await productRepository.GetByIdOrDefaultAsync(productDto.Id, cancellationToken)
            .ThrowIfNull(productDto.Id);
        
        await CategoryExists(productDto.CategoryId, cancellationToken);
        foreach (var tagId in productDto.TagIds ?? [])
        {
            await TagExists(tagId, cancellationToken);
        }
        
        product.UpdateDetails(productDto.Name, productDto.Description, productDto.CategoryId);

        await productRepository.UpdateAsync(product, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task PatchProductAsync(PatchProductDto productDto, CancellationToken cancellationToken = default)
    {
        var product = await productRepository.GetByIdOrDefaultAsync(productDto.Id, cancellationToken)
            .ThrowIfNull(productDto.Id);
        
        if (productDto.CategoryId is not null)
        {
            await CategoryExists(productDto.CategoryId.Value, cancellationToken);
        }
        
        if (productDto.TagIds is not null)
        {
            foreach (var tagId in productDto.TagIds)
            {
                await TagExists(tagId, cancellationToken);
            }
        }
        
        if (productDto.Name is not null)
        {
            product.UpdateDetails(productDto.Name, product.Description, product.CategoryId);
        }
        
        if (productDto.Description is not null)
        {
            product.UpdateDetails(product.Name, productDto.Description, product.CategoryId);
        }
        
        if (productDto.CategoryId is not null)
        {
            product.UpdateDetails(product.Name, product.Description, productDto.CategoryId.Value);
        }
        
        if (productDto.TagIds is not null)
        {
            product.ClearTags();
            foreach (var tagId in productDto.TagIds)
            {
                product.AddTag(tagId);
            }
        }

        await productRepository.UpdateAsync(product, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task<bool> DeleteProductAsync(int productId, CancellationToken cancellationToken = default)
    {
        var success = await productRepository.DeleteAsync(productId, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return success;
    }

    public async Task AddTagToProductAsync(int productId, int tagId, CancellationToken cancellationToken = default)
    {
        var product = await productRepository.GetByIdOrDefaultAsync(productId, cancellationToken)
            .ThrowIfNull(productId);

        await TagExists(tagId, cancellationToken);

        product.AddTag(tagId);

        await productRepository.UpdateAsync(product, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task RemoveTagFromProductAsync(int productId, int tagId, CancellationToken cancellationToken = default)
    {
        var product = await productRepository.GetByIdOrDefaultAsync(productId, cancellationToken)
            .ThrowIfNull(productId);
        
        product.RemoveTag(tagId);

        await productRepository.UpdateAsync(product, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task ClearTagsAsync(int productId, CancellationToken cancellationToken = default)
    {
        var product = await productRepository.GetByIdOrDefaultAsync(productId, cancellationToken)
            .ThrowIfNull(productId);
        
        product.ClearTags();

        await productRepository.UpdateAsync(product, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }


    public async Task<List<ViewProductDto>> GetProductsByCategoryIdAsync(int categoryId,
        CancellationToken cancellationToken)
    {
        var products = await productRepository.GetProductsByCategoryIdAsync(categoryId, cancellationToken);
        List<ViewProductDto> productDtos = [];

        foreach (var product in products)
        {
            productDtos.Add(await GetViewProductDtoAsync(product, cancellationToken));
        }

        return productDtos;
    }

    public async Task<List<ViewProductDto>> GetProductsByTagIdAsync(int tagId, CancellationToken cancellationToken)
    {
        var products = await productRepository.GetProductsByTagIdAsync(tagId, cancellationToken);
        List<ViewProductDto> productDtos = [];

        foreach (var product in products)
        {
            productDtos.Add(await GetViewProductDtoAsync(product, cancellationToken));
        }

        return productDtos;
    }

    public async Task<List<ViewProductDto>> SearchProductsAsync(string searchTerm,
        CancellationToken cancellationToken)
    {
        var products = await productRepository.SearchProductsAsync(searchTerm, cancellationToken);
        List<ViewProductDto> productDtos = [];

        foreach (var product in products)
        {
            productDtos.Add(await GetViewProductDtoAsync(product, cancellationToken));
        }

        return productDtos;
    }

    private async Task<ViewProductDto> GetViewProductDtoAsync(Product product, CancellationToken cancellationToken = default)
    {
        var category = await categoryRepository.GetByIdOrDefaultAsync(product.CategoryId, cancellationToken)
            .ThrowIfNull(product.CategoryId);
        
        var tags = await tagRepository.GetTagsByProductIdAsync(product.Id, cancellationToken);
        
        return ViewProductDto.FromDomain(product, category, tags);
    }
}