using Application.DTOs;
using Application.Interfaces;
using Domain.Interfaces;
using Domain.Managers;

namespace Application.Services;

public class TagService(ITagRepository tagRepository, ITagManager tagManager, IUnitOfWork unitOfWork) : ITagService
{
    public async Task<int> CreateTagAsync(string name, CancellationToken cancellationToken = default)
    {
        var tag = await tagManager.CreateTagAsync(name, cancellationToken);

        await tagRepository.AddAsync(tag, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return tag.Id;
    }

    public async Task UpdateTagAsync(TagDto tagDto, CancellationToken cancellationToken = default)
    {
        var tag = await tagRepository.GetByIdOrDefaultAsync(tagDto.Id, cancellationToken)
            .ThrowIfNull(tagDto.Id);

        await tagManager.UpdateTagAsync(tag, tagDto.Name, cancellationToken);

        await tagRepository.UpdateAsync(tag, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task PatchTagAsync(PatchTagDto tagDto, CancellationToken cancellationToken = default)
    {
        var tag = await tagRepository.GetByIdOrDefaultAsync(tagDto.Id, cancellationToken)
            .ThrowIfNull(tagDto.Id);

        if (tagDto.Name is null)
        {
            return;
        }

        await tagManager.UpdateTagAsync(tag, tagDto.Name, cancellationToken);

        await tagRepository.UpdateAsync(tag, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task<bool> DeleteTagAsync(int tagId, CancellationToken cancellationToken = default)
    {
        var success = await tagRepository.DeleteAsync(tagId, cancellationToken);

        await unitOfWork.SaveChangesAsync(cancellationToken);
        return success;
    }

    public async Task<TagDto> GetTagByIdAsync(int tagId, CancellationToken cancellationToken = default)
    {
        var tag = await tagRepository.GetByIdOrDefaultAsync(tagId, cancellationToken)
            .ThrowIfNull(tagId);

        return TagDto.FromDomain(tag);
    }

    public async Task<TagDto> GetTagBySlugAsync(string slug, CancellationToken cancellationToken = default)
    {
        var tag = await tagRepository.GetBySlugAsync(slug, cancellationToken);
        return TagDto.FromDomain(tag);
    }

    public async Task<List<TagDto>> GetAllTagsAsync(CancellationToken cancellationToken = default)
    {
        var tags = await tagRepository.GetAllAsync(cancellationToken);
        return tags.Select(TagDto.FromDomain).ToList();
    }

    public async Task<List<TagDto>> GetTagsByProductIdAsync(int productId,
        CancellationToken cancellationToken = default)
    {
        var tags = await tagRepository.GetTagsByProductIdAsync(productId, cancellationToken);
        return tags.Select(TagDto.FromDomain).ToList();
    }

    public async Task<bool> IsSlugUniqueAsync(string slug, CancellationToken cancellationToken = default)
    {
        return await tagRepository.IsSlugUniqueAsync(slug, cancellationToken);
    }
}