using System.Diagnostics.CodeAnalysis;
using Application.Exceptions;

namespace Application;

public static class EntityExtensions
{
    [return: NotNull]
    public static T ThrowIfNull<T>(this T? entity, object id)
    {
        if (entity is null)
        { 
            throw new EntityNotFoundApplicationException<T>(id);
        }
        return entity;
    }
    
    [return: NotNull]
    public static async Task<T> ThrowIfNull<T>(this Task<T?> entityTask, object id)
    {
        var entity = await entityTask.ConfigureAwait(false);

        if (entity is null)
        {
            throw new EntityNotFoundApplicationException<T>(id);
        }
        return entity;
    }
}