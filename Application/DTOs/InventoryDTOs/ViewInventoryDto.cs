using Domain.Entities.InventoryAggregate;
using Domain.Entities.ProductAggregate;

namespace Application.DTOs.InventoryDTOs;

public record ViewInventoryDto(
    Guid Id,
    decimal Balance,
    string Currency,
    DateTime CreatedAt,
    List<ViewInventoryItemDto> Items)
{
    public static ViewInventoryDto FromDomain(Inventory inventory, List<Product> products)
    {
        return new ViewInventoryDto
        (
            Id: inventory.Id,
            CreatedAt: inventory.CreatedAt,
            Balance: inventory.Balance.Amount,
            Currency: inventory.Balance.CurrencyCode.ToString(),
            Items: inventory.Items
                .Select(item => ViewInventoryItemDto.FromDomain(
                    item,
                    products.First(x => x.Id == item.ProductId)))
                .ToList()
        );
    }
}