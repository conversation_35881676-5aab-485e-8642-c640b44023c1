using Domain.Entities.InventoryAggregate;
using Domain.Entities.ProductAggregate;

namespace Application.DTOs.InventoryDTOs;

public record ViewInventoryItemDto(
    Guid Id,
    int ProductId,
    string ProductName,
    int Quantity,
    int ReservedQuantity,
    int Threshold,
    int SoldQuantity,
    int TotalSoldQuantity,
    int TotalWastedQuantity,
    string Status,
    decimal ActualPrice,
    decimal SellingPrice,
    string Currency)
{
    public int AvailableQuantity => Quantity - ReservedQuantity;

    public static ViewInventoryItemDto FromDomain(InventoryItem item, Product product)
    {
        return new ViewInventoryItemDto
        (
            Id: item.Id,
            ProductId: item.ProductId,
            ProductName: product.Name,
            Quantity: item.Quantity,
            ReservedQuantity: item.ReservedQuantity,
            Threshold: item.Threshold,
            SoldQuantity: item.SoldQuantity,
            TotalSoldQuantity: item.TotalSoldQuantity,
            TotalWastedQuantity: item.TotalWastedQuantity,
            Status: item.Status.ToString(),
            ActualPrice: item.Price.ActualPrice.Amount,
            SellingPrice: item.Price.SellingPrice.Amount,
            Currency: item.Price.ActualPrice.CurrencyCode.ToString()
        );
    }
}