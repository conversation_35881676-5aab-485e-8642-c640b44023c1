using Domain.Entities.InventoryAggregate;

namespace Application.DTOs.InventoryDTOs;

public record ViewInventoryItemPriceDto(
    Guid Id,
    decimal ActualPrice,
    decimal SellingPrice,
    string Currency,
    DateTime CreatedAt)
{
    public static ViewInventoryItemPriceDto FromDomain(InventoryItemPrice item)
    {
        return new ViewInventoryItemPriceDto
        (
            Id: item.Id,
            ActualPrice: item.ActualPrice.Amount,
            SellingPrice: item.SellingPrice.Amount,
            Currency: item.ActualPrice.CurrencyCode.ToString(),
            CreatedAt: item.CreatedAt
        );
    }
}