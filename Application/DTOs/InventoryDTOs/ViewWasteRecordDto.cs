using Domain.Entities.InventoryAggregate;

namespace Application.DTOs.InventoryDTOs;

public record ViewWasteRecordDto(Guid Id, int Quantity, string Reason, DateTime RecordedDate)
{
    public static ViewWasteRecordDto FromDomain(WasteRecord item)
    {
        return new ViewWasteRecordDto
        (
            Id: item.Id,
            Quantity: item.Quantity,
            Reason: item.Reason,
            RecordedDate: item.RecordedDate
        );
    }
}