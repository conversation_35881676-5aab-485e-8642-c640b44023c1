using Domain.Entities.SaleAggregate;

namespace Application.DTOs.SaleDTOs;

public record ViewSaleDto(
    Guid Id,
    List<ViewSaleItemDto> Items,
    decimal TotalAmount,
    decimal DiscountAmount,
    decimal FinalAmount,
    string Currency,
    DateTime CreatedAt,
    string Status,
    string? CancellationReason)
{
    public static ViewSaleDto FromDomain(Sale sale)
    {
        return new ViewSaleDto
        (
            Id: sale.Id,
            Items: sale.Items.Select(ViewSaleItemDto.FromDomain).ToList(),
            TotalAmount: sale.TotalAmount.Amount,
            DiscountAmount: sale.DiscountAmount.Amount,
            FinalAmount: sale.FinalAmount.Amount,
            Currency: sale.TotalAmount.CurrencyCode.ToString(),
            CreatedAt: sale.CreatedAt,
            Status: sale.Status.ToString(),
            CancellationReason: sale.CancellationReason
        );
    }
}