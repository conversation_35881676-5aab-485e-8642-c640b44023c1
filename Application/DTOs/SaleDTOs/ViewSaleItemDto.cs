using Domain.Entities.SaleAggregate;

namespace Application.DTOs.SaleDTOs;

public record ViewSaleItemDto(
    int ProductId,
    string ProductName,
    int Quantity,
    decimal UnitPrice,
    string Currency)
{
    public decimal TotalPrice => UnitPrice * Quantity;
    
    public static ViewSaleItemDto FromDomain(SaleItem item)
    {
        return new ViewSaleItemDto
        (
            ProductId: item.ProductId,
            ProductName: item.ProductName,
            Quantity: item.Quantity,
            UnitPrice: item.Price.Amount,
            Currency: item.Price.CurrencyCode.ToString()
        );
    }
}