using Domain.Entities.ProductAggregate;
using Domain.Entities.ProductCategoryAggregate;
using Domain.Entities.TagAggregate;

namespace Application.DTOs.ProductDTOs;

public record ViewProductDto(int Id, string Name, string Description, string CategoryName, List<string> Tags)
{
    public static ViewProductDto FromDomain(Product product, ProductCategory category, List<Tag> tags)
    {
        return new ViewProductDto
        (
            Id: product.Id,
            Name: product.Name,
            Description: product.Description,
            CategoryName: category.Name,
            Tags: tags.Select(x => x.Name).ToList()
        );
    }
}