using Application.DTOs;

namespace Application.Interfaces;

public interface IProductCategoryService
{
    public Task<int> CreateCategoryAsync(string name, CancellationToken cancellationToken = default);
    public Task UpdateCategoryAsync(ProductCategoryDto productCategoryDto, CancellationToken cancellationToken = default);
    public Task PatchCategoryAsync(PatchProductCategoryDto productCategoryDto, CancellationToken cancellationToken = default);
    public Task<bool> DeleteCategoryAsync(int categoryId, CancellationToken cancellationToken = default);
    public Task<ProductCategoryDto> GetCategoryByIdAsync(int categoryId, CancellationToken cancellationToken = default);
    public Task<List<ProductCategoryDto>> GetAllCategoriesAsync(CancellationToken cancellationToken = default);
    public Task<bool> IsCategoryInUseAsync(int categoryId, CancellationToken cancellationToken = default);
    public Task<bool> IsCategoryNameUniqueAsync(string name, CancellationToken cancellationToken = default);
}