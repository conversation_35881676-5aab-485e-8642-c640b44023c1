using Application.DTOs;

namespace Application.Interfaces;

public interface ITagService
{
    public Task<int> CreateTagAsync(string name, CancellationToken cancellationToken = default);
    public Task UpdateTagAsync(TagDto tagDto, CancellationToken cancellationToken = default);
    public Task PatchTagAsync(PatchTagDto tagDto, CancellationToken cancellationToken = default);
    public Task<bool> DeleteTagAsync(int tagId, CancellationToken cancellationToken = default);
    public Task<TagDto> GetTagByIdAsync(int tagId, CancellationToken cancellationToken = default);
    public Task<TagDto> GetTagBySlugAsync(string slug, CancellationToken cancellationToken = default);
    public Task<List<TagDto>> GetAllTagsAsync(CancellationToken cancellationToken = default);
    public Task<List<TagDto>> GetTagsByProductIdAsync(int productId, CancellationToken cancellationToken = default);
    public Task<bool> IsSlugUniqueAsync(string slug, CancellationToken cancellationToken = default);
}