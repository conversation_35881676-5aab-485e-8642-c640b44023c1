using Application.DTOs.SaleDTOs;
using Domain.Enums;

namespace Application.Interfaces;

public interface ISaleService
{
    public Task<Guid> CreateSaleAsync(CreateSaleDto saleDto, CancellationToken cancellationToken = default);
    public Task<List<ViewSaleDto>> GetAllSalesAsync(CancellationToken cancellationToken = default);

    public Task<List<ViewSaleDto>> GetSalesByDateRangeAsync(DateRangeDto dateRange,
        CancellationToken cancellationToken = default);

    public Task<List<ViewSaleDto>> GetSalesByStatusAsync(SaleStatus status,
        CancellationToken cancellationToken = default);

    public Task<List<ViewSaleDto>> GetSalesByProductIdAsync(int productId,
        CancellationToken cancellationToken = default);

    public Task<ViewSaleDto> GetSaleByIdAsync(Guid saleId, CancellationToken cancellationToken = default);
    public Task AddItemToSaleAsync(Guid saleId, SaleItemDto item, CancellationToken cancellationToken = default);
    public Task RemoveItemFromSaleAsync(Guid saleId, int productId, CancellationToken cancellationToken = default);

    public Task ApplyDiscountAsync(ApplySaleDiscountDto discountDto,
        CancellationToken cancellationToken = default);

    public Task CompleteSaleAsync(CompleteSaleDto completeDto,
        CancellationToken cancellationToken = default);

    public Task CancelSaleAsync(CancelSaleDto cancelDto, CancellationToken cancellationToken = default);
}