using Application.DTOs.ProductDTOs;

namespace Application.Interfaces;

public interface IProductService
{
    public Task<int> CreateProductAsync(CreateProductDto productDto, CancellationToken cancellationToken = default);
    public Task UpdateProductAsync(UpdateProductDto productDto, CancellationToken cancellationToken = default);
    public Task PatchProductAsync(PatchProductDto productDto, CancellationToken cancellationToken = default);
    public Task<bool> DeleteProductAsync(int productId, CancellationToken cancellationToken = default);
    public Task<ViewProductDto> GetProductByIdAsync(int productId, CancellationToken cancellationToken = default);
    public Task<List<ViewProductDto>> GetAllProductsAsync(CancellationToken cancellationToken = default);

    public Task<List<ViewProductDto>> GetProductsByCategoryIdAsync(int categoryId,
        CancellationToken cancellationToken = default);

    public Task<List<ViewProductDto>> GetProductsByTagIdAsync(int tagId, CancellationToken cancellationToken = default);

    public Task<List<ViewProductDto>> SearchProductsAsync(string searchTerm,
        CancellationToken cancellationToken = default);

    public Task AddTagToProductAsync(int productId, int tagId, CancellationToken cancellationToken = default);
    public Task RemoveTagFromProductAsync(int productId, int tagId, CancellationToken cancellationToken = default);
    public Task ClearTagsAsync(int productId, CancellationToken cancellationToken = default);
}