using Application.DTOs.InventoryDTOs;

namespace Application.Interfaces;

public interface IInventoryService
{
    public Task<Guid> CreateInventoryAsync(CreateInventoryDto inventoryDto, CancellationToken cancellationToken = default);
    public Task AddItemAsync(AddInventoryItemDto itemDto, CancellationToken cancellationToken = default);
    public Task WasteItemAsync(WasteInventoryItemDto wasteDto, CancellationToken cancellationToken = default);
    public Task UpdateItemPriceAsync(UpdateInventoryItemPriceDto priceDto, CancellationToken cancellationToken = default);
    public Task AddItemQuantityAsync(int productId, int quantity, CancellationToken cancellationToken = default);
    public Task DeActivateItemAsync(int productId, CancellationToken cancellationToken = default);
    public Task<ViewInventoryDto> GetCurrentInventoryAsync(CancellationToken cancellationToken = default);
    public Task<List<ViewInventoryItemDto>> GetOutOfStockItemsAsync(CancellationToken cancellationToken = default);
    public Task<List<ViewInventoryItemPriceDto>> GetPriceHistoryForProductAsync(int productId, CancellationToken cancellationToken = default);
    public Task<List<ViewWasteRecordDto>> GetWasteRecordsForProductAsync(int productId, CancellationToken cancellationToken = default);
}