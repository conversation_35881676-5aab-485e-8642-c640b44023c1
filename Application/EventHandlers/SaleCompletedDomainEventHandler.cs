using Application.Exceptions;
using Application.Interfaces;
using Domain.Entities.InventoryAggregate;
using Domain.Entities.SaleAggregate;
using Domain.Events;
using Domain.Interfaces;
using Domain.Managers;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Application.EventHandlers;

public class SaleCompletedDomainEventHandler(
    ISaleRepository saleRepository,
    IInventoryRepository inventoryRepository,
    IInventoryManager inventoryManager,
    IUnitOfWork unitOfWork,
    ILogger<SaleCompletedDomainEventHandler> logger) : IDomainEventHandler<SaleCompletedDomainEvent>
{
    public async Task HandleAsync(SaleCompletedDomainEvent @event)
    {
        try
        {
            logger.LogInformation("Processing SaleCompleted event for sale {SaleId}", @event.SaleId);

            var sale = await saleRepository.GetByIdOrDefaultAsync(@event.SaleId);
            if (sale is null)
            {
                logger.LogWarning("Sale {SaleId} not found when processing SaleCompleted event", @event.SaleId);
                return;
            }

            var inventory = await inventoryManager.GetCurrentInventoryOrDefaultAsync();
            if (inventory is null)
            {
                logger.LogWarning("No current inventory found when processing SaleCompleted event for sale {SaleId}",
                    @event.SaleId);
                return;
            }

            await CheckInventoryItemsThresholdAsync(sale);

            await inventoryRepository.UpdateAsync(inventory);
            await unitOfWork.SaveChangesAsync();

            logger.LogInformation("Successfully processed SaleCompleted event for sale {SaleId}", @event.SaleId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing SaleCompleted event for sale {SaleId}", @event.SaleId);
            throw;
        }
    }

    private async Task CheckInventoryItemsThresholdAsync(Sale sale)
    {
        foreach (var saleItem in sale.Items)
        {
            var inventoryItem = await inventoryRepository.GetInventoryItemByProductIdAsync(saleItem.ProductId);
            if (inventoryItem is null)
            {
                logger.LogCritical(
                    "Inventory item for product {ProductId} not found when processing SaleCompleted event for sale {SaleId}",
                    saleItem.ProductId, sale.Id);
                throw new EntityNotFoundApplicationException<InventoryItem>(saleItem.ProductId);
            }

            inventoryItem.CheckThreshold();
        }
    }
}