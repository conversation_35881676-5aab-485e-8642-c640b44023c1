using SharedKernel;

namespace Domain.Events;

public sealed class InventoryItemWastedDomainEvent(
    Guid inventoryItemId,
    int productId,
    int wastedQuantity,
    string reason) : DomainEvent
{
    public Guid InventoryItemId { get; } = inventoryItemId;
    public int ProductId { get; } = productId;
    public int WastedQuantity { get; } = wastedQuantity;
    public string Reason { get; } = reason;
}