using Domain.ValueObjects;
using SharedKernel;

namespace Domain.Events;

public sealed class InventoryItemPriceUpdatedDomainEvent(
    Guid inventoryId,
    Guid itemId,
    int productId,
    Money oldActualPrice,
    Money newActualPrice,
    Money oldSellingPrice,
    Money newSellingPrice) : DomainEvent
{
    public Guid InventoryId { get; } = inventoryId;
    public Guid ItemId { get; } = itemId;
    public int ProductId { get; } = productId;
    public Money OldActualPrice { get; } = oldActualPrice;
    public Money NewActualPrice { get; } = newActualPrice;
    public Money OldSellingPrice { get; } = oldSellingPrice;
    public Money NewSellingPrice { get; } = newSellingPrice;
}
