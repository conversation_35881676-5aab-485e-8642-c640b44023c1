using SharedKernel;

namespace Domain.Exceptions;

public class InsufficientStockDomainException(int productId, int currentStock, int requestedQuantity) : DomainException(
    $"Insufficient stock for product '{productId}'. Current stock: '{currentStock}', Requested: '{requestedQuantity}'")
{
    public int ProductId { get; } = productId;
    public int CurrentStock { get; } = currentStock;
    public int RequestedQuantity { get; } = requestedQuantity;
}