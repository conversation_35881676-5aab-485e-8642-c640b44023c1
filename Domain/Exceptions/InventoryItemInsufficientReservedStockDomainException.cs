using SharedKernel;

namespace Domain.Exceptions;

public class InventoryItemInsufficientReservedStockDomainException(
    int productId,
    int reservedQuantity,
    int requestedQuantity)
    : DomainException(
        $"Cannot release '{requestedQuantity}' items for product '{productId}'. Only '{reservedQuantity}' items are reserved.")
{
    public int ProductId { get; } = productId;
    public int ReservedQuantity { get; } = reservedQuantity;
    public int RequestedQuantity { get; } = requestedQuantity;
}