using Domain.Enums;
using SharedKernel;

namespace Domain.Exceptions;

public class CurrencyMismatchDomainException(CurrencyCode leftCurrency, CurrencyCode rightCurrency, string operation)
    : DomainException($"Cannot '{operation}' money with different currencies: '{leftCurrency}' and '{rightCurrency}'")
{
    public string LeftCurrency { get; } = leftCurrency.ToString();
    public string RightCurrency { get; } = rightCurrency.ToString();
    public string Operation { get; } = operation;
}