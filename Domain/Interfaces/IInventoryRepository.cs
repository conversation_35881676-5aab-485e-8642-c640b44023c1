using Domain.Entities.InventoryAggregate;

namespace Domain.Interfaces;

public interface IInventoryRepository : IRepository<Inventory, Guid>
{
    public Task<Inventory?> GetMostRecentInventoryAsync(CancellationToken cancellationToken = default);

    public Task<InventoryItem?> GetInventoryItemByProductIdAsync(int productId,
        CancellationToken cancellationToken = default);

    public Task<List<InventoryItem>> GetOutOfStockItemsAsync(CancellationToken cancellationToken = default);

    public Task<List<InventoryItemPrice>> GetPriceHistoryForProductAsync(int productId,
        CancellationToken cancellationToken = default);

    public Task<List<WasteRecord>> GetWasteRecordsForProductAsync(int productId,
        CancellationToken cancellationToken = default);
}