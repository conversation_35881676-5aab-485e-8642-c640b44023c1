using Domain.Entities.ProductAggregate;
using Domain.Entities.TagAggregate;

namespace Domain.Interfaces;

public interface ITagRepository : IRepository<Tag, int>
{
    public Task<Tag> GetBySlugAsync(string slug, CancellationToken cancellationToken = default);
    public Task<List<Tag>> GetTagsByProductIdAsync(int productId, CancellationToken cancellationToken = default);
    public Task<List<Product>> GetProductsByTagIdAsync(int tagId, CancellationToken cancellationToken = default);
    public Task<bool> IsSlugUniqueAsync(string slug, CancellationToken cancellationToken = default);
}