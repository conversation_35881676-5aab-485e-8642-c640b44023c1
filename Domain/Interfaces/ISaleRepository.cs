using Domain.Entities.SaleAggregate;
using Domain.Enums;

namespace Domain.Interfaces;

public interface ISaleRepository : IRepository<Sale, Guid>
{
    public Task<List<Sale>> GetSalesByStatusAsync(SaleStatus status, CancellationToken cancellationToken = default);

    public Task<List<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate,
        CancellationToken cancellationToken = default);

    public Task<List<Sale>> GetSalesByProductIdAsync(int productId, CancellationToken cancellationToken = default);
}