using SharedKernel;

namespace Domain.Interfaces;

public interface IRepository<T, TId> where T : Entity<TId> where TId : struct, IEquatable<TId>
{
    public Task<List<T>> GetAllAsync(CancellationToken cancellationToken = default);
    public Task<T?> GetByIdOrDefaultAsync(TId id, CancellationToken cancellationToken = default);
    public Task<TId> AddAsync(T entity, CancellationToken cancellationToken = default);
    public Task UpdateAsync(T entity, CancellationToken cancellationToken = default);
    public Task<bool> DeleteAsync(TId id, CancellationToken cancellationToken = default);
}