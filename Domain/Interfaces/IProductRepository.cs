using Domain.Entities.ProductAggregate;

namespace Domain.Interfaces;

public interface IProductRepository : IRepository<Product, int>
{
    public Task<List<Product>> GetProductsByCategoryIdAsync(int categoryId,
        CancellationToken cancellationToken = default);

    public Task<List<Product>> GetProductsByTagIdAsync(int tagId, CancellationToken cancellationToken = default);
    public Task<List<Product>> SearchProductsAsync(string searchTerm, CancellationToken cancellationToken = default);
}