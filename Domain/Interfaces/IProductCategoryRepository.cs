using Domain.Entities.ProductAggregate;
using Domain.Entities.ProductCategoryAggregate;

namespace Domain.Interfaces;

public interface IProductCategoryRepository : IRepository<ProductCategory, int>
{
    public Task<bool> IsCategoryInUseAsync(int categoryId, CancellationToken cancellationToken = default);
    public Task<bool> IsCategoryNameUniqueAsync(string name, CancellationToken cancellationToken = default);

    public Task<List<Product>> GetProductsByCategoryIdAsync(int categoryId,
        CancellationToken cancellationToken = default);
}