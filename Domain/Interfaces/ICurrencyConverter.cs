using Domain.Enums;
using Domain.ValueObjects;

namespace Domain.Interfaces;

public interface ICurrencyConverter
{
    public Task<Money> ConvertAsync(Money source, CurrencyCode targetCurrencyCode,
        CancellationToken cancellationToken = default);

    public Task<decimal> GetExchangeRateAsync(CurrencyCode sourceCurrencyCode, CurrencyCode targetCurrencyCode,
        CancellationToken cancellationToken = default);
}