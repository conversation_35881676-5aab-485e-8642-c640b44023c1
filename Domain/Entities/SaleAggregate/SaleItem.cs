using Ardalis.GuardClauses;
using Domain.ValueObjects;
using SharedKernel;

namespace Domain.Entities.SaleAggregate;

public class SaleItem : Entity<int>
{
    public int ProductId { get; private set; }
    public string ProductName { get; private set; }
    public Money Price { get; private set; }
    public int Quantity { get; private set; }
    
    public Money TotalPrice => new Money(Price.Amount * Quantity, Price.CurrencyCode);

    private SaleItem() { }

    public SaleItem(int productId, string productName, Money price, int quantity)
    {
        Guard.Against.NegativeOrZero(productId, nameof(productId));
        Guard.Against.NegativeOrZero(quantity, nameof(quantity));
        Guard.Against.NullOrWhiteSpace(productName);

        ProductId = productId;
        ProductName = productName;
        Price = price;
        Quantity = quantity;
    }

    public void UpdateQuantity(int quantity)
    {
        Guard.Against.NegativeOrZero(quantity, nameof(quantity));

        Quantity = quantity;
    }
}
