using Domain.Enums;
using Domain.Events;
using Domain.Exceptions;
using Domain.ValueObjects;
using SharedKernel;

namespace Domain.Entities.SaleAggregate;

public class Sale : AggregateRoot<Guid>
{
    private readonly List<SaleItem> _items = [];

    public Money TotalAmount { get; private set; } = Money.Zero;
    public Money DiscountAmount { get; private set; } = Money.Zero;
    public Money FinalAmount { get; private set; } = Money.Zero;
    public DateTime CreatedAt { get; private set; }
    public SaleStatus Status { get; private set; } = SaleStatus.Created;

    public string? CancellationReason { get; private set; } = null;
    public IReadOnlyCollection<SaleItem> Items => _items.AsReadOnly();

    //For ORM
    private Sale()
    {
    }
    
    internal Sale(IEnumerable<SaleItem> saleItems, Money? discountAmount = null)
    {
        DiscountAmount = discountAmount ?? Money.Zero;
        CreatedAt = DateTime.UtcNow;

        foreach (var saleItem in saleItems)
        {
            AddItem(saleItem.ProductId, saleItem.ProductName, saleItem.Price, saleItem.Quantity);
        }
    }

    internal void AddItem(int productId, string productName, Money price, int quantity)
    {
        GuardAgainstModification();

        var existingItem = _items.FirstOrDefault(i => i.ProductId == productId);

        if (existingItem is not null)
        {
            existingItem.UpdateQuantity(existingItem.Quantity + quantity);
        }
        else
        {
            var saleItem = new SaleItem(productId, productName, price, quantity);
            _items.Add(saleItem);
        }

        Status = SaleStatus.Pending;
    }

    internal void RemoveItem(int productId)
    {
        GuardAgainstModification();

        var item = _items.FirstOrDefault(i => i.ProductId == productId);
        if (item is null)
        {
            return;
        }

        _items.Remove(item);
    }
    
    public int GetSaleItemQuantity(int productId)
    {
        var item = _items.FirstOrDefault(i => i.ProductId == productId);
        return item?.Quantity ?? 0;
    }
    
    public void ApplyDiscount(Money discount)
    {
        GuardAgainstModification();

        DiscountAmount = discount;
    }

    internal void CompleteSale(Money total, Money final)
    {
        GuardAgainstModification();

        if (_items.Count == 0)
        {
            throw new SaleCannotBeEmptyDomainException(Id);
        }
        TotalAmount = total;
        FinalAmount = final;

        Status = SaleStatus.Completed;
        AddDomainEvent(new SaleCompletedDomainEvent(Id));
    }
    
    internal void CancelSale(string? reason = null)
    {
        if (Status == SaleStatus.Completed)
        {
            throw new SaleCannotBeModifiedDomainException(Id, Status);
        }

        Status = SaleStatus.Cancelled;
        CancellationReason = reason;
        AddDomainEvent(new SaleCanceledDomainEvent(Id, reason));
    }
    
    private void GuardAgainstModification()
    {
        if (Status is SaleStatus.Completed or SaleStatus.Cancelled)
        {
            throw new SaleCannotBeModifiedDomainException(Id, Status);
        }
    }
}