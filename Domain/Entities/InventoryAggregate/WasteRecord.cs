using Ardalis.GuardClauses;
using SharedKernel;

namespace Domain.Entities.InventoryAggregate;

public class WasteRecord : Entity<Guid>
{
    public int Quantity { get; private set; }
    public string Reason { get; private set; }
    public DateTime RecordedDate { get; private set; }
    
    //For ORM
    private WasteRecord() {}
    
    public WasteRecord(int quantity, string reason)
    {
        Guard.Against.NegativeOrZero(quantity, nameof(quantity));
        Guard.Against.NullOrWhiteSpace(reason, nameof(reason));

        Quantity = quantity;
        Reason = reason;
        RecordedDate = DateTime.UtcNow;
    }
}
