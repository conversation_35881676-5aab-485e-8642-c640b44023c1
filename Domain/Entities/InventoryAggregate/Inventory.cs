using Ardalis.GuardClauses;
using Domain.ValueObjects;
using Domain.Events;
using Domain.Exceptions;
using SharedKernel;

namespace Domain.Entities.InventoryAggregate;

public class Inventory : AggregateRoot<Guid>
{
    private readonly List<InventoryItem> _items = [];

    public DateTime CreatedAt { get; private set; }
    public Money Balance { get; private set; }


    public IReadOnlyCollection<InventoryItem> Items => _items.AsReadOnly();

    //For ORM
    private Inventory()
    {
    }

    public Inventory(Money balance, DateTime? createdAt = null)
    {
        Balance = balance;
        CreatedAt = createdAt ?? DateTime.UtcNow;
    }

    public void AddItem(int productId, int quantity, Money actualPrice, Money sellingPrice, int threshold)
    {
        var existingItem = _items.FirstOrDefault(i => i.ProductId == productId);
        if (existingItem is not null)
        {
            throw new InventoryItemDuplicateDomainException(Id, productId);
        }

        var item = new InventoryItem(productId, quantity, actualPrice, sellingPrice, threshold);

        _items.Add(item);
    }

    public Money GetItemPrice(int productId)
    {
        var item = GetItem(productId);
        return item.Price.SellingPrice;
    }

    public void UpdateItemPrice(int productId, Money actualPrice, Money sellingPrice)
    {
        var item = GetItem(productId);

        var eventData = new InventoryItemPriceUpdatedDomainEvent(
            Id,
            item.Id,
            productId,
            item.Price.ActualPrice,
            actualPrice,
            item.Price.SellingPrice,
            sellingPrice);
        
        item.UpdatePrice(actualPrice, sellingPrice);

        AddDomainEvent(eventData);
    }

    internal void ReserveItem(int productId, int quantity)
    {
        var inventoryItem = GetItem(productId);

        inventoryItem.Reserve(quantity);
    }
    
    internal void ReleaseItem(int productId, int quantity)
    {
        var inventoryItem = GetItem(productId);

        inventoryItem.Release(quantity);
    }
    
    internal void ConfirmReservation(int productId, int quantity)
    {
        var inventoryItem = GetItem(productId);

        inventoryItem.ConfirmReservation(quantity);
    }

    public void WasteItem(int productId, int quantity, string reason)
    {
        var inventoryItem = GetItem(productId);
        
        inventoryItem.RecordWaste(quantity, reason);

        AddDomainEvent(new InventoryItemWastedDomainEvent(
            inventoryItem.Id,
            productId,
            quantity,
            reason));
    }

    public void AddItemQuantity(int productId, int quantity)
    {
        var inventoryItem = GetItem(productId);

        inventoryItem.AddQuantity(quantity);
    }

    public void DeActivateItem(int productId)
    {
        var inventoryItem = GetItem(productId);

        inventoryItem.DeActivate();
    }

    public void SetThreshold(int productId, int threshold)
    {
        var inventoryItem = GetItem(productId);

        inventoryItem.SetThreshold(threshold);
    }

    public void AddToBalance(Money amount)
    {
        Guard.Against.Null(amount, nameof(amount));
        
        if (Balance.CurrencyCode != amount.CurrencyCode)
        {
            throw new CurrencyMismatchDomainException(Balance.CurrencyCode, amount.CurrencyCode, "add to balance");
        }
        
        Balance += amount;
    }
    
    private InventoryItem GetItem(int productId)
    {
        var item = _items.FirstOrDefault(i => i.ProductId == productId);
        if (item is null)
        {
            throw new InventoryItemNotFoundDomainException(Id, productId);
        }

        return item;
    }
}
