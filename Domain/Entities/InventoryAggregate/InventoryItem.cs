using Domain.ValueObjects;
using Ardalis.GuardClauses;
using Domain.Enums;
using Domain.Events;
using Domain.Exceptions;
using SharedKernel;

namespace Domain.Entities.InventoryAggregate;

public class InventoryItem : Entity<Guid>
{
    private readonly List<InventoryItemPrice> _prices = [];
    private readonly List<WasteRecord> _wasteRecords = [];

    public int ProductId { get; private set; }
    public int Quantity { get; private set; }

    public int ReservedQuantity { get; private set; }
    public int Threshold { get; private set; }
    public int SoldQuantity { get; private set; }
    public int TotalSoldQuantity { get; private set; }
    public int TotalWastedQuantity { get; private set; }
    public InventoryItemStatus Status { get; private set; }
    public InventoryItemPrice Price => _prices.OrderByDescending(p => p.CreatedAt).FirstOrDefault();

    public IReadOnlyCollection<InventoryItemPrice> Prices => _prices.AsReadOnly();
    public IReadOnlyCollection<WasteRecord> WasteRecords => _wasteRecords.AsReadOnly();

    public bool IsThresholdReached => Quantity <= Threshold;
    public void CheckThreshold()
    {
        if (IsThresholdReached)
        {
            AddDomainEvent(new InventoryItemThresholdReachedDomainEvent(Id, ProductId, Quantity));
        }
    }


    //For ORM
    private InventoryItem()
    {
    }

    public InventoryItem(int productId, int quantity, Money actualPrice, Money sellingPrice, int threshold)
    {
        Guard.Against.NegativeOrZero(productId, nameof(productId));
        Guard.Against.Negative(quantity, nameof(quantity));
        Guard.Against.Negative(threshold, nameof(threshold));

        ProductId = productId;
        Quantity = quantity;
        Threshold = threshold;

        UpdatePrice(actualPrice, sellingPrice);
        UpdateStatus();
    }

    public void Reserve(int quantity)
    {
        Guard.Against.NegativeOrZero(quantity, nameof(quantity));

        if (Quantity < quantity)
        {
            throw new InsufficientStockDomainException(ProductId, Quantity, quantity);
        }

        ReservedQuantity += quantity;
        Quantity -= quantity;
    }

    public void Release(int quantity)
    {
        Guard.Against.NegativeOrZero(quantity, nameof(quantity));

        if (ReservedQuantity < quantity)
        {
            throw new InventoryItemInsufficientReservedStockDomainException(ProductId, ReservedQuantity, quantity);
        }

        ReservedQuantity -= quantity;
        Quantity += quantity;
    }

    public void ConfirmReservation(int quantity)
    {
        Release(quantity);
        Sell(quantity);
    }

    private void Sell(int quantity)
    {
        Guard.Against.NegativeOrZero(quantity, nameof(quantity));

        if (Quantity < quantity)
        {
            throw new InsufficientStockDomainException(ProductId, Quantity, quantity);
        }

        Quantity -= quantity;
        SoldQuantity += quantity;
        TotalSoldQuantity += quantity;

        UpdateStatus();
    }

    public void RecordWaste(int quantity, string reason)
    {
        Guard.Against.NegativeOrZero(quantity, nameof(quantity));
        Guard.Against.NullOrWhiteSpace(reason, nameof(reason));

        if (Quantity < quantity)
        {
            throw new InsufficientStockDomainException(ProductId, Quantity, quantity);
        }

        var wasteRecord = new WasteRecord(quantity, reason);
        _wasteRecords.Add(wasteRecord);

        Quantity -= quantity;
        TotalWastedQuantity += quantity;

        UpdateStatus();
    }

    public void UpdatePrice(Money actualPrice, Money sellingPrice)
    {
        Guard.Against.Null(actualPrice, nameof(actualPrice));
        Guard.Against.Null(sellingPrice, nameof(sellingPrice));

        var newPrice = new InventoryItemPrice(actualPrice, sellingPrice);
        _prices.Add(newPrice);
    }

    public void AddQuantity(int quantity)
    {
        Guard.Against.NegativeOrZero(quantity, nameof(quantity));

        Quantity += quantity;
        UpdateStatus();
    }

    public void DeActivate()
    {
        Status = InventoryItemStatus.Inactive;
    }

    public void SetThreshold(int threshold)
    {
        Guard.Against.NegativeOrZero(threshold, nameof(threshold));

        Threshold = threshold;
    }

    private void UpdateStatus()
    {
        Status = Quantity switch
        {
            0 => InventoryItemStatus.OutOfStock,
            _ => InventoryItemStatus.Active
        };
    }
}