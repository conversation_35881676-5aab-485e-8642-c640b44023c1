using Domain.ValueObjects;
using Ardalis.GuardClauses;
using SharedKernel;

namespace Domain.Entities.InventoryAggregate;

public class InventoryItemPrice : Entity<Guid>
{
    public Money ActualPrice { get; private set; }
    public Money SellingPrice { get; private set; }
    
    public DateTime CreatedAt { get; private set; }
    
    //For ORM
    private InventoryItemPrice() {}
    public InventoryItemPrice(Money actualPrice, Money sellingPrice)
    {
        Guard.Against.Null(actualPrice, nameof(actualPrice));
        Guard.Against.Null(sellingPrice, nameof(sellingPrice));

        ActualPrice = actualPrice;
        SellingPrice = sellingPrice;
        CreatedAt = DateTime.UtcNow;
    }
}
