using System.Text.RegularExpressions;
using Ardalis.GuardClauses;
using SharedKernel;

namespace Domain.Entities.TagAggregate;

public class Tag : AggregateRoot
{
    public string Name { get; private set; }
    public string Slug { get; private set; }

    private Tag() { }

    internal Tag(string name)
    {
        Name = Guard.Against.NullOrWhiteSpace(name, nameof(name));
        Slug = CreateSlug(name);
    }

    internal void Update(string name)
    {
        Name = Guard.Against.NullOrWhiteSpace(name, nameof(name));
        Slug = CreateSlug(name);
    }
    
    private static string CreateSlug(string name)
    {
        var slug = name.ToLowerInvariant();
        slug = Regex.Replace(slug, @"[^a-z0-9\s-]", "");
        slug = Regex.Replace(slug, @"\s+", "-");
        slug = Regex.Replace(slug, @"-+", "-");
        slug = slug.Trim('-');

        return slug;
    }
}
