using Ardalis.GuardClauses;
using SharedKernel;

namespace Domain.Entities.ProductCategoryAggregate;

public class ProductCategory : AggregateRoot
{
    public string Name { get; private set; }

    private ProductCategory() { }

    internal ProductCategory(string name)
    {
        Name = Guard.Against.NullOrWhiteSpace(name, nameof(name), "Category name cannot be empty");
    }

    internal void Update(string name)
    {
        Name = Guard.Against.NullOrWhiteSpace(name, nameof(name), "Category name cannot be empty");
    }
}
