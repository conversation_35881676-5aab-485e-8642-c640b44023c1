using Ardalis.GuardClauses;
using Domain.Exceptions;
using SharedKernel;

namespace Domain.Entities.ProductAggregate;

public class Product : AggregateRoot
{
    private readonly List<ProductTag> _productTags = [];

    public string Name { get; private set; }
    public string Description { get; private set; }
    public int CategoryId { get; private set; }
    public IReadOnlyCollection<ProductTag> ProductTags => _productTags.AsReadOnly();

    private Product()
    {
    }

    public Product(string name, string description)
    {
        Name = Guard.Against.NullOrWhiteSpace(name, nameof(name));
        Description = Guard.Against.Null(description, nameof(description));
    }

    public Product(string name, string description, int categoryId) : this(name, description)
    {
        CategoryId = Guard.Against.NegativeOrZero(categoryId, nameof(categoryId));
    }

    public Product(string name, string description, int categoryId, IEnumerable<int> tagIds) : this(name, description,
        categoryId)
    {
        _productTags = tagIds.Select(tagId => new ProductTag(Id, tagId)).ToList();
    }

    public void UpdateDetails(string name, string description, int categoryId)
    {
        Name = Guard.Against.NullOrWhiteSpace(name, nameof(name));
        Description = Guard.Against.Null(description, nameof(description));
        CategoryId = Guard.Against.NegativeOrZero(categoryId, nameof(categoryId));
    }

    public void AddTag(int tagId)
    {
        var tag = _productTags.FirstOrDefault(x => x.TagId == tagId);
        if (tag is not null)
        {
            throw new ProductTagAlreadyExistDomainException(Id, tagId);
        }
        
        _productTags.Add(new ProductTag(Id, tagId));
    }

    public void RemoveTag(int tagId)
    {
        var tag = _productTags.FirstOrDefault(x => x.TagId == tagId);
        if (tag is null)
        {
            throw new ProductTagNotFoundDomainException(tagId);
        }

        _productTags.Remove(tag);
    }

    public void ClearTags()
    {
        _productTags.Clear();
    }
}