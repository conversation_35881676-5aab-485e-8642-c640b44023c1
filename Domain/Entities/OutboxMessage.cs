using SharedKernel;

namespace Domain.Entities;

public class OutboxMessage : Entity<Guid>
{
    public string Type { get; private set; }
    public string Content { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? ProcessedAt { get; private set; }
    public string? Error { get; private set; }
    
    private OutboxMessage() { }
    
    public OutboxMessage(string type, string content)
    {
        Id = Guid.NewGuid();
        Type = type;
        Content = content;
        CreatedAt = DateTime.UtcNow;
    }

    public void MarkAsProcessed()
    {
        ProcessedAt = DateTime.UtcNow;
    }
    
    public void MarkAsFailed(string error)
    {
        Error = error;
    }
}
