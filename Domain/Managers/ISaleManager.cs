using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Domain.ValueObjects;

namespace Domain.Managers;

public interface ISaleManager
{
    public Task<Sale> CreateSaleAsync(List<SaleItem> saleItems, Money? discount,
        CancellationToken cancellationToken = default);

    public Task<Sale> CreateACompletedSaleAsync(List<SaleItem> saleItems, Money discount,
        CurrencyCode targetCurrencyCode,
        CancellationToken cancellationToken = default);

    public Task AddItemAsync(Sale sale, int productId, string productName, int quantity,
        CancellationToken cancellationToken = default);

    public Task RemoveItemAsync(Sale sale, int productId, CancellationToken cancellationToken = default);
    public Task CancelSaleAsync(Sale sale, string? reason, CancellationToken cancellationToken = default);

    public Task CompleteSaleAsync(Sale sale, CurrencyCode targetCurrencyCode,
        CancellationToken cancellationToken = default);
}