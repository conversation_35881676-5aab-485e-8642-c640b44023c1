using Domain.Entities.ProductCategoryAggregate;

namespace Domain.Managers;

public interface IProductCategoryManager
{
    public Task<ProductCategory> CreateAsync(string name, CancellationToken cancellationToken = default);

    public Task UpdateAsync(ProductCategory productCategory, string newName,
        CancellationToken cancellationToken = default);
    
    public Task TryToDeleteAsync(int categoryId, CancellationToken cancellationToken = default);
}