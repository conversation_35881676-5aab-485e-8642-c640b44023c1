using Domain.Entities.InventoryAggregate;
using Domain.Exceptions;
using Domain.Interfaces;
using Domain.Managers;

namespace Domain.Services;

public class InventoryDomainService(IInventoryRepository inventoryRepository) : IInventoryManager
{
    public async Task<Inventory> GetCurrentInventoryAsync(CancellationToken cancellationToken = default)
    {
        var currentInventory = await inventoryRepository.GetMostRecentInventoryAsync(cancellationToken);

        if (currentInventory is null)
        {
            throw new CurrentInventoryWasNotFound();
        }

        return currentInventory;
    }

    public async Task<Inventory?> GetCurrentInventoryOrDefaultAsync(CancellationToken cancellationToken = default)
    {
        return await inventoryRepository.GetMostRecentInventoryAsync(cancellationToken);
    }
}