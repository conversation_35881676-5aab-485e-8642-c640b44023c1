using Domain.Entities.InventoryAggregate;
using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Domain.Interfaces;
using Domain.Managers;
using Domain.ValueObjects;

namespace Domain.Services;

public class SaleDomainService(ICurrencyConverter currencyConverter, IInventoryManager inventoryManager) : ISaleManager
{
    public async Task<Sale> CreateSaleAsync(List<SaleItem> saleItems, Money? discount,
        CancellationToken cancellationToken = default)
    {
        var currentInventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);
        foreach (var saleItem in saleItems)
        {
            currentInventory.ReserveItem(saleItem.ProductId, saleItem.Quantity);
        }
        
        var sale = new Sale(saleItems, discount);
        
        return sale;
    }

    public async Task<Sale> CreateACompletedSaleAsync(List<SaleItem> saleItems, Money discount, CurrencyCode targetCurrencyCode,
        CancellationToken cancellationToken = default)
    {
        var sale = await CreateSaleAsync(saleItems, discount, cancellationToken);
        await CompleteSaleAsync(sale, targetCurrencyCode, cancellationToken);
        return sale;
    }

    public async Task AddItemAsync(Sale sale, int productId, string productName, int quantity,
        CancellationToken cancellationToken = default)
    {
        var currentInventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);
        
        currentInventory.ReserveItem(productId, quantity);
        
        sale.AddItem(productId, productName, currentInventory.GetItemPrice(productId), quantity);
    }

    public async Task RemoveItemAsync(Sale sale, int productId, CancellationToken cancellationToken = default)
    {
        int reservedQuantity = sale.GetSaleItemQuantity(productId);

        sale.RemoveItem(productId);
        
        var currentInventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);
        
        currentInventory.ReleaseItem(productId, reservedQuantity);
    }

    public async Task CancelSaleAsync(Sale sale, string? reason, CancellationToken cancellationToken = default)
    {
        sale.CancelSale(reason);
        
        var currentInventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);

        foreach (var saleItem in sale.Items)
        {
            currentInventory.ReleaseItem(saleItem.ProductId, saleItem.Quantity);
        }
    }

    public async Task CompleteSaleAsync(Sale sale, CurrencyCode targetCurrencyCode,
        CancellationToken cancellationToken = default)
    {
        var total = new Money(0, targetCurrencyCode);

        foreach (var item in sale.Items)
        {
            var itemPrice = item.Price;

            if (itemPrice.CurrencyCode != targetCurrencyCode)
            {
                itemPrice = await currencyConverter.ConvertAsync(itemPrice, targetCurrencyCode, cancellationToken);
            }

            total += itemPrice.Amount * item.Quantity;
        }

        var discount = sale.DiscountAmount;
        if (discount.CurrencyCode != targetCurrencyCode)
        {
            discount = await currencyConverter.ConvertAsync(discount, targetCurrencyCode, cancellationToken);
        }

        sale.CompleteSale(total, total - discount);
        
        var currentInventory = await inventoryManager.GetCurrentInventoryAsync(cancellationToken);
        
        ConfirmSaleReservation(sale, currentInventory);
        await AddSaleAmountToInventoryBalanceAsync(sale, currentInventory);
    }
    
    private void ConfirmSaleReservation(Sale sale, Inventory inventory)
    {
        foreach (var saleItem in sale.Items)
        {
            inventory.ConfirmReservation(saleItem.ProductId, saleItem.Quantity);
        }
    }
    
    private async Task AddSaleAmountToInventoryBalanceAsync(Sale sale, Inventory inventory)
    {
        var saleAmount = sale.FinalAmount;
        var inventoryBalance = inventory.Balance;

        if (saleAmount.CurrencyCode != inventoryBalance.CurrencyCode)
        {
            saleAmount = await currencyConverter.ConvertAsync(saleAmount, inventoryBalance.CurrencyCode);
        }

        inventory.AddToBalance(saleAmount);
    }
}