using Domain.Entities.TagAggregate;
using Domain.Exceptions;
using Domain.Interfaces;
using Domain.Managers;

namespace Domain.Services;

public class TagDomainService(ITagRepository tagRepository) : ITagManager
{
    public async Task<Tag> CreateTagAsync(string name, CancellationToken cancellationToken = default)
    {
        if (await tagRepository.IsSlugUniqueAsync(name, cancellationToken) == false)
        {
            throw new TagAlreadyExistsDomainException(name);
        }

        return new Tag(name);
    }

    public async Task UpdateTagAsync(Tag tag, string name, CancellationToken cancellationToken = default)
    {
        if (await tagRepository.IsSlugUniqueAsync(name, cancellationToken) == false)
        {
            throw new TagAlreadyExistsDomainException(name);
        }
        
        tag.Update(name);
    }
}