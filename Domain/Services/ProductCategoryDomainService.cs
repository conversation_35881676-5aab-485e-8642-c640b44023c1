using Domain.Entities.ProductCategoryAggregate;
using Domain.Exceptions;
using Domain.Interfaces;
using Domain.Managers;

namespace Domain.Services;

public class ProductCategoryDomainService(IProductCategoryRepository productCategoryDomainRepository)
    : IProductCategoryManager
{
    public async Task<ProductCategory> CreateAsync(string name, CancellationToken cancellationToken = default)
    {
        if (await productCategoryDomainRepository.IsCategoryNameUniqueAsync(name, cancellationToken) == false)
        {
            throw new ProductCategoryAlreadyExistsDomainException(name);
        }

        return new ProductCategory(name);
    }

    public async Task UpdateAsync(ProductCategory productCategory, string newName,
        CancellationToken cancellationToken = default)
    {
        if (await productCategoryDomainRepository.IsCategoryNameUniqueAsync(newName, cancellationToken) == false)
        {
            throw new ProductCategoryAlreadyExistsDomainException(newName);
        }

        productCategory.Update(newName);
    }

    public async Task TryToDeleteAsync(int categoryId, CancellationToken cancellationToken = default)
    {
        if (await productCategoryDomainRepository.IsCategoryInUseAsync(categoryId, cancellationToken))
        {
            throw new ProductCategoryInUseDomainException(categoryId);
        }
    }
}