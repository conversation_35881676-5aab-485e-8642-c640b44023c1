using Ardalis.GuardClauses;
using Domain.Enums;
using Domain.Exceptions;
using SharedKernel;

namespace Domain.ValueObjects;

public class Money : ValueObject
{
    public static Money Zero => new Money(0, CurrencyCode.XXX);
    
    public decimal Amount { get; }
    
    public CurrencyCode CurrencyCode { get; }
    
    private Money(){}
    
    public Money(decimal amount, CurrencyCode currencyCode)
    {
        Guard.Against.Negative(amount, nameof(amount), "Amount cannot be negative");
        
        Amount = amount;
        CurrencyCode = currencyCode;
    }
    
    public Money(decimal amount, string currencyCode) : this(amount, Enum.Parse<CurrencyCode>(currencyCode, ignoreCase: true))
    {
    }

    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Amount;
        yield return CurrencyCode;
    }

    public static Money operator +(Money? left, Money? right)
    {
        Guard.Against.Null(left, nameof(left));
        Guard.Against.Null(right, nameof(right));
        
        if (left.HasSameCurrencyAs(right) == false)
        {
            throw new CurrencyMismatchDomainException(left.CurrencyCode, right.CurrencyCode, "add");
        }
        
        return new Money(left.Amount + right.Amount, left.CurrencyCode);
    }
    
    public static Money operator +(Money? left, decimal amount)
    {
        Guard.Against.Null(left, nameof(left));
        
        return new Money(left.Amount + amount, left.CurrencyCode);
    }
    
    public static Money operator +(decimal amount, Money? right)
    {
        Guard.Against.Null(right, nameof(right));
        
        return new Money(right.Amount + amount, right.CurrencyCode);
    }
    
    public static Money operator -(Money? left, Money? right)
    {
        Guard.Against.Null(left, nameof(left));
        Guard.Against.Null(right, nameof(right));

        if (left.HasSameCurrencyAs(right) == false)
        {
            throw new CurrencyMismatchDomainException(left.CurrencyCode, right.CurrencyCode, "subtract");
        }

        return new Money(left.Amount - right.Amount, left.CurrencyCode);
    }

    public Money ConvertTo(CurrencyCode targetCurrency, decimal exchangeRate)
    {
        Guard.Against.NegativeOrZero(exchangeRate, nameof(exchangeRate));
        
        if (CurrencyCode == targetCurrency)
        {
            return this;
        }

        return new Money(Amount * exchangeRate, targetCurrency);
    }

    private bool HasSameCurrencyAs(Money? other)
    {
        Guard.Against.Null(other, nameof(other));
        
        return CurrencyCode == other.CurrencyCode;
    }

    public override string ToString()
    {
        return $"{Amount} {CurrencyCode}";
    }
}