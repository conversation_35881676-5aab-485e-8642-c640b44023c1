using Domain.Enums;
using Domain.Events;
using Domain.ValueObjects;

namespace Domain.Tests.Events;

public class DomainEventTests
{
    [Fact]
    public void InventoryItemPriceUpdatedDomainEventWithValidParametersCreatesEvent()
    {
        // Arrange
        var inventoryId = Guid.NewGuid();
        var itemId = Guid.NewGuid();
        var productId = 1;
        var oldActualPrice = new Money(50m, CurrencyCode.USD);
        var newActualPrice = new Money(60m, CurrencyCode.USD);
        var oldSellingPrice = new Money(100m, CurrencyCode.USD);
        var newSellingPrice = new Money(120m, CurrencyCode.USD);

        // Act
        var @event = new InventoryItemPriceUpdatedDomainEvent(
            inventoryId,
            itemId,
            productId,
            oldActualPrice,
            newActualPrice,
            oldSellingPrice,
            newSellingPrice);

        // Assert
        @event.InventoryId.Should().Be(inventoryId);
        @event.ItemId.Should().Be(itemId);
        @event.ProductId.Should().Be(productId);
        @event.OldActualPrice.Should().Be(oldActualPrice);
        @event.NewActualPrice.Should().Be(newActualPrice);
        @event.OldSellingPrice.Should().Be(oldSellingPrice);
        @event.NewSellingPrice.Should().Be(newSellingPrice);
        @event.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void InventoryItemThresholdReachedDomainEventWithValidParametersCreatesEvent()
    {
        // Arrange
        var inventoryItemId = Guid.NewGuid();
        var productId = 1;
        var quantity = 5;

        // Act
        var @event = new InventoryItemThresholdReachedDomainEvent(inventoryItemId, productId, quantity);

        // Assert
        @event.InventoryItemId.Should().Be(inventoryItemId);
        @event.ProductId.Should().Be(productId);
        @event.Quantity.Should().Be(quantity);
        @event.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void InventoryItemWastedDomainEventWithValidParametersCreatesEvent()
    {
        // Arrange
        var inventoryItemId = Guid.NewGuid();
        var productId = 1;
        var wastedQuantity = 5;
        var reason = "Expired";

        // Act
        var @event = new InventoryItemWastedDomainEvent(inventoryItemId, productId, wastedQuantity, reason);

        // Assert
        @event.InventoryItemId.Should().Be(inventoryItemId);
        @event.ProductId.Should().Be(productId);
        @event.WastedQuantity.Should().Be(wastedQuantity);
        @event.Reason.Should().Be(reason);
        @event.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void SaleCanceledDomainEventWithValidParametersCreatesEvent()
    {
        // Arrange
        var saleId = Guid.NewGuid();
        var reason = "Customer cancelled";

        // Act
        var @event = new SaleCanceledDomainEvent(saleId, reason);

        // Assert
        @event.SaleId.Should().Be(saleId);
        @event.Reason.Should().Be(reason);
        @event.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void SaleCompletedDomainEventWithValidParametersCreatesEvent()
    {
        // Arrange
        var saleId = Guid.NewGuid();

        // Act
        var @event = new SaleCompletedDomainEvent(saleId);

        // Assert
        @event.SaleId.Should().Be(saleId);
        @event.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
    }
}