using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Domain.ValueObjects;

namespace Domain.Tests.Entities.SaleAggregate;

public class SaleItemTests
{
    private readonly int _productId = 1;
    private readonly string _productName = "Test Product";
    private readonly Money _price = new Money(100m, CurrencyCode.USD);
    private readonly int _quantity = 2;

    [Fact]
    public void ConstructorWithValidParametersCreatesSaleItem()
    {
        // Arrange & Act
        var saleItem = new SaleItem(_productId, _productName, _price, _quantity);

        // Assert
        saleItem.ProductId.Should().Be(_productId);
        saleItem.ProductName.Should().Be(_productName);
        saleItem.Price.Should().Be(_price);
        saleItem.Quantity.Should().Be(_quantity);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void ConstructorWithInvalidProductIdThrowsArgumentException(int invalidProductId)
    {
        // Arrange & Act
        Action act = () => _ = new SaleItem(invalidProductId, _productName, _price, _quantity);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData(" ")]
    public void ConstructorWithInvalidProductNameThrowsArgumentException(string invalidProductName)
    {
        // Arrange & Act
        Action act = () => _ = new SaleItem(_productId, invalidProductName, _price, _quantity);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void ConstructorWithInvalidQuantityThrowsArgumentException(int invalidQuantity)
    {
        // Arrange & Act
        Action act = () => _ = new SaleItem(_productId, _productName, _price, invalidQuantity);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void TotalPriceReturnsCorrectAmount()
    {
        // Arrange
        var saleItem = new SaleItem(_productId, _productName, _price, _quantity);
        var expectedTotal = new Money(_price.Amount * _quantity, _price.CurrencyCode);

        // Act
        var totalPrice = saleItem.TotalPrice;

        // Assert
        totalPrice.Should().Be(expectedTotal);
    }

    [Fact]
    public void UpdateQuantityWithValidQuantityUpdatesQuantity()
    {
        // Arrange
        var saleItem = new SaleItem(_productId, _productName, _price, _quantity);
        var newQuantity = 5;

        // Act
        saleItem.UpdateQuantity(newQuantity);

        // Assert
        saleItem.Quantity.Should().Be(newQuantity);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void UpdateQuantityWithInvalidQuantityThrowsArgumentException(int invalidQuantity)
    {
        // Arrange
        var saleItem = new SaleItem(_productId, _productName, _price, _quantity);

        // Act
        Action act = () =>
        {
            saleItem.UpdateQuantity(invalidQuantity);
        };

        // Assert
        act.Should().Throw<ArgumentException>();
    }
}
