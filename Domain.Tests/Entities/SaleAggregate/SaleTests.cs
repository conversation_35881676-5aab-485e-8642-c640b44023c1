using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Domain.Events;
using Domain.Exceptions;
using Domain.ValueObjects;

namespace Domain.Tests.Entities.SaleAggregate;

public class SaleTests
{
    private readonly Money _defaultPrice = new Money(100m, CurrencyCode.USD);
    
    private SaleItem CreateSaleItem(int productId = 1, string productName = "Test Product", Money? price = null, int quantity = 1)
    {
        return new SaleItem(productId, productName, price ?? _defaultPrice, quantity);
    }
    
    [Fact]
    public void ConstructorWithValidParametersCreatesSale()
    {
        // Arrange
        var saleItems = new List<SaleItem>
        {
            CreateSaleItem(1, "Product 1", new Money(100m, CurrencyCode.USD), 2),
            CreateSaleItem(2, "Product 2", new Money(50m, CurrencyCode.USD), 1)
        };
        
        // Act
        var sale = new Sale(saleItems);
        
        // Assert
        sale.Items.Should().HaveCount(2);
        sale.Status.Should().Be(SaleStatus.Pending);
        sale.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        sale.DiscountAmount.Should().Be(Money.Zero);
        sale.CancellationReason.Should().BeNull();
    }
    
    [Fact]
    public void ConstructorWithDiscountSetsSaleWithDiscount()
    {
        // Arrange
        var saleItems = new List<SaleItem> { CreateSaleItem() };
        var discount = new Money(10m, CurrencyCode.USD);
        
        // Act
        var sale = new Sale(saleItems, discount);
        
        // Assert
        sale.DiscountAmount.Should().Be(discount);
    }
    
    [Fact]
    public void AddItemWithNewProductAddsItemToSale()
    {
        // Arrange
        var sale = new Sale(new List<SaleItem>());
        var productId = 1;
        var productName = "Test Product";
        var price = new Money(100m, CurrencyCode.USD);
        var quantity = 2;
        
        // Act
        sale.AddItem(productId, productName, price, quantity);
        
        // Assert
        sale.Items.Should().HaveCount(1);
        var item = sale.Items.First();
        item.ProductId.Should().Be(productId);
        item.ProductName.Should().Be(productName);
        item.Price.Should().Be(price);
        item.Quantity.Should().Be(quantity);
        sale.Status.Should().Be(SaleStatus.Pending);
    }
    
    [Fact]
    public void AddItemWithExistingProductUpdatesQuantity()
    {
        // Arrange
        var productId = 1;
        var initialQuantity = 2;
        var additionalQuantity = 3;
        
        var saleItems = new List<SaleItem> { CreateSaleItem(productId, quantity: initialQuantity) };
        var sale = new Sale(saleItems);
        
        // Act
        sale.AddItem(productId, "Test Product", _defaultPrice, additionalQuantity);
        
        // Assert
        sale.Items.Should().HaveCount(1);
        var item = sale.Items.First();
        item.Quantity.Should().Be(initialQuantity + additionalQuantity);
    }
    
    [Fact]
    public void AddItemWhenSaleIsCompletedThrowsSaleCannotBeModifiedException()
    {
        // Arrange
        var sale = new Sale(new List<SaleItem> { CreateSaleItem() });
        
        // Use reflection to set the status since we can't complete a sale directly
        var statusProperty = typeof(Sale).GetProperty("Status");
        statusProperty?.SetValue(sale, SaleStatus.Completed);
        
        // Act
        Action act = () => sale.AddItem(2, "Another Product", _defaultPrice, 1);
        
        // Assert
        act.Should().Throw<SaleCannotBeModifiedDomainException>()
            .Where(e => e.SaleId == sale.Id && e.CurrentStatus == SaleStatus.Completed);
    }
    
    [Fact]
    public void AddItemWhenSaleIsCancelledThrowsSaleCannotBeModifiedException()
    {
        // Arrange
        var sale = new Sale(new List<SaleItem> { CreateSaleItem() });
        sale.CancelSale("Test cancellation");
        
        // Act
        Action act = () => sale.AddItem(2, "Another Product", _defaultPrice, 1);
        
        // Assert
        act.Should().Throw<SaleCannotBeModifiedDomainException>()
            .Where(e => e.SaleId == sale.Id && e.CurrentStatus == SaleStatus.Cancelled);
    }
    
    [Fact]
    public void RemoveItemWithExistingProductRemovesItemFromSale()
    {
        // Arrange
        var productId = 1;
        var saleItems = new List<SaleItem> { CreateSaleItem(productId) };
        var sale = new Sale(saleItems);
        
        // Act
        sale.RemoveItem(productId);
        
        // Assert
        sale.Items.Should().BeEmpty();
    }
    
    [Fact]
    public void RemoveItemWithNonExistingProductDoesNothing()
    {
        // Arrange
        var saleItems = new List<SaleItem> { CreateSaleItem(1) };
        var sale = new Sale(saleItems);
        
        // Act
        sale.RemoveItem(999); // Non-existing product ID
        
        // Assert
        sale.Items.Should().HaveCount(1);
    }
    
    [Fact]
    public void RemoveItemWhenSaleIsCompletedThrowsSaleCannotBeModifiedException()
    {
        // Arrange
        var productId = 1;
        var sale = new Sale(new List<SaleItem> { CreateSaleItem(productId) });
        
        // Use reflection to set the status since we can't complete a sale directly
        var statusProperty = typeof(Sale).GetProperty("Status");
        statusProperty?.SetValue(sale, SaleStatus.Completed);
        
        // Act
        Action act = () => sale.RemoveItem(productId);
        
        // Assert
        act.Should().Throw<SaleCannotBeModifiedDomainException>()
            .Where(e => e.SaleId == sale.Id && e.CurrentStatus == SaleStatus.Completed);
    }
    
    [Fact]
    public void GetSaleItemQuantityWithExistingProductReturnsQuantity()
    {
        // Arrange
        var productId = 1;
        var quantity = 5;
        var saleItems = new List<SaleItem> { CreateSaleItem(productId, quantity: quantity) };
        var sale = new Sale(saleItems);
        
        // Act
        var result = sale.GetSaleItemQuantity(productId);
        
        // Assert
        result.Should().Be(quantity);
    }
    
    [Fact]
    public void GetSaleItemQuantityWithNonExistingProductReturnsZero()
    {
        // Arrange
        var saleItems = new List<SaleItem> { CreateSaleItem(1) };
        var sale = new Sale(saleItems);
        
        // Act
        var result = sale.GetSaleItemQuantity(999); // Non-existing product ID
        
        // Assert
        result.Should().Be(0);
    }
    
    [Fact]
    public void ApplyDiscountWithValidDiscountSetsDiscountAmount()
    {
        // Arrange
        var sale = new Sale(new List<SaleItem> { CreateSaleItem() });
        var discount = new Money(10m, CurrencyCode.USD);
        
        // Act
        sale.ApplyDiscount(discount);
        
        // Assert
        sale.DiscountAmount.Should().Be(discount);
    }
    
    [Fact]
    public void ApplyDiscountWhenSaleIsCompletedThrowsSaleCannotBeModifiedException()
    {
        // Arrange
        var sale = new Sale(new List<SaleItem> { CreateSaleItem() });
        
        // Use reflection to set the status since we can't complete a sale directly
        var statusProperty = typeof(Sale).GetProperty("Status");
        statusProperty?.SetValue(sale, SaleStatus.Completed);
        
        // Act
        Action act = () => sale.ApplyDiscount(new Money(10m, CurrencyCode.USD));
        
        // Assert
        act.Should().Throw<SaleCannotBeModifiedDomainException>()
            .Where(e => e.SaleId == sale.Id && e.CurrentStatus == SaleStatus.Completed);
    }
    
    [Fact]
    public void CancelSaleWithPendingSaleCancelsSaleAndRaisesDomainEvent()
    {
        // Arrange
        var sale = new Sale(new List<SaleItem> { CreateSaleItem() });
        var reason = "Customer cancelled";
        
        // Act
        sale.CancelSale(reason);
        
        // Assert
        sale.Status.Should().Be(SaleStatus.Cancelled);
        sale.CancellationReason.Should().Be(reason);
        
        // Verify domain event was raised
        sale.DomainEvents.Should().ContainSingle(e => e is SaleCanceledDomainEvent);
        var canceledEvent = (SaleCanceledDomainEvent)sale.DomainEvents.First(e => e is SaleCanceledDomainEvent);
        canceledEvent.SaleId.Should().Be(sale.Id);
        canceledEvent.Reason.Should().Be(reason);
    }
    
    [Fact]
    public void CancelSaleWithCompletedSaleThrowsSaleCannotBeModifiedException()
    {
        // Arrange
        var sale = new Sale(new List<SaleItem> { CreateSaleItem() });
        
        // Use reflection to set the status since we can't complete a sale directly
        var statusProperty = typeof(Sale).GetProperty("Status");
        statusProperty?.SetValue(sale, SaleStatus.Completed);
        
        // Act
        Action act = () => sale.CancelSale("Test cancellation");
        
        // Assert
        act.Should().Throw<SaleCannotBeModifiedDomainException>()
            .Where(e => e.SaleId == sale.Id && e.CurrentStatus == SaleStatus.Completed);
    }
}
