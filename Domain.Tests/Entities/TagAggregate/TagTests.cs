// using Domain.Entities.TagAggregate;
//
// namespace Domain.Tests.Entities.TagAggregate;
//
// public class TagTests
// {
//     private readonly string _name = "Test Tag";
//     
//     [Fact]
//     public void ConstructorWithValidNameCreatesTag()
//     {
//         // Arrange & Act
//         var tag = new Tag(_name);
//         
//         // Assert
//         tag.Name.Should().Be(_name);
//         tag.Slug.Should().Be("test-tag"); // Assuming the Tag class converts name to slug
//     }
//     
//     [Theory]
//     [InlineData(null)]
//     [InlineData("")]
//     [InlineData(" ")]
//     public void ConstructorWithInvalidNameThrowsArgumentException(string invalidName)
//     {
//         // Arrange & Act
//         Action act = () => _ = new Tag(invalidName);
//         
//         // Assert
//         act.Should().Throw<ArgumentException>();
//             
//     }
//     
//     [Fact]
//     public void UpdateNameWithValidNameUpdatesNameAndSlug()
//     {
//         // Arrange
//         var tag = new Tag(_name);
//         var newName = "Updated Tag";
//         
//         // Act
//         tag.Update(newName);
//         
//         // Assert
//         tag.Name.Should().Be(newName);
//         tag.Slug.Should().Be("updated-tag");
//     }
//     
//     [Theory]
//     [InlineData(null)]
//     [InlineData("")]
//     [InlineData(" ")]
//     public void UpdateNameWithInvalidNameThrowsArgumentException(string invalidName)
//     {
//         // Arrange
//         var tag = new Tag(_name);
//         
//         // Act
//         var act = () => tag.Update(invalidName);
//         
//         // Assert
//         act.Should().Throw<ArgumentException>();
//             
//     }
//     
//     [Fact]
//     public void GenerateSlugCreatesSlugFromName()
//     {
//         // Arrange
//         var tagName = "Test Tag With Spaces & Special Characters!";
//         
//         // Act
//         var tag = new Tag(tagName);
//         
//         // Assert
//         tag.Slug.Should().Be("test-tag-with-spaces-special-characters");
//     }
// }
