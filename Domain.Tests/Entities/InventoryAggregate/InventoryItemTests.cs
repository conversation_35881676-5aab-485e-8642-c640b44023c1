using Domain.Entities.InventoryAggregate;
using Domain.Enums;
using Domain.Exceptions;
using Domain.ValueObjects;

namespace Domain.Tests.Entities.InventoryAggregate;

public class InventoryItemTests
{
    private readonly int _productId = 1;
    private readonly int _quantity = 10;
    private readonly int _threshold = 0;
    private readonly Money _actualPrice = new Money(50m, CurrencyCode.USD);
    private readonly Money _sellingPrice = new Money(100m, CurrencyCode.USD);

    [Fact]
    public void ConstructorWithValidParametersCreatesInventoryItem()
    {
        // Arrange & Act
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);

        // Assert
        item.ProductId.Should().Be(_productId);
        item.Quantity.Should().Be(_quantity);
        item.ReservedQuantity.Should().Be(0);
        item.SoldQuantity.Should().Be(0);
        item.TotalSoldQuantity.Should().Be(0);
        item.TotalWastedQuantity.Should().Be(0);
        item.Status.Should().Be(InventoryItemStatus.Active);
        item.Price.ActualPrice.Should().Be(_actualPrice);
        item.Price.SellingPrice.Should().Be(_sellingPrice);
        item.Prices.Should().HaveCount(1);
        item.WasteRecords.Should().BeEmpty();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void ConstructorWithInvalidProductIdThrowsArgumentException(int invalidProductId)
    {
        // Arrange & Act
        var act = () =>
        {
            _ = new InventoryItem(invalidProductId, _quantity, _actualPrice, _sellingPrice, _threshold);
        };

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ConstructorWithNegativeQuantityThrowsArgumentException()
    {
        // Arrange & Act
        var act = () =>
        {
            _ = new InventoryItem(_productId, -1, _actualPrice, _sellingPrice, _threshold);
        };

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ReserveWithSufficientQuantityDecreasesQuantityAndIncreasesReservedQuantity()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);
        var reserveQuantity = 3;

        // Act
        item.Reserve(reserveQuantity);

        // Assert
        item.Quantity.Should().Be(_quantity - reserveQuantity);
        item.ReservedQuantity.Should().Be(reserveQuantity);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void ReserveWithInvalidQuantityThrowsArgumentException(int invalidQuantity)
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);

        // Act
        Action act = () => item.Reserve(invalidQuantity);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ReserveWithInsufficientQuantityThrowsInsufficientStockDomainException()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);
        var reserveQuantity = _quantity + 1;

        // Act
        Action act = () => item.Reserve(reserveQuantity);

        // Assert
        act.Should().Throw<InsufficientStockDomainException>()
            .Where(e => e.ProductId == _productId && e.CurrentStock == _quantity && e.RequestedQuantity == reserveQuantity);
    }

    [Fact]
    public void ReleaseWithSufficientReservedQuantityIncreasesQuantityAndDecreasesReservedQuantity()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);
        var reserveQuantity = 3;
        var releaseQuantity = 2;

        item.Reserve(reserveQuantity);

        // Act
        item.Release(releaseQuantity);

        // Assert
        item.Quantity.Should().Be(_quantity - reserveQuantity + releaseQuantity);
        item.ReservedQuantity.Should().Be(reserveQuantity - releaseQuantity);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void ReleaseWithInvalidQuantityThrowsArgumentException(int invalidQuantity)
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);
        item.Reserve(3);

        // Act
        Action act = () => item.Release(invalidQuantity);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ReleaseWithInsufficientReservedQuantityThrowsInventoryItemInsufficientReservedStockDomainException()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);
        var reserveQuantity = 3;
        var releaseQuantity = reserveQuantity + 1;

        item.Reserve(reserveQuantity);

        // Act
        Action act = () => item.Release(releaseQuantity);

        // Assert
        act.Should().Throw<InventoryItemInsufficientReservedStockDomainException>()
            .Where(e => e.ProductId == _productId && e.ReservedQuantity == reserveQuantity && e.RequestedQuantity == releaseQuantity);
    }

    [Fact]
    public void ConfirmReservationWithValidParametersReleasesAndSellsItems()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);
        var reserveQuantity = 3;

        item.Reserve(reserveQuantity);

        // Act
        item.ConfirmReservation(reserveQuantity);

        // Assert
        item.Quantity.Should().Be(_quantity - reserveQuantity);
        item.ReservedQuantity.Should().Be(0);
        item.SoldQuantity.Should().Be(reserveQuantity);
        item.TotalSoldQuantity.Should().Be(reserveQuantity);
    }

    [Fact]
    public void RecordWasteWithValidParametersDecreasesQuantityAndAddsWasteRecord()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);
        var wasteQuantity = 3;
        var wasteReason = "Expired";

        // Act
        item.RecordWaste(wasteQuantity, wasteReason);

        // Assert
        item.Quantity.Should().Be(_quantity - wasteQuantity);
        item.TotalWastedQuantity.Should().Be(wasteQuantity);
        item.WasteRecords.Should().HaveCount(1);
        item.WasteRecords.First().Quantity.Should().Be(wasteQuantity);
        item.WasteRecords.First().Reason.Should().Be(wasteReason);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void RecordWasteWithInvalidQuantityThrowsArgumentException(int invalidQuantity)
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);

        // Act
        Action act = () => item.RecordWaste(invalidQuantity, "Expired");

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData(" ")]
    public void RecordWasteWithInvalidReasonThrowsArgumentException(string invalidReason)
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);

        // Act
        Action act = () => item.RecordWaste(1, invalidReason);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void RecordWasteWithInsufficientQuantityThrowsInsufficientStockDomainException()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);
        var wasteQuantity = _quantity + 1;

        // Act
        Action act = () => item.RecordWaste(wasteQuantity, "Expired");

        // Assert
        act.Should().Throw<InsufficientStockDomainException>()
            .Where(e => e.ProductId == _productId && e.CurrentStock == _quantity && e.RequestedQuantity == wasteQuantity);
    }

    [Fact]
    public void UpdatePriceWithValidParametersAddsPriceToHistoryAndUpdatesCurrent()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);
        var newActualPrice = new Money(60m, CurrencyCode.USD);
        var newSellingPrice = new Money(120m, CurrencyCode.USD);

        // Act
        item.UpdatePrice(newActualPrice, newSellingPrice);

        // Assert
        item.Price.ActualPrice.Should().Be(newActualPrice);
        item.Price.SellingPrice.Should().Be(newSellingPrice);
        item.Prices.Should().HaveCount(2);
        item.Prices.Last().ActualPrice.Should().Be(newActualPrice);
        item.Prices.Last().SellingPrice.Should().Be(newSellingPrice);
    }

    [Fact]
    public void AddQuantityWithValidQuantityIncreasesQuantity()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);
        var additionalQuantity = 5;

        // Act
        item.AddQuantity(additionalQuantity);

        // Assert
        item.Quantity.Should().Be(_quantity + additionalQuantity);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void AddQuantityWithInvalidQuantityThrowsArgumentException(int invalidQuantity)
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);

        // Act
        Action act = () => item.AddQuantity(invalidQuantity);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void DeactivateSetsStatusToInactive()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);

        // Act
        item.DeActivate();

        // Assert
        item.Status.Should().Be(InventoryItemStatus.Inactive);
    }

    [Fact]
    public void UpdateStatusWithZeroQuantitySetsStatusToOutOfStock()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);

        // Act - Waste all items to set quantity to zero
        item.RecordWaste(_quantity, "Testing");

        // Assert
        item.Status.Should().Be(InventoryItemStatus.OutOfStock);
    }

    [Fact]
    public void IsThresholdReachedWhenQuantityEqualsThresholdReturnsTrue()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);

        item.SetThreshold(_quantity);

        // Act & Assert
        item.IsThresholdReached.Should().BeTrue();
    }

    [Fact]
    public void IsThresholdReachedWhenQuantityLessThanThresholdReturnsTrue()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);

        item.SetThreshold(_quantity + 1);

        // Act & Assert
        item.IsThresholdReached.Should().BeTrue();
    }

    [Fact]
    public void IsThresholdReachedWhenQuantityGreaterThanThresholdReturnsFalse()
    {
        // Arrange
        var item = new InventoryItem(_productId, _quantity, _actualPrice, _sellingPrice, _threshold);

        item.SetThreshold(_quantity - 1);

        // Act & Assert
        item.IsThresholdReached.Should().BeFalse();
    }
}
