using Domain.Entities.InventoryAggregate;
using Domain.Enums;
using Domain.Events;
using Domain.Exceptions;
using Domain.ValueObjects;

namespace Domain.Tests.Entities.InventoryAggregate;

public class InventoryTests
{
    private readonly Money _defaultBalance = new Money(1000m, CurrencyCode.USD);
    
    [Fact]
    public void ConstructorWithValidParametersCreatesInventory()
    {
        // Arrange & Act
        var inventory = new Inventory(_defaultBalance);
        
        // Assert
        inventory.Balance.Should().Be(_defaultBalance);
        inventory.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        inventory.Items.Should().BeEmpty();
    }
    
    [Fact]
    public void ConstructorWithSpecificDateSetsCreatedAtToSpecifiedDate()
    {
        // Arrange
        var specificDate = new DateTime(2023, 1, 1);
        
        // Act
        var inventory = new Inventory(_defaultBalance, specificDate);
        
        // Assert
        inventory.CreatedAt.Should().Be(specificDate);
    }
    
    [Fact]
    public void AddItemWithValidParametersAddsItemToInventory()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var quantity = 10;
        var actualPrice = new Money(50m, CurrencyCode.USD);
        var sellingPrice = new Money(100m, CurrencyCode.USD);
        
        // Act
        inventory.AddItem(productId, quantity, actualPrice, sellingPrice, 0);
        
        // Assert
        inventory.Items.Should().HaveCount(1);
        var item = inventory.Items.First();
        item.ProductId.Should().Be(productId);
        item.Quantity.Should().Be(quantity);
        item.Price.ActualPrice.Should().Be(actualPrice);
        item.Price.SellingPrice.Should().Be(sellingPrice);
    }
    
    [Fact]
    public void AddItemWithDuplicateProductIdThrowsInventoryItemDuplicateDomainException()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        inventory.AddItem(productId, 10, new Money(50m, CurrencyCode.USD), new Money(100m, CurrencyCode.USD), 0);
        
        // Act
        Action act = () => inventory.AddItem(productId, 5, new Money(60m, CurrencyCode.USD), new Money(120m, CurrencyCode.USD), 0);
        
        // Assert
        act.Should().Throw<InventoryItemDuplicateDomainException>()
            .Where(e => e.InventoryId == inventory.Id && e.ProductId == productId);
    }
    
    [Fact]
    public void GetItemPriceWithExistingProductIdReturnsSellingPrice()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var sellingPrice = new Money(100m, CurrencyCode.USD);
        inventory.AddItem(productId, 10, new Money(50m, CurrencyCode.USD), sellingPrice, 0);
        
        // Act
        var price = inventory.GetItemPrice(productId);
        
        // Assert
        price.Should().Be(sellingPrice);
    }
    
    [Fact]
    public void GetItemPriceWithNonExistingProductIdThrowsInventoryItemNotFoundException()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var nonExistingProductId = 999;
        
        // Act
        Action act = () => inventory.GetItemPrice(nonExistingProductId);
        
        // Assert
        act.Should().Throw<InventoryItemNotFoundDomainException>()
            .Where(e => e.InventoryId == inventory.Id && e.ProductId == nonExistingProductId);
    }
    
    [Fact]
    public void UpdateItemPriceWithExistingProductIdUpdatesPriceAndRaisesDomainEvent()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var oldActualPrice = new Money(50m, CurrencyCode.USD);
        var oldSellingPrice = new Money(100m, CurrencyCode.USD);
        var newActualPrice = new Money(60m, CurrencyCode.USD);
        var newSellingPrice = new Money(120m, CurrencyCode.USD);
        
        inventory.AddItem(productId, 10, oldActualPrice, oldSellingPrice, 0);
        
        // Act
        inventory.UpdateItemPrice(productId, newActualPrice, newSellingPrice);
        
        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Price.ActualPrice.Should().Be(newActualPrice);
        item.Price.SellingPrice.Should().Be(newSellingPrice);
        
        // Verify domain event was raised
        inventory.DomainEvents.Should().ContainSingle(e => e is InventoryItemPriceUpdatedDomainEvent);
        var priceUpdatedEvent = (InventoryItemPriceUpdatedDomainEvent)inventory.DomainEvents.First(e => e is InventoryItemPriceUpdatedDomainEvent);
        priceUpdatedEvent.InventoryId.Should().Be(inventory.Id);
        priceUpdatedEvent.ProductId.Should().Be(productId);
        priceUpdatedEvent.OldActualPrice.Should().Be(oldActualPrice);
        priceUpdatedEvent.NewActualPrice.Should().Be(newActualPrice);
        priceUpdatedEvent.OldSellingPrice.Should().Be(oldSellingPrice);
        priceUpdatedEvent.NewSellingPrice.Should().Be(newSellingPrice);
    }
    
    [Fact]
    public void ReserveItemWithSufficientQuantityDecreasesQuantityAndIncreasesReservedQuantity()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var initialQuantity = 10;
        var reserveQuantity = 3;
        
        inventory.AddItem(productId, initialQuantity, new Money(50m, CurrencyCode.USD), new Money(100m, CurrencyCode.USD), 0);
        
        // Act
        inventory.ReserveItem(productId, reserveQuantity);
        
        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Quantity.Should().Be(initialQuantity - reserveQuantity);
        item.ReservedQuantity.Should().Be(reserveQuantity);
    }
    
    [Fact]
    public void ReserveItemWithInsufficientQuantityThrowsInsufficientStockDomainException()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var initialQuantity = 5;
        var reserveQuantity = 10;
        
        inventory.AddItem(productId, initialQuantity, new Money(50m, CurrencyCode.USD), new Money(100m, CurrencyCode.USD), 0);
        
        // Act
        Action act = () => inventory.ReserveItem(productId, reserveQuantity);
        
        // Assert
        act.Should().Throw<InsufficientStockDomainException>()
            .Where(e => e.ProductId == productId && e.CurrentStock == initialQuantity && e.RequestedQuantity == reserveQuantity);
    }
    
    [Fact]
    public void ReleaseItemWithSufficientReservedQuantityIncreasesQuantityAndDecreasesReservedQuantity()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var initialQuantity = 10;
        var reserveQuantity = 3;
        var releaseQuantity = 2;
        
        inventory.AddItem(productId, initialQuantity, new Money(50m, CurrencyCode.USD), new Money(100m, CurrencyCode.USD), 0);
        inventory.ReserveItem(productId, reserveQuantity);
        
        // Act
        inventory.ReleaseItem(productId, releaseQuantity);
        
        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Quantity.Should().Be(initialQuantity - reserveQuantity + releaseQuantity);
        item.ReservedQuantity.Should().Be(reserveQuantity - releaseQuantity);
    }
    
    [Fact]
    public void ReleaseItemWithInsufficientReservedQuantityThrowsInventoryItemInsufficientReservedStockDomainException()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var initialQuantity = 10;
        var reserveQuantity = 3;
        var releaseQuantity = 5;
        
        inventory.AddItem(productId, initialQuantity, new Money(50m, CurrencyCode.USD), new Money(100m, CurrencyCode.USD), 0);
        inventory.ReserveItem(productId, reserveQuantity);
        
        // Act
        Action act = () => inventory.ReleaseItem(productId, releaseQuantity);
        
        // Assert
        act.Should().Throw<InventoryItemInsufficientReservedStockDomainException>()
            .Where(e => e.ProductId == productId && e.ReservedQuantity == reserveQuantity && e.RequestedQuantity == releaseQuantity);
    }
    
    [Fact]
    public void ConfirmReservationWithValidParametersReleasesAndSellsItems()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var initialQuantity = 10;
        var reserveQuantity = 3;
        
        inventory.AddItem(productId, initialQuantity, new Money(50m, CurrencyCode.USD), new Money(100m, CurrencyCode.USD), 0);
        inventory.ReserveItem(productId, reserveQuantity);
        
        // Act
        inventory.ConfirmReservation(productId, reserveQuantity);
        
        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Quantity.Should().Be(initialQuantity - reserveQuantity);
        item.ReservedQuantity.Should().Be(0);
        item.SoldQuantity.Should().Be(reserveQuantity);
        item.TotalSoldQuantity.Should().Be(reserveQuantity);
    }
    
    [Fact]
    public void WasteItemWithValidParametersDecreasesQuantityAndRaisesDomainEvent()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var initialQuantity = 10;
        var wasteQuantity = 3;
        var wasteReason = "Expired";
        
        inventory.AddItem(productId, initialQuantity, new Money(50m, CurrencyCode.USD), new Money(100m, CurrencyCode.USD), 0);
        
        // Act
        inventory.WasteItem(productId, wasteQuantity, wasteReason);
        
        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Quantity.Should().Be(initialQuantity - wasteQuantity);
        item.TotalWastedQuantity.Should().Be(wasteQuantity);
        item.WasteRecords.Should().HaveCount(1);
        item.WasteRecords.First().Quantity.Should().Be(wasteQuantity);
        item.WasteRecords.First().Reason.Should().Be(wasteReason);
        
        // Verify domain event was raised
        inventory.DomainEvents.Should().ContainSingle(e => e is InventoryItemWastedDomainEvent);
        var wasteEvent = (InventoryItemWastedDomainEvent)inventory.DomainEvents.First(e => e is InventoryItemWastedDomainEvent);
        wasteEvent.ProductId.Should().Be(productId);
        wasteEvent.WastedQuantity.Should().Be(wasteQuantity);
        wasteEvent.Reason.Should().Be(wasteReason);
    }
    
    [Fact]
    public void WasteItemWithInsufficientQuantityThrowsInsufficientStockDomainException()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var initialQuantity = 5;
        var wasteQuantity = 10;
        var wasteReason = "Expired";
        
        inventory.AddItem(productId, initialQuantity, new Money(50m, CurrencyCode.USD), new Money(100m, CurrencyCode.USD), 0);
        
        // Act
        Action act = () => inventory.WasteItem(productId, wasteQuantity, wasteReason);
        
        // Assert
        act.Should().Throw<InsufficientStockDomainException>()
            .Where(e => e.ProductId == productId && e.CurrentStock == initialQuantity && e.RequestedQuantity == wasteQuantity);
    }
    
    [Fact]
    public void AddItemQuantityWithValidParametersIncreasesQuantity()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        var initialQuantity = 10;
        var additionalQuantity = 5;
        
        inventory.AddItem(productId, initialQuantity, new Money(50m, CurrencyCode.USD), new Money(100m, CurrencyCode.USD), 0);
        
        // Act
        inventory.AddItemQuantity(productId, additionalQuantity);
        
        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Quantity.Should().Be(initialQuantity + additionalQuantity);
    }
    
    [Fact]
    public void DeactivateItemWithExistingProductIdSetsStatusToInactive()
    {
        // Arrange
        var inventory = new Inventory(_defaultBalance);
        var productId = 1;
        
        inventory.AddItem(productId, 10, new Money(50m, CurrencyCode.USD), new Money(100m, CurrencyCode.USD), 0);
        
        // Act
        inventory.DeActivateItem(productId);
        
        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Status.Should().Be(InventoryItemStatus.Inactive);
    }
}
