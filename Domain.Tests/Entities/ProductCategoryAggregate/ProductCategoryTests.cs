using Domain.Exceptions;
using Domain.Interfaces;
using Domain.Services;

namespace Domain.Tests.Entities.ProductCategoryAggregate;

public class ProductCategoryTests
{
    private readonly string _name = "Test Category";
    private readonly Mock<IProductCategoryRepository> _repoMock;
    private readonly ProductCategoryDomainService _service;

    public ProductCategoryTests()
    {
        _repoMock = new Mock<IProductCategoryRepository>();
        _service = new ProductCategoryDomainService(_repoMock.Object);
    }

    [Fact]
    public async Task ConstructorWithValidNameCreatesProductCategory()
    {
        // Arrange
        _repoMock.Setup(r => r.IsCategoryNameUniqueAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var category = await _service.CreateAsync(_name);

        // Assert
        category.Name.Should().Be(_name);
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData(" ")]
    public void ConstructorWithInvalidNameThrowsArgumentException(string invalidName)
    {
        // Arrange
        _repoMock.Setup(r => r.IsCategoryNameUniqueAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var act = async () => await _service.CreateAsync(invalidName);

        // Assert
        act.Should().ThrowAsync<ArgumentException>();
    }

    [Fact]
    public async Task UpdateNameWithValidNameUpdatesCategoryName()
    {
        // Arrange
        _repoMock.Setup(r => r.IsCategoryNameUniqueAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var category = await _service.CreateAsync(_name);
        var newName = "Updated Category";

        // Act
        await _service.UpdateAsync(category, newName);

        // Assert
        category.Name.Should().Be(newName);
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData(" ")]
    public async Task UpdateNameWithInvalidNameThrowsArgumentException(string invalidName)
    {
        // Arrange
        _repoMock.Setup(r => r.IsCategoryNameUniqueAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var category = await _service.CreateAsync(_name);

        // Act
        var act = async () => await _service.UpdateAsync(category, invalidName);

        // Assert
        await act.Should().ThrowAsync<ArgumentException>();
    }

    [Fact]
    public async Task CreateAsyncShouldReturnProductCategoryWhenNameIsUnique()
    {
        // Arrange
        string name = "Electronics";
        _repoMock.Setup(r => r.IsCategoryNameUniqueAsync(name, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.CreateAsync(name);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(name, result.Name);
    }

    [Fact]
    public async Task CreateAsyncShouldThrowExceptionWhenNameIsNotUnique()
    {
        // Arrange
        string name = "Books";
        _repoMock.Setup(r => r.IsCategoryNameUniqueAsync(name, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var act = async () => await _service.CreateAsync(name);

        // Assert
        await act.Should().ThrowAsync<ProductCategoryAlreadyExistsDomainException>();
    }

    [Fact]
    public async Task TryToDeleteAsyncShouldThrowWhenCategoryIsInUse()
    {
        // Arrange
        _repoMock.Setup(r => r.IsCategoryInUseAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var act = async () => await _service.TryToDeleteAsync(1);

        // Assert
        await act.Should().ThrowAsync<ProductCategoryInUseDomainException>();
    }

    [Fact]
    public async Task TryToDeleteAsyncShouldNotThrowWhenCategoryIsNotInUse()
    {
        // Arrange
        _repoMock.Setup(r => r.IsCategoryInUseAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var act = async () => await _service.TryToDeleteAsync(1);

        // Assert
        await act.Should().NotThrowAsync();
    }
}
