using Domain.Entities.ProductAggregate;
using Domain.Exceptions;

namespace Domain.Tests.Entities.ProductAggregate;

public class ProductTests
{
    private readonly string _name = "Test Product";
    private readonly string _description = "Test Description";
    private readonly int _categoryId = 1;

    [Fact]
    public void ConstructorWithValidParametersCreatesProduct()
    {
        // Arrange & Act
        var product = new Product(_name, _description, _categoryId);

        // Assert
        product.Name.Should().Be(_name);
        product.Description.Should().Be(_description);
        product.CategoryId.Should().Be(_categoryId);
        product.ProductTags.Should().BeEmpty();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData(" ")]
    public void ConstructorWithInvalidNameThrowsArgumentException(string invalidName)
    {
        // Arrange & Act
        Action act = () => _ = new Product(invalidName, _description, _categoryId);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void ConstructorWithNullDescriptionThrowsArgumentException()
    {
        // Arrange & Act
        Action act = () => _ = new Product(_name, null!, _categoryId);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void ConstructorWithInvalidCategoryIdThrowsArgumentException(int invalidCategoryId)
    {
        // Arrange & Act
        Action act = () => _ = new Product(_name, _description, invalidCategoryId);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void UpdateDetailsWithValidParametersUpdatesProductDetails()
    {
        // Arrange
        var product = new Product(_name, _description, _categoryId);
        var newName = "Updated Product";
        var newDescription = "Updated Description";
        var newCategoryId = 2;

        // Act
        product.UpdateDetails(newName, newDescription, newCategoryId);

        // Assert
        product.Name.Should().Be(newName);
        product.Description.Should().Be(newDescription);
        product.CategoryId.Should().Be(newCategoryId);
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData(" ")]
    public void UpdateDetailsWithInvalidNameThrowsArgumentException(string invalidName)
    {
        // Arrange
        var product = new Product(_name, _description, _categoryId);

        // Act
        Action act = () => product.UpdateDetails(invalidName, _description, _categoryId);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void UpdateDetailsWithNullDescriptionThrowsArgumentException()
    {
        // Arrange
        var product = new Product(_name, _description, _categoryId);

        // Act
        Action act = () => product.UpdateDetails(_name, null!, _categoryId);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void UpdateDetailsWithInvalidCategoryIdThrowsArgumentException(int invalidCategoryId)
    {
        // Arrange
        var product = new Product(_name, _description, _categoryId);

        // Act
        Action act = () => product.UpdateDetails(_name, _description, invalidCategoryId);

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void AddTagWithValidTagIdAddsTagToProduct()
    {
        // Arrange
        var product = new Product(_name, _description, _categoryId);
        var tagId = 1;

        // Act
        product.AddTag(tagId);

        // Assert
        product.ProductTags.Should().ContainSingle();
        product.ProductTags.Should().Contain(x => x.TagId == tagId);
    }

    [Fact]
    public void AddTagWithDuplicateTagIdThrowsTagAlreadyExistDomainException()
    {
        // Arrange
        var product = new Product(_name, _description, _categoryId);
        var tagId = 1;

        // Act
        var act = () =>
        {
            product.AddTag(tagId);
            product.AddTag(tagId); // Add the same tag again
        };


        // Assert
        act.Should().Throw<ProductTagAlreadyExistDomainException>();
    }

    [Fact]
    public void RemoveTagWithExistingTagIdRemovesTagFromProduct()
    {
        // Arrange
        var product = new Product(_name, _description, _categoryId);
        var tagId = 1;
        product.AddTag(tagId);

        // Act
        product.RemoveTag(tagId);

        // Assert
        product.ProductTags.Should().BeEmpty();
    }

    [Fact]
    public void RemoveTagWithNonExistingTagIdThrowsTagNotFoundDomainException()
    {
        // Arrange
        var product = new Product(_name, _description, _categoryId);

        // Act
        var act = () =>
        {
            product.RemoveTag(999); // Non-existing tag ID
        };

        // Assert
        act.Should().Throw<ProductTagNotFoundDomainException>();
    }

    [Fact]
    public void ClearTagsRemovesAllTags()
    {
        // Arrange
        var product = new Product(_name, _description, _categoryId);
        product.AddTag(1);
        product.AddTag(2);
        product.AddTag(3);

        // Act
        product.ClearTags();

        // Assert
        product.ProductTags.Should().BeEmpty();
    }
}
