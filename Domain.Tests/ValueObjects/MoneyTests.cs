using Domain.Enums;
using Domain.Exceptions;
using Domain.ValueObjects;

namespace Domain.Tests.ValueObjects;

public class MoneyTests
{
    [Fact]
    public void ConstructorWithValidValuesCreatesMoneyObject()
    {
        // Arrange & Act
        var money = new Money(100.50m, "USD");

        // Assert
        money.Amount.Should().Be(100.50m);
        money.CurrencyCode.ToString().Should().Be("USD");
    }

    [Fact]
    public void ConstructorWithNegativeAmountThrowsException()
    {
        // Arrange & Act
        Action act = () => _ = new Money(-10m, "USD");

        // Assert
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void AdditionWithSameCurrencyReturnsSumOfAmounts()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(50m, "USD");

        // Act
        var result = money1 + money2;

        // Assert
        result.Amount.Should().Be(150m);
        result.CurrencyCode.ToString().Should().Be("USD");
    }

    [Fact]
    public void AdditionWithDifferentCurrenciesThrowsCurrencyMismatchException()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(50m, "EUR");

        // Act
        Action act = () => { _ = money1 + money2; };

        // Assert
        act.Should().Throw<CurrencyMismatchDomainException>();
    }

    [Fact]
    public void AdditionWithDecimalValueAddsToAmount()
    {
        // Arrange
        var money = new Money(100m, "USD");
        var amount = 50m;

        // Act
        var result = money + amount;

        // Assert
        result.Amount.Should().Be(150m);
        result.CurrencyCode.ToString().Should().Be("USD");
    }

    [Fact]
    public void SubtractionWithSameCurrencyReturnsCorrectDifference()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(30m, "USD");

        // Act
        var result = money1 - money2;

        // Assert
        result.Amount.Should().Be(70m);
        result.CurrencyCode.ToString().Should().Be("USD");
    }

    [Fact]
    public void SubtractionWithDifferentCurrenciesThrowsCurrencyMismatchException()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(30m, "EUR");

        // Act
        Action act = () => { _ = money1 - money2; };

        // Assert
        act.Should().Throw<CurrencyMismatchDomainException>();
    }

    [Fact]
    public void ConvertToWithValidExchangeRateReturnsConvertedMoney()
    {
        // Arrange
        var money = new Money(100m, "USD");
        var exchangeRate = 0.85m; // USD to EUR rate

        // Act
        var result = money.ConvertTo(Enum.Parse<CurrencyCode>("EUR"), exchangeRate);

        // Assert
        result.Amount.Should().Be(85m);
        result.CurrencyCode.ToString().Should().Be("EUR");
    }

    [Fact]
    public void ConvertToWithSameCurrencyReturnsSameMoneyObject()
    {
        // Arrange
        var money = new Money(100m, "USD");

        // Act
        var result = money.ConvertTo(Enum.Parse<CurrencyCode>("USD"), 1.0m);

        // Assert
        result.Should().Be(money);
    }

    [Fact]
    public void ConvertToWithZeroOrNegativeExchangeRateThrowsException()
    {
        // Arrange
        var money = new Money(100m, "USD");

        // Act & Assert
        Action act1 = () => money.ConvertTo(Enum.Parse<CurrencyCode>("EUR"), 0m);
        act1.Should().Throw<ArgumentException>();

        Action act2 = () => money.ConvertTo(Enum.Parse<CurrencyCode>("EUR"), -1m);
        act2.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void EqualsWithSameValuesReturnsTrue()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(100m, "USD");

        // Act & Assert
        money1.Should().Be(money2);
    }

    [Fact]
    public void EqualsWithDifferentValuesReturnsFalse()
    {
        // Arrange
        var money1 = new Money(100m, "USD");
        var money2 = new Money(100m, "EUR");
        var money3 = new Money(200m, "USD");

        // Act & Assert
        money1.Should().NotBe(money2);
        money1.Should().NotBe(money3);
    }

    [Fact]
    public void ToStringReturnsFormattedString()
    {
        // Arrange
        var money = new Money(100.50m, "USD");

        // Act
        var result = money.ToString();

        // Assert
        result.Should().Be("100.50 USD");
    }

    [Fact]
    public void ZeroReturnsMoney()
    {
        // Act
        var zero = Money.Zero;

        // Assert
        zero.Amount.Should().Be(0m);
    }
}
