using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Domain.Exceptions;
using Domain.Interfaces;
using Domain.Services;
using Domain.ValueObjects;

namespace Domain.Tests.Services;

public class SaleDomainServiceTests
{
    private readonly Mock<ICurrencyConverter> _currencyConverterMock;
    private readonly SaleDomainService _domainService;
    private readonly string _defaultCurrency = "USD";

    public SaleDomainServiceTests()
    {
        _currencyConverterMock = new Mock<ICurrencyConverter>();
        _domainService = new SaleDomainService(_currencyConverterMock.Object);

        // Setup default currency converter behavior
        _currencyConverterMock
            .Setup(c => c.ConvertAsync(It.IsAny<Money>(), It.IsAny<CurrencyCode>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Money source, CurrencyCode targetCurrency, CancellationToken _) =>
                source.CurrencyCode == targetCurrency
                    ? source
                    : new Money(source.Amount, targetCurrency));
    }

    [Fact]
    public async Task CompleteSaleAsyncWithValidSaleAndInventoryCompletesTheSale()
    {
        // Arrange
        var sale = CreateSale();

        // Act
        await _domainService.CompleteSaleAsync(sale, _defaultCurrency);

        // Assert
        sale.Status.Should().Be(SaleStatus.Completed);
        sale.TotalAmount.Should().NotBeNull();
        sale.FinalAmount.Should().NotBeNull();
    }

    [Fact]
    public async Task CompleteSaleAsyncWithEmptySaleThrowsSaleCannotBeEmptyDomainException()
    {
        // Arrange
        var sale = new Sale(new List<SaleItem>());

        // Act
        Func<Task> act = async () => await _domainService.CompleteSaleAsync(sale, _defaultCurrency);

        // Assert
        await act.Should().ThrowAsync<SaleCannotBeEmptyDomainException>()
            .Where(e => e.SaleId == sale.Id);
    }

    [Fact]
    public async Task CompleteSaleAsyncWithCompletedSaleThrowsSaleCannotBeModifiedDomainException()
    {
        // Arrange
        var sale = CreateSale();

        // Set sale status to completed
        var statusProperty = typeof(Sale).GetProperty("Status");
        statusProperty?.SetValue(sale, SaleStatus.Completed);

        // Act
        Func<Task> act = async () => await _domainService.CompleteSaleAsync(sale, _defaultCurrency);

        // Assert
        await act.Should().ThrowAsync<SaleCannotBeModifiedDomainException>()
            .Where(e => e.SaleId == sale.Id && e.CurrentStatus == SaleStatus.Completed);
    }

    [Fact]
    public async Task CompleteSaleAsyncWithCancelledSaleThrowsSaleCannotBeModifiedDomainException()
    {
        // Arrange
        var sale = CreateSale();
        sale.CancelSale("Test cancellation");

        // Act
        Func<Task> act = async () => await _domainService.CompleteSaleAsync(sale, _defaultCurrency);

        // Assert
        await act.Should().ThrowAsync<SaleCannotBeModifiedDomainException>()
            .Where(e => e.SaleId == sale.Id && e.CurrentStatus == SaleStatus.Cancelled);
    }

    [Fact]
    public async Task CompleteSaleAsyncWithDifferentCurrenciesConvertsToTargetCurrency()
    {
        // Arrange
        var saleItems = new List<SaleItem>
        {
            new SaleItem(1, "Product 1", new Money(100m, CurrencyCode.EUR), 1)
        };
        var sale = new Sale(saleItems);

        // Setup currency converter to convert EUR to USD
        _currencyConverterMock
            .Setup(c => c.ConvertAsync(It.Is<Money>(m => m.CurrencyCode == CurrencyCode.EUR), CurrencyCode.USD, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Money(120m, CurrencyCode.USD)); // Assuming 1 EUR = 1.2 USD

        // Act
        await _domainService.CompleteSaleAsync(sale, "USD");

        // Assert
        sale.Status.Should().Be(SaleStatus.Completed);
        sale.TotalAmount.CurrencyCode.Should().Be(CurrencyCode.USD);
        sale.FinalAmount.CurrencyCode.Should().Be(CurrencyCode.USD);

        // Verify currency converter was called
        _currencyConverterMock.Verify(
            c => c.ConvertAsync(It.Is<Money>(m => m.CurrencyCode == CurrencyCode.EUR), CurrencyCode.USD, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task CompleteSaleAsyncWithDiscountAppliesDiscountToFinalAmount()
    {
        // Arrange
        var saleItems = new List<SaleItem>
        {
            new SaleItem(1, "Product 1", new Money(100m, _defaultCurrency), 1)
        };
        var discount = new Money(20m, _defaultCurrency);
        var sale = new Sale(saleItems, discount);

        // Act
        await _domainService.CompleteSaleAsync(sale, _defaultCurrency);

        // Assert
        sale.Status.Should().Be(SaleStatus.Completed);
        sale.TotalAmount.Amount.Should().Be(100m);
        sale.DiscountAmount.Amount.Should().Be(20m);
        sale.FinalAmount.Amount.Should().Be(80m); // 100 - 20
    }

    private Sale CreateSale()
    {
        var saleItems = new List<SaleItem>
        {
            new SaleItem(1, "Product 1", new Money(100m, _defaultCurrency), 2),
            new SaleItem(2, "Product 2", new Money(50m, _defaultCurrency), 1)
        };
        return new Sale(saleItems);
    }
}
