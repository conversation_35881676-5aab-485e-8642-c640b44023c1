using Application.EventHandlers;
using Application.Interfaces;
using Domain.Entities.InventoryAggregate;
using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Domain.Events;
using Domain.Interfaces;
using Domain.Services;
using Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace Application.Tests.EventHandlers;

public class SaleCompletedDomainEventHandlerTests
{
    private readonly Mock<ISaleRepository> _saleRepositoryMock;
    private readonly Mock<IInventoryRepository> _inventoryRepositoryMock;
    private readonly InventoryDomainService _inventoryDomainService;
    private readonly Mock<ICurrencyConverter> _currencyConverterMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<ILogger<SaleCompletedDomainEventHandler>> _loggerMock;
    private readonly SaleCompletedDomainEventHandler _handler;

    public SaleCompletedDomainEventHandlerTests()
    {
        _saleRepositoryMock = new Mock<ISaleRepository>();
        _inventoryRepositoryMock = new Mock<IInventoryRepository>();
        _currencyConverterMock = new Mock<ICurrencyConverter>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _loggerMock = new Mock<ILogger<SaleCompletedDomainEventHandler>>();

        _inventoryDomainService = new InventoryDomainService(_inventoryRepositoryMock.Object);

        _handler = new SaleCompletedDomainEventHandler(
            _saleRepositoryMock.Object,
            _inventoryRepositoryMock.Object,
            _inventoryDomainService,
            _currencyConverterMock.Object,
            _unitOfWorkMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task HandleAsyncWithValidSaleAddsAmountToInventoryBalance()
    {
        // Arrange
        var saleId = Guid.NewGuid();
        var @event = new SaleCompletedDomainEvent(saleId);

        var saleItems = new List<SaleItem>
        {
            new SaleItem(1, "Product 1", new Money(100m, CurrencyCode.USD), 2)
        };
        var sale = new Sale(saleItems);

        // Use reflection to set the final amount since CompleteSale is internal
        var finalAmountProperty = typeof(Sale).GetProperty("FinalAmount");
        finalAmountProperty?.SetValue(sale, new Money(200m, CurrencyCode.USD));

        var inventory = new Inventory(new Money(1000m, CurrencyCode.USD));

        _saleRepositoryMock.Setup(x => x.GetByIdOrDefaultAsync(saleId, default))
            .ReturnsAsync(sale);
        _inventoryRepositoryMock.Setup(x => x.GetMostRecentInventoryAsync(default))
            .ReturnsAsync(inventory);
        _saleRepositoryMock.Setup(x => x.GetSalesByDateRangeAsync(It.IsAny<DateTime>(), It.IsAny<DateTime>(), default))
            .ReturnsAsync(new List<Sale>());

        // Act
        await _handler.HandleAsync(@event);

        // Assert
        inventory.Balance.Amount.Should().Be(1200m);
        _inventoryRepositoryMock.Verify(x => x.UpdateAsync(inventory, default), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(default), Times.Once);
    }

    [Fact]
    public async Task HandleAsyncWithNonExistentSaleLogsWarningAndReturns()
    {
        // Arrange
        var saleId = Guid.NewGuid();
        var @event = new SaleCompletedDomainEvent(saleId);

        _saleRepositoryMock.Setup(x => x.GetByIdOrDefaultAsync(saleId, default))
            .ReturnsAsync((Sale?)null);

        // Act
        await _handler.HandleAsync(@event);

        // Assert
        _inventoryRepositoryMock.Verify(x => x.UpdateAsync(It.IsAny<Inventory>(), default), Times.Never);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(default), Times.Never);
    }

    [Fact]
    public async Task HandleAsyncWithCurrencyConversionConvertsAmountCorrectly()
    {
        // Arrange
        var saleId = Guid.NewGuid();
        var @event = new SaleCompletedDomainEvent(saleId);

        var saleItems = new List<SaleItem>
        {
            new SaleItem(1, "Product 1", new Money(100m, CurrencyCode.EUR), 2)
        };
        var sale = new Sale(saleItems);

        // Use reflection to set the final amount
        var finalAmountProperty = typeof(Sale).GetProperty("FinalAmount");
        finalAmountProperty?.SetValue(sale, new Money(200m, CurrencyCode.EUR));

        var inventory = new Inventory(new Money(1000m, CurrencyCode.USD));
        var convertedAmount = new Money(220m, CurrencyCode.USD); // 200 EUR * 1.1 rate

        _saleRepositoryMock.Setup(x => x.GetByIdOrDefaultAsync(saleId, default))
            .ReturnsAsync(sale);
        _inventoryRepositoryMock.Setup(x => x.GetMostRecentInventoryAsync(default))
            .ReturnsAsync(inventory);
        _currencyConverterMock.Setup(x => x.ConvertAsync(
                It.Is<Money>(m => m.Amount == 200m && m.CurrencyCode == CurrencyCode.EUR),
                CurrencyCode.USD,
                default))
            .ReturnsAsync(convertedAmount);
        _saleRepositoryMock.Setup(x => x.GetSalesByDateRangeAsync(It.IsAny<DateTime>(), It.IsAny<DateTime>(), default))
            .ReturnsAsync(new List<Sale>());

        // Act
        await _handler.HandleAsync(@event);

        // Assert
        inventory.Balance.Amount.Should().Be(1220m);
        _currencyConverterMock.Verify(x => x.ConvertAsync(
            It.Is<Money>(m => m.Amount == 200m && m.CurrencyCode == CurrencyCode.EUR),
            CurrencyCode.USD,
            default), Times.Once);
    }
}