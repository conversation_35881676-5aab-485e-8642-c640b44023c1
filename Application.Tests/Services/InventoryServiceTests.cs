using Application.DTOs.InventoryDTOs;
using Application.Interfaces;
using Application.Services;
using Domain.Entities.InventoryAggregate;
using Domain.Entities.ProductAggregate;
using Domain.Enums;
using Domain.Exceptions;
using Domain.Interfaces;
using Domain.Managers;
using Domain.ValueObjects;

namespace Application.Tests.Services;

public class InventoryServiceTests
{
    private readonly Mock<IInventoryRepository> _inventoryRepositoryMock;
    private readonly Mock<IProductRepository> _productRepositoryMock;
    private readonly Mock<IInventoryManager> _inventoryManager;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly InventoryService _service;
    private readonly CurrencyCode _defaultCurrency = CurrencyCode.USD;

    public InventoryServiceTests()
    {
        _inventoryRepositoryMock = new Mock<IInventoryRepository>();
        _productRepositoryMock = new Mock<IProductRepository>();
        _inventoryManager = new Mock<IInventoryManager>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();

        _service = new InventoryService(
            _inventoryRepositoryMock.Object,
            _productRepositoryMock.Object,
            _inventoryManager.Object,
            _unitOfWorkMock.Object);
    }

    [Fact]
    public async Task CreateInventoryAsyncWithValidParametersCreatesAndSavesInventory()
    {
        // Arrange
        var createDto = new CreateInventoryDto
        (
            Balance: 1000m,
            Currency: _defaultCurrency.ToString()
        );

        // Act
        var result = await _service.CreateInventoryAsync(createDto);

        // Assert
        _inventoryRepositoryMock.Verify(
            r => r.AddAsync(It.Is<Inventory>(i =>
                    i.Id == result &&
                    i.Balance.Amount == createDto.Balance &&
                    i.Balance.CurrencyCode == Enum.Parse<CurrencyCode>(createDto.Currency)),
                It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task AddItemAsyncWithValidParametersAddsItemToCurrentInventory()
    {
        // Arrange
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));

        var productId = 1;
        var quantity = 10;
        var actualPrice = new Money(50m, _defaultCurrency);
        var sellingPrice = new Money(100m, _defaultCurrency);
        var product = new Product("Test Product", "Description", 1);
        var inventoryDto = new AddInventoryItemDto(productId, quantity, 50, 100, _defaultCurrency.ToString(), 0);

        _inventoryManager
            .Setup(r => r.GetCurrentInventoryAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(inventory);

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);

        // Act
        await _service.AddItemAsync(inventoryDto);

        // Assert
        inventory.Items.Should().ContainSingle(i => i.ProductId == productId);
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Quantity.Should().Be(quantity);
        item.Price.ActualPrice.Should().Be(actualPrice);
        item.Price.SellingPrice.Should().Be(sellingPrice);

        _inventoryRepositoryMock.Verify(
            r => r.UpdateAsync(inventory, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task UpdateItemPriceAsyncWithValidParametersUpdatesItemPrice()
    {
        // Arrange
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        var productId = 1;
        var actualPrice = new Money(60m, _defaultCurrency);
        var sellingPrice = new Money(120m, _defaultCurrency);

        var inventoryDto = new UpdateInventoryItemPriceDto(productId, 60, 120, _defaultCurrency.ToString());

        inventory.AddItem(productId, 10, new Money(50m, _defaultCurrency), new Money(100m, _defaultCurrency), 0);

        _inventoryManager
            .Setup(r => r.GetCurrentInventoryAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(inventory);

        // Act
        await _service.UpdateItemPriceAsync(inventoryDto);

        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Price.ActualPrice.Should().Be(actualPrice);
        item.Price.SellingPrice.Should().Be(sellingPrice);

        _inventoryRepositoryMock.Verify(
            r => r.UpdateAsync(inventory, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task UpdateItemPriceAsyncWithNonExistentItemThrowsInventoryItemNotFoundDomainException()
    {
        // Arrange
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        var productId = 999;

        var inventoryDto = new UpdateInventoryItemPriceDto(productId, 60, 120, _defaultCurrency.ToString());

        _inventoryManager
            .Setup(r => r.GetCurrentInventoryAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(inventory);

        // Act
        Func<Task> act = async () => await _service.UpdateItemPriceAsync(inventoryDto);

        // Assert
        await act.Should().ThrowAsync<InventoryItemNotFoundDomainException>()
            .Where(e => e.InventoryId == inventory.Id && e.ProductId == productId);
    }

    [Fact]
    public async Task AddItemQuantityAsyncWithValidParametersAddsQuantityToItem()
    {
        // Arrange
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        var productId = 1;
        var initialQuantity = 10;
        var additionalQuantity = 5;

        inventory.AddItem(productId, initialQuantity, new Money(50m, _defaultCurrency),
            new Money(100m, _defaultCurrency), 0);

        _inventoryManager
            .Setup(r => r.GetCurrentInventoryAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(inventory);

        // Act
        await _service.AddItemQuantityAsync(productId, additionalQuantity);

        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Quantity.Should().Be(initialQuantity + additionalQuantity);

        _inventoryRepositoryMock.Verify(
            r => r.UpdateAsync(inventory, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task WasteItemAsyncWithValidParametersWastesItemAndRecordsReason()
    {
        // Arrange
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        var productId = 1;
        var initialQuantity = 10;
        var wasteQuantity = 3;
        var wasteReason = "Expired";

        var wasteDto = new WasteInventoryItemDto(productId, wasteQuantity, wasteReason);

        inventory.AddItem(productId, initialQuantity, new Money(50m, _defaultCurrency),
            new Money(100m, _defaultCurrency), 0);

        _inventoryManager
            .Setup(r => r.GetCurrentInventoryAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(inventory);

        // Act
        await _service.WasteItemAsync(wasteDto);

        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Quantity.Should().Be(initialQuantity - wasteQuantity);
        item.TotalWastedQuantity.Should().Be(wasteQuantity);
        item.WasteRecords.Should().HaveCount(1);
        item.WasteRecords.First().Quantity.Should().Be(wasteQuantity);
        item.WasteRecords.First().Reason.Should().Be(wasteReason);

        _inventoryRepositoryMock.Verify(
            r => r.UpdateAsync(inventory, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task DeactivateItemAsyncWithValidParametersDeactivatesItem()
    {
        // Arrange
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        var productId = 1;

        inventory.AddItem(productId, 10, new Money(50m, _defaultCurrency), new Money(100m, _defaultCurrency), 0);

        _inventoryManager
            .Setup(r => r.GetCurrentInventoryAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(inventory);

        // Act
        await _service.DeActivateItemAsync(productId);

        // Assert
        var item = inventory.Items.First(i => i.ProductId == productId);
        item.Status.Should().Be(InventoryItemStatus.Inactive);

        _inventoryRepositoryMock.Verify(
            r => r.UpdateAsync(inventory, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }
}