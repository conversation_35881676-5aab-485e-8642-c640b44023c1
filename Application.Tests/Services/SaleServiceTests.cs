using Application.DTOs.SaleDTOs;
using Application.Interfaces;
using Application.Services;
using Domain.Entities.InventoryAggregate;
using Domain.Entities.ProductAggregate;
using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Domain.Interfaces;
using Domain.Managers;
using Domain.ValueObjects;

namespace Application.Tests.Services;

public class SaleServiceTests
{
    private readonly Mock<ISaleRepository> _saleRepositoryMock;
    private readonly Mock<IInventoryManager> _inventoryManager;
    private readonly Mock<IProductRepository> _productRepositoryMock;
    private readonly Mock<ISaleManager> _completeSaleServiceMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly SaleService _service;
    private readonly CurrencyCode _defaultCurrency = CurrencyCode.USD;

    public SaleServiceTests()
    {
        _saleRepositoryMock = new Mock<ISaleRepository>();
        _inventoryManager = new Mock<IInventoryManager>();
        _productRepositoryMock = new Mock<IProductRepository>();
        _completeSaleServiceMock = new Mock<ISaleManager>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();

        _service = new SaleService(
            _saleRepositoryMock.Object,
            _productRepositoryMock.Object,
            _completeSaleServiceMock.Object,
            _inventoryManager.Object,
            _unitOfWorkMock.Object);
    }

    [Fact]
    public async Task CreateSaleAsyncWithValidParametersCreatesAndSavesSale()
    {
        // Arrange
        var inventory = CreateInventory();
        var saleItemDtos = new List<SaleItemDto>
        {
            new SaleItemDto(ProductId: 1, Quantity: 2),
            new SaleItemDto(ProductId: 2, Quantity: 1)
        };

        var saleDto = new CreateSaleDto(Items: saleItemDtos);

        _inventoryManager
            .Setup(r => r.GetCurrentInventoryAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(inventory);

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Func<int, CancellationToken, Product>((id, _) =>
            {
                var product = new Product("Test Product", "Description", 1)
                {
                    Id = id
                };
                return product;
            }));

        // Act
        await _service.CreateSaleAsync(saleDto);

        // Assert
        _saleRepositoryMock.Verify(
            r => r.AddAsync(It.Is<Sale>(s =>
                    s.Items.Count == 2),
                It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);

        // Verify inventory items were reserved
        var item1 = inventory.Items.First(i => i.ProductId == 1);
        item1.ReservedQuantity.Should().Be(2);

        var item2 = inventory.Items.First(i => i.ProductId == 2);
        item2.ReservedQuantity.Should().Be(1);
    }

    [Fact]
    public async Task CreateSaleAsyncWithDiscountCreatesAndSavesSaleWithDiscount()
    {
        // Arrange
        var inventory = CreateInventory();
        var saleItemDtos = new List<SaleItemDto>
        {
            new(1, 1)
        };
        var discount = new Money(10m, _defaultCurrency);

        var saleDto = new CreateSaleDto(Items: saleItemDtos, DiscountAmount: discount.Amount,
            Currency: discount.CurrencyCode.ToString());

        _inventoryManager
            .Setup(r => r.GetCurrentInventoryAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(inventory);

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Func<int, CancellationToken, Product>((id, _) =>
            {
                var product = new Product("Test Product", "Description", 1)
                {
                    Id = id
                };
                return product;
            }));
        _saleRepositoryMock
            .Setup(r => r.AddAsync(It.IsAny<Sale>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Guid.NewGuid);
        // Act
        await _service.CreateSaleAsync(saleDto);

        // Assert

        _saleRepositoryMock.Verify(
            r => r.AddAsync(It.Is<Sale>(s =>
                    s.DiscountAmount == discount),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetSaleByIdAsyncWithValidIdReturnsSaleDto()
    {
        // Arrange
        var saleId = Guid.NewGuid();
        var sale = new Sale(new List<SaleItem>
        {
            new SaleItem(1, "Product 1", new Money(100m, _defaultCurrency), 2)
        });

        // Use reflection to set the ID since it's generated in the constructor
        var idProperty = typeof(Sale).GetProperty("Id");
        idProperty?.SetValue(sale, saleId);

        _saleRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(saleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(sale);

        // Act
        var result = await _service.GetSaleByIdAsync(saleId);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(saleId);
        result.Items.Should().HaveCount(1);
    }

    [Fact]
    public async Task GetAllSalesAsyncReturnsAllSales()
    {
        // Arrange
        var sales = new List<Sale>
        {
            new Sale(new List<SaleItem> { new SaleItem(1, "Product 1", new Money(100m, _defaultCurrency), 1) }),
            new Sale(new List<SaleItem> { new SaleItem(2, "Product 2", new Money(50m, _defaultCurrency), 2) })
        };

        _saleRepositoryMock
            .Setup(r => r.GetAllAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(sales);

        // Act
        var result = await _service.GetAllSalesAsync();

        // Assert
        result.Should().HaveCount(2);
    }

    [Fact]
    public async Task CompleteSaleAsyncWithValidIdCompletesSale()
    {
        // Arrange
        var saleId = Guid.NewGuid();
        var sale = new Sale(new List<SaleItem>
        {
            new SaleItem(1, "Product 1", new Money(100m, _defaultCurrency), 2)
        });

        var completeSaleDto = new CompleteSaleDto(saleId, _defaultCurrency.ToString());

        // Use reflection to set the ID since it's generated in the constructor
        var idProperty = typeof(Sale).GetProperty("Id");
        idProperty?.SetValue(sale, saleId);

        var inventory = CreateInventory(true);

        _saleRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(saleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(sale);

        _inventoryManager
            .Setup(r => r.GetCurrentInventoryAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(inventory);

        // Act
        await _service.CompleteSaleAsync(completeSaleDto);

        // Assert
        _completeSaleServiceMock.Verify(
            s => s.CompleteSaleAsync(sale, _defaultCurrency.ToString(), It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task CancelSaleAsyncWithValidIdCancelsSale()
    {
        // Arrange
        var saleId = Guid.NewGuid();
        var sale = new Sale(new List<SaleItem>
        {
            new SaleItem(1, "Product 1", new Money(100m, _defaultCurrency), 2)
        });

        // Use reflection to set the ID since it's generated in the constructor
        var idProperty = typeof(Sale).GetProperty("Id");
        idProperty?.SetValue(sale, saleId);

        var inventory = CreateInventory(true);
        var reason = "Customer cancelled";

        var cancelSaleDto = new CancelSaleDto(saleId, reason);

        _saleRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(saleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(sale);

        _inventoryManager
            .Setup(r => r.GetCurrentInventoryAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(inventory);

        // Act
        await _service.CancelSaleAsync(cancelSaleDto);

        // Assert
        // Verify sale was cancelled
        sale.Status.Should().Be(Domain.Enums.SaleStatus.Cancelled);
        sale.CancellationReason.Should().Be(reason);

        // Verify inventory items were released
        var item = inventory.Items.First(i => i.ProductId == 1);
        item.ReservedQuantity.Should().Be(0);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetSalesByDateRangeAsyncReturnsFilteredSales()
    {
        // Arrange
        var startDate = new DateTime(2023, 1, 1);
        var endDate = new DateTime(2023, 1, 31);
        var sales = new List<Sale>
        {
            new Sale(new List<SaleItem> { new SaleItem(1, "Product 1", new Money(100m, _defaultCurrency), 1) }),
            new Sale(new List<SaleItem> { new SaleItem(2, "Product 2", new Money(50m, _defaultCurrency), 2) })
        };

        var dto = new DateRangeDto(startDate, endDate);

        _saleRepositoryMock
            .Setup(r => r.GetSalesByDateRangeAsync(startDate, endDate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(sales);

        // Act
        var result = await _service.GetSalesByDateRangeAsync(dto);

        // Assert
        result.Should().HaveCount(2);
    }

    [Fact]
    public async Task GetSalesByStatusAsyncReturnsFilteredSales()
    {
        // Arrange
        var status = Domain.Enums.SaleStatus.Completed;
        var sales = new List<Sale>
        {
            new Sale(new List<SaleItem> { new SaleItem(1, "Product 1", new Money(100m, _defaultCurrency), 1) })
        };

        _saleRepositoryMock
            .Setup(r => r.GetSalesByStatusAsync(status, It.IsAny<CancellationToken>()))
            .ReturnsAsync(sales);

        // Act
        var result = await _service.GetSalesByStatusAsync(status);

        // Assert
        result.Should().HaveCount(1);
    }

    [Fact]
    public async Task GetSalesByProductIdAsyncReturnsFilteredSales()
    {
        // Arrange
        var productId = 1;
        var sales = new List<Sale>
        {
            new Sale(new List<SaleItem> { new SaleItem(productId, "Product 1", new Money(100m, _defaultCurrency), 1) })
        };

        _saleRepositoryMock
            .Setup(r => r.GetSalesByProductIdAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(sales);

        // Act
        var result = await _service.GetSalesByProductIdAsync(productId);

        // Assert
        result.Should().HaveCount(1);
    }

    private Inventory CreateInventory(bool withReservedItems = false)
    {
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        inventory.AddItem(1, 10, new Money(50m, _defaultCurrency), new Money(100m, _defaultCurrency), 0);
        inventory.AddItem(2, 20, new Money(25m, _defaultCurrency), new Money(50m, _defaultCurrency), 0);

        if (withReservedItems)
        {
            inventory.ReserveItem(1, 2);
            inventory.ReserveItem(2, 1);
        }

        return inventory;
    }
}