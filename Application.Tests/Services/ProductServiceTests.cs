using Application.DTOs.ProductDTOs;
using Application.Interfaces;
using Application.Services;
using Domain.Entities.ProductAggregate;
using Domain.Entities.ProductCategoryAggregate;
using Domain.Interfaces;
using Domain.Services;

namespace Application.Tests.Services;

public class ProductServiceTests
{
    private readonly Mock<IProductRepository> _productRepositoryMock;
    private readonly Mock<IProductCategoryRepository> _productCategoryRepositoryMock;
    private readonly Mock<ITagRepository> _tagRepositoryMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly ProductService _service;
    private readonly ProductCategoryDomainService _productCategoryDomainService;
    private readonly TagDomainService _tagDomainService;

    private readonly ProductCategory _defaultCategory;

    public ProductServiceTests()
    {
        _productRepositoryMock = new Mock<IProductRepository>();
        _productCategoryRepositoryMock = new Mock<IProductCategoryRepository>();
        _tagRepositoryMock = new Mock<ITagRepository>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();

        _service = new ProductService(
            _productRepositoryMock.Object,
            _tagRepositoryMock.Object,
            _productCategoryRepositoryMock.Object,
            _unitOfWorkMock.Object);

        _productCategoryDomainService = new ProductCategoryDomainService(
            _productCategoryRepositoryMock.Object
        );

        _tagDomainService = new TagDomainService(
            _tagRepositoryMock.Object
        );
        _productCategoryRepositoryMock
            .Setup(r => r.IsCategoryNameUniqueAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var cateTask = _productCategoryDomainService.CreateAsync("cate1");
        cateTask.Wait();

        _defaultCategory = cateTask.Result;
    }

    [Fact]
    public async Task GetAllProductsAsyncReturnsAllProductDtos()
    {
        // Arrange
        _productCategoryRepositoryMock
            .Setup(r => r.IsCategoryNameUniqueAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var category1 = await _productCategoryDomainService.CreateAsync("cate1");
        var category2 = await _productCategoryDomainService.CreateAsync("cate2");

        var products = new List<Product>
        {
            new Product("Product 1", "Description 1", 1),
            new Product("Product 2", "Description 2", 2)
        };

        _productRepositoryMock
            .Setup(r => r.GetAllAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(products);

        _productCategoryRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(
                new Func<int, CancellationToken, ProductCategory>((id, _) => id == 1 ? category1 : category2));

        _tagRepositoryMock
            .Setup(r => r.GetTagsByProductIdAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        var result = await _service.GetAllProductsAsync();

        // Assert
        result.Should().HaveCount(2);
        result[0].CategoryName.Should().Be(category1.Name);
        result[1].CategoryName.Should().Be(category2.Name);
    }

    [Fact]
    public async Task GetProductByIdAsyncWithValidIdReturnsProductDto()
    {
        // Arrange
        var productId = 1;
        var product = new Product("Product 1", "Description 1", 1);

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);

        _productCategoryRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_defaultCategory);

        _tagRepositoryMock
            .Setup(r => r.GetTagsByProductIdAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        var result = await _service.GetProductByIdAsync(productId);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(product.Id);
        result.Name.Should().Be(product.Name);
        result.Description.Should().Be(product.Description);
        result.CategoryName.Should().Be(_defaultCategory.Name);
    }

    [Fact]
    public async Task CreateProductAsyncWithValidParametersCreatesAndSavesProduct()
    {
        // Arrange
        _tagRepositoryMock
            .Setup(r => r.IsSlugUniqueAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var tag = await _tagDomainService.CreateTagAsync("New Tag");
        var productDto = new CreateProductDto
        (
            Name: "New Product",
            Description: "New Description",
            CategoryId: 1,
            TagIds: [1, 2]
        );
        var productId = 1;

        _productRepositoryMock
            .Setup(r => r.AddAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(productId);

        _productCategoryRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_defaultCategory);

        _tagRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(tag);
        
        // Act
        await _service.CreateProductAsync(productDto);

        // Assert

        _productRepositoryMock.Verify(
            r => r.AddAsync(It.Is<Product>(p =>
                    p.Name == productDto.Name &&
                    p.Description == productDto.Description &&
                    p.CategoryId == productDto.CategoryId &&
                    p.ProductTags.Count == 2),
                It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task CreateProductAsyncWithoutTagsCreatesProductWithoutTags()
    {
        // Arrange
        var productDto = new CreateProductDto
        (
            Name: "New Product",
            Description: "New Description",
            CategoryId: 1,
            TagIds: []
        );
        var productId = 1;

        _productRepositoryMock
            .Setup(r => r.AddAsync(It.IsAny<Product>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(productId);
        
        _productCategoryRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_defaultCategory);
        // Act
        await _service.CreateProductAsync(productDto);

        // Assert
        _productRepositoryMock.Verify(
            r => r.AddAsync(It.Is<Product>(p =>
                    p.Name == productDto.Name &&
                    p.Description == productDto.Description &&
                    p.CategoryId == productDto.CategoryId &&
                    p.ProductTags.Count == 0),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task CreateProductAsyncWithInvalidParametersThrowsException()
    {
        // Arrange
        _productCategoryRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_defaultCategory);
        
        var productDto = new CreateProductDto
        (
            Name: "", // Invalid name
            Description: "New Description",
            CategoryId: 1
        );

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _service.CreateProductAsync(productDto));
    }

    [Fact]
    public async Task UpdateProductAsyncWithValidParametersUpdatesAndSavesProduct()
    {
        // Arrange
        var productDto = new UpdateProductDto
        (
            Id: 1,
            Name: "Updated Product",
            Description: "Updated Description",
            CategoryId: 2
        );
        var product = new Product("Product 1", "Description 1", 1);

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productDto.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        
        _productCategoryRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_defaultCategory);
        // Act
        await _service.UpdateProductAsync(productDto);

        // Assert
        product.Name.Should().Be(productDto.Name);
        product.Description.Should().Be(productDto.Description);
        product.CategoryId.Should().Be(productDto.CategoryId);

        _productRepositoryMock.Verify(
            r => r.UpdateAsync(product, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task UpdateProductAsyncWithNonExistentProductThrowsException()
    {
        // Arrange
        _productCategoryRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_defaultCategory);
        
        var productDto = new UpdateProductDto
        (
            Id: 999, // Non-existent product ID
            Name: "Updated Product",
            Description: "Updated Description",
            CategoryId: 2
        );

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productDto.Id, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Product not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.UpdateProductAsync(productDto));
    }

    [Fact]
    public async Task UpdateProductAsyncWithInvalidParametersThrowsException()
    {
        // Arrange
        var productDto = new UpdateProductDto
        (
            Id: 1,
            Name: "", // Invalid name
            Description: "Updated Description",
            CategoryId: 2
        );
        var product = new Product("Product 1", "Description 1", 1);

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productDto.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);

        _productCategoryRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_defaultCategory);
        
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _service.UpdateProductAsync(productDto));
    }

    [Fact]
    public async Task DeleteProductAsyncWithValidIdDeletesAndSavesProduct()
    {
        // Arrange
        var productId = 1;
        _productRepositoryMock
            .Setup(r => r.DeleteAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.DeleteProductAsync(productId);

        // Assert
        result.Should().BeTrue();

        _productRepositoryMock.Verify(
            r => r.DeleteAsync(productId, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task DeleteProductAsyncWithNonExistentProductReturnsFalse()
    {
        // Arrange
        var productId = 999; // Non-existent product ID

        _productRepositoryMock
            .Setup(r => r.DeleteAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _service.DeleteProductAsync(productId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task DeleteProductAsyncWithFailedDeletionReturnsFalse()
    {
        // Arrange
        var productId = 1;

        _productRepositoryMock
            .Setup(r => r.DeleteAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _service.DeleteProductAsync(productId);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task AddTagToProductAsyncWithValidParametersAddsTagAndSavesProduct()
    {
        // Arrange
        _tagRepositoryMock
            .Setup(r => r.IsSlugUniqueAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var tagId = 1;
        var tag = await _tagDomainService.CreateTagAsync("New Tag");
        var productId = 1;
        var product = new Product("Product 1", "Description 1", 1);
        
            
        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);

        _tagRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(tag);

        // Act
        await _service.AddTagToProductAsync(productId, tagId);

        // Assert
        product.ProductTags.Should().Contain(x => x.TagId == tagId);

        _productRepositoryMock.Verify(
            r => r.UpdateAsync(product, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task RemoveTagFromProductAsyncWithValidParametersRemovesTagAndSavesProduct()
    {
        // Arrange
        var productId = 1;
        var tagId = 2;
        var product = new Product("Product 1", "Description 1", 1);
        product.AddTag(tagId);

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);

        // Act
        await _service.RemoveTagFromProductAsync(productId, tagId);

        // Assert
        product.ProductTags.Should().NotContain(x => x.TagId == tagId);

        _productRepositoryMock.Verify(
            r => r.UpdateAsync(product, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }
    [Fact]
    public async Task PatchProductAsyncWithOnlyNameUpdatesOnlyName()
    {
        // Arrange
        var productId = 1;
        var originalProduct = new Product("Original Product", "Original Description", 1);
        var patchDto = new PatchProductDto(productId, Name: "Updated Product Name");

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(originalProduct);

        // Act
        await _service.PatchProductAsync(patchDto);

        // Assert
        originalProduct.Name.Should().Be("Updated Product Name");
        originalProduct.Description.Should().Be("Original Description");
        originalProduct.CategoryId.Should().Be(1);

        _productRepositoryMock.Verify(
            r => r.UpdateAsync(originalProduct, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task PatchProductAsyncWithOnlyDescriptionUpdatesOnlyDescription()
    {
        // Arrange
        var productId = 1;
        var originalProduct = new Product("Original Product", "Original Description", 1);
        var patchDto = new PatchProductDto(productId, Description: "Updated Description");

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(originalProduct);

        // Act
        await _service.PatchProductAsync(patchDto);

        // Assert
        originalProduct.Name.Should().Be("Original Product");
        originalProduct.Description.Should().Be("Updated Description");
        originalProduct.CategoryId.Should().Be(1);

        _productRepositoryMock.Verify(
            r => r.UpdateAsync(originalProduct, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task PatchProductAsyncWithOnlyCategoryIdUpdatesOnlyCategoryId()
    {
        // Arrange
        var productId = 1;
        var originalProduct = new Product("Original Product", "Original Description", 1);
        var patchDto = new PatchProductDto(productId, CategoryId: 2);

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(originalProduct);

        _productCategoryRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(2, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_defaultCategory);

        // Act
        await _service.PatchProductAsync(patchDto);

        // Assert
        originalProduct.Name.Should().Be("Original Product");
        originalProduct.Description.Should().Be("Original Description");
        originalProduct.CategoryId.Should().Be(2);

        _productRepositoryMock.Verify(
            r => r.UpdateAsync(originalProduct, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task PatchProductAsyncWithMultipleFieldsUpdatesOnlyProvidedFields()
    {
        // Arrange
        var productId = 1;
        var originalProduct = new Product("Original Product", "Original Description", 1);
        var patchDto = new PatchProductDto(productId, Name: "Updated Product", CategoryId: 2);

        _productRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(originalProduct);

        _productCategoryRepositoryMock
            .Setup(r => r.GetByIdOrDefaultAsync(2, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_defaultCategory);

        // Act
        await _service.PatchProductAsync(patchDto);

        // Assert
        originalProduct.Name.Should().Be("Updated Product");
        originalProduct.Description.Should().Be("Original Description");
        originalProduct.CategoryId.Should().Be(2);

        _productRepositoryMock.Verify(
            r => r.UpdateAsync(originalProduct, It.IsAny<CancellationToken>()),
            Times.Once);

        _unitOfWorkMock.Verify(
            u => u.SaveChangesAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
