using System.Diagnostics.CodeAnalysis;

namespace SharedKernel;

public static class ExtensionMethods
{
    public static Type GetTypeFromName(this string? typeName)
    {
        if (typeName is null || Type.GetType(typeName) is not { } type)
        {
            throw new Exception($"Type {typeName} not found");
        }
        
        return type;
    }
    
    public static string GetTypeName(this object? @object)
    {
        return @object?.GetType().AssemblyQualifiedName ?? "null";
    }
    
    
    [return: NotNull]
    public static T RequireFound<T>(this T? entity, Func<Exception>? exceptionFactory = null)
    {
        exceptionFactory ??= () => new Exception($"{typeof(T).Name} was not found."); 
        
        if (entity is null)
        { 
            throw exceptionFactory();
        }
        return entity;
    }
    
    [return: NotNull]
    public static async Task<T> RequireFound<T>(this Task<T?> entityTask, Func<Exception>? exceptionFactory = null)
    {
        exceptionFactory ??= () => new Exception($"{typeof(T).Name} was not found."); 
        
        var entity = await entityTask.ConfigureAwait(false);

        if (entity is null)
        { 
            throw exceptionFactory();
        }
        return entity;
    }
}