namespace SharedKernel;

/// <summary>
/// Represents the result of an operation that can either succeed or fail.
/// Contains information about whether the operation succeeded and any errors that occurred.
/// </summary>
public sealed class Result
{
    /// <summary>
    /// Initializes a new instance of the Result class
    /// </summary>
    /// <param name="isSuccess">Whether the operation succeeded</param>
    /// <param name="errors">The errors that occurred if the operation failed</param>
    private Result(bool isSuccess, params Error[] errors)
    {
        if (isSuccess && errors.Length != 0 || !isSuccess && errors.Length == 0)
        {
            throw new ArgumentException("Invalid error", nameof(errors));
        }

        IsSuccess = isSuccess;
        Errors = [.. errors];
    }

    /// <summary>
    /// Initializes a new instance of the Result class
    /// </summary>
    /// <param name="isSuccess">Whether the operation succeeded</param>
    /// <param name="errors">The errors that occurred if the operation failed</param>
    private Result(bool isSuccess, List<Error> errors)
    {
        if (isSuccess && errors.Count != 0 || !isSuccess && errors.Count == 0)
        {
            throw new ArgumentException("Invalid error", nameof(errors));
        }

        IsSuccess = isSuccess;
        Errors = [.. errors];
    }

    /// <summary>
    /// Gets a value indicating whether the operation succeeded
    /// </summary>
    public bool IsSuccess { get; }
    
    /// <summary>
    /// Gets a value indicating whether the operation failed
    /// </summary>
    public bool IsFailure => !IsSuccess;
    
    /// <summary>
    /// Gets the errors that occurred if the operation failed
    /// </summary>
    public List<Error> Errors { get; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <returns>A successful result</returns>
    public static Result Success() => new Result(true);

    /// <summary>
    /// Creates a failed result with the specified errors
    /// </summary>
    /// <param name="errors">The errors that occurred</param>
    /// <returns>A failed result</returns>
    public static Result Failure(params Error[] errors) => new Result(false, errors);
    
    /// <summary>
    /// Creates a failed result with the specified errors
    /// </summary>
    /// <param name="errors">The errors that occurred</param>
    /// <returns>A failed result</returns>
    public static Result Failure(List<Error> errors) => new Result(false, errors);

    /// <summary>
    /// Implicitly converts an error to a failed result
    /// </summary>
    /// <param name="error">The error that occurred</param>
    public static implicit operator Result(Error error) => Failure(error);
    
    /// <summary>
    /// Implicitly converts an array of errors to a failed result
    /// </summary>
    /// <param name="errors">The errors that occurred</param>
    public static implicit operator Result(Error[] errors) => Failure(errors);
    
    /// <summary>
    /// Implicitly converts a list of errors to a failed result
    /// </summary>
    /// <param name="errors">The errors that occurred</param>
    public static implicit operator Result(List<Error> errors) => Failure(errors);

    /// <summary>
    /// Maps a successful result to a new result with a value
    /// </summary>
    /// <typeparam name="T">The type of the value</typeparam>
    /// <param name="func">The function to map the result</param>
    /// <returns>A new result with the mapped value</returns>
    public Result<T> Map<T>(Func<T> func)
    {
        if (IsFailure)
        {
            return Result<T>.Failure(Errors);
        }

        try
        {
            return Result<T>.Success(func());
        }
        catch (Exception ex)
        {
            return Result<T>.Failure(new Error("Exception", ex.Message));
        }
    }

    /// <summary>
    /// Combines multiple results into a single result
    /// </summary>
    /// <param name="results">The results to combine</param>
    /// <returns>A successful result if all results are successful; otherwise, a failed result with all errors</returns>
    public static Result Combine(params Result[] results)
    {
        var failedResults = results.Where(r => r.IsFailure).ToArray();

        if (failedResults.Length == 0)
        {
            return Success();
        }

        var errors = failedResults
            .SelectMany(r => r.Errors)
            .ToArray();

        return Failure(errors);
    }
}

/// <summary>
/// Represents the result of an operation that can either succeed with a value or fail.
/// Contains information about whether the operation succeeded, the value if it succeeded, and any errors that occurred if it failed.
/// </summary>
/// <typeparam name="T">The type of the value</typeparam>
public sealed class Result<T>
{
    /// <summary>
    /// Initializes a new instance of the Result class
    /// </summary>
    /// <param name="isSuccess">Whether the operation succeeded</param>
    /// <param name="value">The value if the operation succeeded</param>
    /// <param name="errors">The errors that occurred if the operation failed</param>
    private Result(bool isSuccess, T? value = default, params Error[] errors)
    {
        if (isSuccess && errors.Length != 0 || !isSuccess && errors.Length == 0)
        {
            throw new ArgumentException("Invalid error", nameof(errors));
        }

        IsSuccess = isSuccess;
        Errors = [.. errors];
        Value = value;
    }

    /// <summary>
    /// Initializes a new instance of the Result class
    /// </summary>
    /// <param name="isSuccess">Whether the operation succeeded</param>
    /// <param name="errors">The errors that occurred if the operation failed</param>
    /// <param name="value">The value if the operation succeeded</param>
    private Result(bool isSuccess, List<Error> errors, T? value = default)
    {
        if (isSuccess && errors.Count != 0 || !isSuccess && errors.Count == 0)
        {
            throw new ArgumentException("Invalid error", nameof(errors));
        }

        IsSuccess = isSuccess;
        Errors = [.. errors];
        Value = value;
    }

    /// <summary>
    /// Gets a value indicating whether the operation succeeded
    /// </summary>
    public bool IsSuccess { get; }
    
    /// <summary>
    /// Gets a value indicating whether the operation failed
    /// </summary>
    public bool IsFailure => !IsSuccess;
    
    /// <summary>
    /// Gets the value if the operation succeeded
    /// </summary>
    public T? Value { get; }
    
    /// <summary>
    /// Gets the errors that occurred if the operation failed
    /// </summary>
    public List<Error> Errors { get; }

    /// <summary>
    /// Creates a successful result with the specified value
    /// </summary>
    /// <param name="value">The value</param>
    /// <returns>A successful result</returns>
    public static Result<T> Success(T? value) => new Result<T>(true, value);

    /// <summary>
    /// Creates a failed result with the specified errors
    /// </summary>
    /// <param name="errors">The errors that occurred</param>
    /// <returns>A failed result</returns>
    public static Result<T> Failure(params Error[] errors) => new Result<T>(false, value: default, errors);
    
    /// <summary>
    /// Creates a failed result with the specified errors
    /// </summary>
    /// <param name="errors">The errors that occurred</param>
    /// <returns>A failed result</returns>
    public static Result<T> Failure(List<Error> errors) => new Result<T>(false, errors);

    /// <summary>
    /// Implicitly converts a value to a successful result
    /// </summary>
    /// <param name="value">The value</param>
    public static implicit operator Result<T>(T? value) => Success(value);
    
    /// <summary>
    /// Implicitly converts an error to a failed result
    /// </summary>
    /// <param name="error">The error that occurred</param>
    public static implicit operator Result<T>(Error error) => Failure(error);
    
    /// <summary>
    /// Implicitly converts an array of errors to a failed result
    /// </summary>
    /// <param name="errors">The errors that occurred</param>
    public static implicit operator Result<T>(Error[] errors) => Failure(errors);
    
    /// <summary>
    /// Implicitly converts a list of errors to a failed result
    /// </summary>
    /// <param name="errors">The errors that occurred</param>
    public static implicit operator Result<T>(List<Error> errors) => Failure(errors);

    /// <summary>
    /// Maps a successful result to a new result with a different value
    /// </summary>
    /// <typeparam name="TResult">The type of the new value</typeparam>
    /// <param name="func">The function to map the value</param>
    /// <returns>A new result with the mapped value</returns>
    public Result<TResult> Map<TResult>(Func<T, TResult> func)
    {
        if (IsFailure)
        {
            return Result<TResult>.Failure(Errors);
        }

        try
        {
            return Result<TResult>.Success(func(Value!));
        }
        catch (Exception ex)
        {
            return Result<TResult>.Failure(new Error("Exception", ex.Message));
        }
    }

    /// <summary>
    /// Binds a successful result to a new result
    /// </summary>
    /// <typeparam name="TResult">The type of the new value</typeparam>
    /// <param name="func">The function to bind the value</param>
    /// <returns>A new result</returns>
    public Result<TResult> Bind<TResult>(Func<T, Result<TResult>> func)
    {
        if (IsFailure)
        {
            return Result<TResult>.Failure(Errors);
        }

        try
        {
            return func(Value!);
        }
        catch (Exception ex)
        {
            return Result<TResult>.Failure(new Error("Exception", ex.Message));
        }
    }

    /// <summary>
    /// Ensures that a predicate is satisfied
    /// </summary>
    /// <param name="predicate">The predicate to check</param>
    /// <param name="error">The error to return if the predicate is not satisfied</param>
    /// <returns>The current result if the predicate is satisfied; otherwise, a failed result</returns>
    public Result<T> Ensure(Func<T, bool> predicate, Error error)
    {
        if (IsFailure)
        {
            return this;
        }

        if (predicate(Value!))
        {
            return this;
        }
        else
        {
            return Failure(error);
        }
    }

    /// <summary>
    /// Gets the value if the operation succeeded; otherwise, returns the default value
    /// </summary>
    /// <param name="defaultValue">The default value to return if the operation failed</param>
    /// <returns>The value if the operation succeeded; otherwise, the default value</returns>
    public T GetValueOrDefault(T defaultValue)
    {
        if (IsSuccess)
        {
            return Value!;
        }
        else
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// Gets the value if the operation succeeded; otherwise, throws an exception
    /// </summary>
    /// <returns>The value if the operation succeeded</returns>
    /// <exception cref="InvalidOperationException">Thrown if the operation failed</exception>
    public T GetValueOrThrow()
    {
        if (IsFailure)
        {
            throw new InvalidOperationException($"Cannot access value of failed result. Errors: {string.Join(", ", Errors.Select(e => e.Description))}");
        }

        return Value!;
    }

    /// <summary>
    /// Tries to get the value if the operation succeeded
    /// </summary>
    /// <param name="value">The value if the operation succeeded; otherwise, the default value</param>
    /// <returns>true if the operation succeeded; otherwise, false</returns>
    public bool TryGetValue(out T? value)
    {
        value = IsSuccess ? Value : default;
        return IsSuccess;
    }

    /// <summary>
    /// Tries to execute a function and returns a result
    /// </summary>
    /// <param name="func">The function to execute</param>
    /// <param name="errorHandler">The function to handle exceptions</param>
    /// <returns>A successful result if the function succeeds; otherwise, a failed result</returns>
    public static Result<T> Try(Func<T> func, Func<Exception, Error>? errorHandler = null)
    {
        try
        {
            return Success(func());
        }
        catch (Exception ex)
        {
            var error = errorHandler != null
                ? errorHandler(ex)
                : new Error("Exception", ex.Message);

            return Failure(error);
        }
    }
}
