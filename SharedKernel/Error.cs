namespace SharedKernel;

/// <summary>
/// Represents an error that occurred during an operation.
/// Immutable record type with code and description.
/// </summary>
/// <param name="Code">The error code</param>
/// <param name="Description">The error description</param>
public sealed record Error(string Code, string Description = "")
{
    /// <summary>
    /// Represents the absence of an error
    /// </summary>
    public static readonly Error None = new(string.Empty, string.Empty);
}