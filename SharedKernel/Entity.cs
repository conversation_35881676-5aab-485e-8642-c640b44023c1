namespace SharedKernel;

/// <summary>
/// Base class for all entities with integer ID in the domain model.
/// </summary>
public abstract class Entity : Entity<int>
{
}

/// <summary>
/// Base class for all entities in the domain model with generic ID type.
/// Provides identity, equality, and comparison functionality.
/// </summary>
/// <typeparam name="TId">The type of the entity's identifier</typeparam>
public abstract class Entity<TId> : IEntity, IEquatable<Entity<TId>> where TId : struct, IEquatable<TId>
{
    private readonly List<DomainEvent> _domainEvents = [];
    
    public IReadOnlyCollection<DomainEvent> DomainEvents => _domainEvents.AsReadOnly();
    
    public void AddDomainEvent(DomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }
    
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
    
    protected Entity()
    {
        
    }
    protected Entity(TId id)
    {
        Id = id;
    }
    
    public TId Id { get; set; }
    public bool Equals(Entity<TId>? other)
    {
        if (other is null)
        {
            return false;
        }
        return Id.Equals(other.Id);
    }
    
    public override bool Equals(object? obj)
    {
        if (obj is Entity<TId> other)
        {
            return Equals(other);
        }
        return false;
    }
    
    public override int GetHashCode() => Id.GetHashCode();
    public static bool operator ==(Entity<TId> a, Entity<TId> b) => a.Equals(b);
    public static bool operator !=(Entity<TId> a, Entity<TId> b) => !(a.Equals(b));
}

