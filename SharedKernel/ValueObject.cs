namespace SharedKernel;

/// <summary>
/// Base class for all value objects in the domain model.
/// Value objects are immutable and are compared by their values, not by identity.
/// </summary>
public abstract class ValueObject : IEquatable<ValueObject>
{
    /// <summary>
    /// Gets the atomic values that make up this value object.
    /// These values will be used for equality comparison and hash code generation.
    /// </summary>
    /// <returns>An enumerable of atomic values that make up this value object</returns>
    protected abstract IEnumerable<object> GetAtomicValues();

    /// <summary>
    /// Determines whether the specified value object is equal to the current value object
    /// </summary>
    /// <param name="other">The value object to compare with the current value object</param>
    /// <returns>true if the specified value object is equal to the current value object; otherwise, false</returns>
    public bool Equals(ValueObject? other)
    {
        if (other is null)
        {
            return false;
        }
        return AreValuesEqual(other);
    }

    /// <summary>
    /// Returns the hash code for this value object
    /// </summary>
    /// <returns>A hash code for the current value object</returns>
    public override int GetHashCode()
    {
        return GetAtomicValues().Aggregate(default(int), HashCode.Combine);
    }

    /// <summary>
    /// Determines whether the specified object is equal to the current value object
    /// </summary>
    /// <param name="obj">The object to compare with the current value object</param>
    /// <returns>true if the specified object is equal to the current value object; otherwise, false</returns>
    public override bool Equals(object? obj)
    {
        if (obj is ValueObject other)
        {
            return AreValuesEqual(other);
        }
        return false;
    }

    /// <summary>
    /// Determines whether the values of the specified value object are equal to the values of the current value object
    /// </summary>
    /// <param name="other">The value object to compare with the current value object</param>
    /// <returns>true if the values are equal; otherwise, false</returns>
    private bool AreValuesEqual(ValueObject other)
    {
        return GetAtomicValues().SequenceEqual(other.GetAtomicValues());
    }


    public static bool operator ==(ValueObject? left, ValueObject? right)
    {
        if (left is null || right is null)
        {
            return ReferenceEquals(left, right);
        }
        
        return left.Equals(right);
    }

    public static bool operator !=(ValueObject? left, ValueObject? right)
    {
        return !(left == right);
    }
}