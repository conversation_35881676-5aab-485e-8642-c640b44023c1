using Domain.Entities;
using Domain.Entities.InventoryAggregate;
using Domain.Enums;
using Domain.Events;
using Domain.ValueObjects;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SharedKernel;

namespace Infrastructure.Tests.Data;

public class UnitOfWorkTests : IDisposable
{
    private readonly ApplicationDbContext _dbContext;
    private readonly UnitOfWork _unitOfWork;
    private readonly CurrencyCode _defaultCurrency = CurrencyCode.USD;
    
    public UnitOfWorkTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: $"UnitOfWorkTests_{Guid.NewGuid()}")
            .Options;
        
        _dbContext = new ApplicationDbContext(options);
        _unitOfWork = new UnitOfWork(_dbContext);
    }
    
    public void Dispose()
    {
        _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }
    
    [Fact]
    public async Task SaveChangesAsyncWithDomainEventsCreatesOutboxMessages()
    {
        // Arrange
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        inventory.AddItem(1, 10, new Money(50m, _defaultCurrency), new Money(100m, _defaultCurrency), 0);
        
        // Add a domain event
        inventory.WasteItem(1, 3, "Expired");
        
        await _dbContext.Inventories.AddAsync(inventory);
        
        // Act
        await _unitOfWork.SaveChangesAsync();
        
        // Assert
        var outboxMessages = await _dbContext.OutboxMessages.ToListAsync();
        outboxMessages.Should().HaveCount(1);
        
        var message = outboxMessages.First();
        message.Type.Should().Contain(nameof(InventoryItemWastedDomainEvent));
        
        // Verify domain events were cleared
        inventory.DomainEvents.Should().BeEmpty();
    }
    
    [Fact]
    public async Task SaveChangesAsyncWithMultipleDomainEventsCreatesMultipleOutboxMessages()
    {
        // Arrange
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        inventory.AddItem(1, 10, new Money(50m, _defaultCurrency), new Money(100m, _defaultCurrency), 0);
        
        // Add multiple domain events
        inventory.WasteItem(1, 3, "Expired");
        inventory.UpdateItemPrice(1, new Money(60m, _defaultCurrency), new Money(120m, _defaultCurrency));
        
        await _dbContext.Inventories.AddAsync(inventory);
        
        // Act
        await _unitOfWork.SaveChangesAsync();
        
        // Assert
        var outboxMessages = await _dbContext.OutboxMessages.ToListAsync();
        outboxMessages.Should().HaveCount(2);
        
        outboxMessages.Should().Contain(m => m.Type.Contains(nameof(InventoryItemWastedDomainEvent)));
        outboxMessages.Should().Contain(m => m.Type.Contains(nameof(InventoryItemPriceUpdatedDomainEvent)));
        
        // Verify domain events were cleared
        inventory.DomainEvents.Should().BeEmpty();
    }
    
    [Fact]
    public async Task SaveChangesAsyncWithoutDomainEventsDoesNotCreateOutboxMessages()
    {
        // Arrange
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        inventory.AddItem(1, 10, new Money(50m, _defaultCurrency), new Money(100m, _defaultCurrency), 0);
        
        // No domain events added
        
        await _dbContext.Inventories.AddAsync(inventory);
        
        // Act
        await _unitOfWork.SaveChangesAsync();
        
        // Assert
        var outboxMessages = await _dbContext.OutboxMessages.ToListAsync();
        outboxMessages.Should().BeEmpty();
    }
    
    [Fact]
    public async Task SaveChangesAsyncWithExistingOutboxMessagesOnlyAddsNewMessages()
    {
        // Arrange
        // Add an existing outbox message
        var existingMessage = new OutboxMessage("ExistingType", "ExistingContent");
        await _dbContext.OutboxMessages.AddAsync(existingMessage);
        await _dbContext.SaveChangesAsync();
        
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        inventory.AddItem(1, 10, new Money(50m, _defaultCurrency), new Money(100m, _defaultCurrency), 0);
        
        // Add a domain event
        inventory.WasteItem(1, 3, "Expired");
        
        await _dbContext.Inventories.AddAsync(inventory);
        
        // Act
        await _unitOfWork.SaveChangesAsync();
        
        // Assert
        var outboxMessages = await _dbContext.OutboxMessages.ToListAsync();
        outboxMessages.Should().HaveCount(2); // Existing + new
        
        outboxMessages.Should().Contain(m => m.Type == "ExistingType");
        outboxMessages.Should().Contain(m => m.Type.Contains(nameof(InventoryItemWastedDomainEvent)));
    }
    
    [Fact]
    public async Task SaveChangesAsyncWithMultipleAggregatesProcessesAllDomainEvents()
    {
        // Arrange
        var inventory1 = new Inventory(new Money(1000m, _defaultCurrency));
        inventory1.AddItem(1, 10, new Money(50m, _defaultCurrency), new Money(100m, _defaultCurrency), 0);
        inventory1.WasteItem(1, 3, "Expired");
        
        var inventory2 = new Inventory(new Money(2000m, _defaultCurrency));
        inventory2.AddItem(2, 20, new Money(25m, _defaultCurrency), new Money(50m, _defaultCurrency), 0);
        inventory2.UpdateItemPrice(2, new Money(30m, _defaultCurrency), new Money(60m, _defaultCurrency));
        
        await _dbContext.Inventories.AddRangeAsync(inventory1, inventory2);
        
        // Act
        await _unitOfWork.SaveChangesAsync();
        
        // Assert
        var outboxMessages = await _dbContext.OutboxMessages.ToListAsync();
        outboxMessages.Should().HaveCount(2);
        
        outboxMessages.Should().Contain(m => m.Type.Contains(nameof(InventoryItemWastedDomainEvent)));
        outboxMessages.Should().Contain(m => m.Type.Contains(nameof(InventoryItemPriceUpdatedDomainEvent)));
        
        // Verify domain events were cleared from both aggregates
        inventory1.DomainEvents.Should().BeEmpty();
        inventory2.DomainEvents.Should().BeEmpty();
    }
}
