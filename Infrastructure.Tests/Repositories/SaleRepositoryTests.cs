using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Domain.ValueObjects;
using Infrastructure.Data;
using Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Tests.Repositories;

public class SaleRepositoryTests : IDisposable
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SaleRepository _repository;
    private readonly CurrencyCode _defaultCurrency = CurrencyCode.USD;
    
    public SaleRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: $"SaleRepositoryTests_{Guid.NewGuid()}")
            .Options;
        
        _dbContext = new ApplicationDbContext(options);
        _repository = new SaleRepository(_dbContext);
    }
    
    public void Dispose()
    {
        _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }
    
    [Fact]
    public async Task GetByIdAsyncWithExistingSaleReturnsSale()
    {
        // Arrange
        var sale = CreateSale();
        await _dbContext.Sales.AddAsync(sale);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _repository.GetByIdAsync(sale.Id);
        
        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(sale.Id);
        result.Items.Should().HaveCount(1);
    }
    
    [Fact]
    public async Task GetByIdAsyncWithNonExistingSaleThrowsInvalidOperationException()
    {
        // Arrange
        var nonExistingId = Guid.NewGuid();
        
        // Act
        Func<Task> act = async () => await _repository.GetByIdAsync(nonExistingId);
        
        // Assert
        await act.Should().ThrowAsync<InvalidOperationException>();
    }
    
    [Fact]
    public async Task GetByIdOrDefaultAsyncWithExistingSaleReturnsSale()
    {
        // Arrange
        var sale = CreateSale();
        await _dbContext.Sales.AddAsync(sale);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _repository.GetByIdOrDefaultAsync(sale.Id);
        
        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(sale.Id);
    }
    
    [Fact]
    public async Task GetByIdOrDefaultAsyncWithNonExistingSaleReturnsNull()
    {
        // Arrange
        var nonExistingId = Guid.NewGuid();
        
        // Act
        var result = await _repository.GetByIdOrDefaultAsync(nonExistingId);
        
        // Assert
        result.Should().BeNull();
    }
    
    [Fact]
    public async Task GetAllAsyncReturnsAllSales()
    {
        // Arrange
        var sale1 = CreateSale();
        var sale2 = CreateSale();
        await _dbContext.Sales.AddRangeAsync(sale1, sale2);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _repository.GetAllAsync();
        
        // Assert
        result.Should().HaveCount(2);
    }
    
    [Fact]
    public async Task GetSalesByStatusAsyncReturnsFilteredSales()
    {
        // Arrange
        var pendingSale = CreateSale();
        
        var completedSale = CreateSale();
        // Set status to completed using reflection
        var statusProperty = typeof(Sale).GetProperty("Status");
        statusProperty?.SetValue(completedSale, SaleStatus.Completed);
        
        await _dbContext.Sales.AddRangeAsync(pendingSale, completedSale);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _repository.GetSalesByStatusAsync(SaleStatus.Completed);
        
        // Assert
        result.Should().HaveCount(1);
        result.First().Status.Should().Be(SaleStatus.Completed);
    }
    
    [Fact]
    public async Task GetSalesByDateRangeAsyncReturnsFilteredSales()
    {
        // Arrange
        var sale1 = CreateSale();
        // Set created date using reflection
        var createdAtProperty = typeof(Sale).GetProperty("CreatedAt");
        createdAtProperty?.SetValue(sale1, new DateTime(2023, 1, 15));
        
        var sale2 = CreateSale();
        createdAtProperty?.SetValue(sale2, new DateTime(2023, 2, 15));
        
        await _dbContext.Sales.AddRangeAsync(sale1, sale2);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _repository.GetSalesByDateRangeAsync(
            new DateTime(2023, 1, 1), 
            new DateTime(2023, 1, 31));
        
        // Assert
        result.Should().HaveCount(1);
        result.First().CreatedAt.Should().Be(new DateTime(2023, 1, 15));
    }
    
    [Fact]
    public async Task GetSalesByProductIdAsyncReturnsFilteredSales()
    {
        // Arrange
        var productId1 = 1;
        var productId2 = 2;
        
        var sale1 = new Sale(new List<SaleItem> 
        { 
            new SaleItem(productId1, "Product 1", new Money(100m, _defaultCurrency), 1) 
        });
        
        var sale2 = new Sale(new List<SaleItem> 
        { 
            new SaleItem(productId2, "Product 2", new Money(50m, _defaultCurrency), 1) 
        });
        
        await _dbContext.Sales.AddRangeAsync(sale1, sale2);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _repository.GetSalesByProductIdAsync(productId1);
        
        // Assert
        result.Should().HaveCount(1);
        result.First().Items.Should().ContainSingle(i => i.ProductId == productId1);
    }
    
    [Fact]
    public async Task AddAsyncWithValidSaleAddsSaleToDatabase()
    {
        // Arrange
        var sale = CreateSale();
        
        // Act
        var result = await _repository.AddAsync(sale);
        await _dbContext.SaveChangesAsync();
        
        // Assert
        result.Should().Be(sale.Id);
        
        var savedSale = await _dbContext.Sales
            .Include(s => s.Items)
            .FirstOrDefaultAsync(s => s.Id == sale.Id);
        
        savedSale.Should().NotBeNull();
        savedSale!.Items.Should().HaveCount(1);
    }
    
    [Fact]
    public async Task UpdateAsyncWithValidSaleUpdatesSaleInDatabase()
    {
        // Arrange
        var sale = CreateSale();
        await _dbContext.Sales.AddAsync(sale);
        await _dbContext.SaveChangesAsync();
        
        // Cancel the sale
        sale.CancelSale("Test cancellation");
        
        // Act
        await _repository.UpdateAsync(sale);
        await _dbContext.SaveChangesAsync();
        
        // Assert
        var updatedSale = await _dbContext.Sales.FindAsync(sale.Id);
        updatedSale.Should().NotBeNull();
        updatedSale!.Status.Should().Be(SaleStatus.Cancelled);
        updatedSale.CancellationReason.Should().Be("Test cancellation");
    }
    
    [Fact]
    public void DeleteasyncThrowsnotimplementedexception()
    {
        // Arrange
        var saleId = Guid.NewGuid();
        
        // Act
        Func<Task> act = async () => await _repository.DeleteAsync(saleId);
        
        // Assert
        act.Should().ThrowAsync<NotImplementedException>();
    }
    
    private Sale CreateSale()
    {
        return new Sale(new List<SaleItem> 
        { 
            new SaleItem(1, "Test Product", new Money(100m, _defaultCurrency), 1) 
        });
    }
}
