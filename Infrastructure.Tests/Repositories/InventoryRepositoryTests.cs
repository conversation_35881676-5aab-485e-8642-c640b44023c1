using Domain.Entities.InventoryAggregate;
using Domain.Enums;
using Domain.Services;
using Domain.ValueObjects;
using Infrastructure.Data;
using Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Tests.Repositories;

public class InventoryRepositoryTests : IDisposable
{
    private readonly ApplicationDbContext _dbContext;
    private readonly InventoryRepository _inventoryService;
    private readonly InventoryDomainService inventoryDomainService;
    private readonly CurrencyCode _defaultCurrency = CurrencyCode.USD;
    
    public InventoryRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: $"InventoryRepositoryTests_{Guid.NewGuid()}")
            .Options;
        _dbContext = new ApplicationDbContext(options);
        _inventoryService = new InventoryRepository(_dbContext);
        inventoryDomainService = new InventoryDomainService(_inventoryService);
    }
    
    public void Dispose()
    {
        _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }
    
    [Fact]
    public async Task GetAllAsyncReturnsAllInventories()
    {
        // Arrange
        var inventory1 = CreateInventory();
        var inventory2 = CreateInventory();
        await _dbContext.Inventories.AddRangeAsync(inventory1, inventory2);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _inventoryService.GetAllAsync();
        
        // Assert
        result.Should().HaveCount(2);
    }
    
    [Fact]
    public async Task GetByIdAsyncWithExistingInventoryReturnsInventory()
    {
        // Arrange
        var inventory = CreateInventory();
        await _dbContext.Inventories.AddAsync(inventory);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _inventoryService.GetByIdAsync(inventory.Id);
        
        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(inventory.Id);
        result.Items.Should().HaveCount(1);
    }
    
    [Fact]
    public async Task GetByIdAsyncWithNonExistingInventoryThrowsInvalidOperationException()
    {
        // Arrange
        var nonExistingId = Guid.NewGuid();
        
        // Act
        Func<Task> act = async () => await _inventoryService.GetByIdAsync(nonExistingId);
        
        // Assert
        await act.Should().ThrowAsync<InvalidOperationException>();
    }
    
    [Fact]
    public async Task GetByIdOrDefaultAsyncWithExistingInventoryReturnsInventory()
    {
        // Arrange
        var inventory = CreateInventory();
        await _dbContext.Inventories.AddAsync(inventory);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _inventoryService.GetByIdOrDefaultAsync(inventory.Id);
        
        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(inventory.Id);
    }
    
    [Fact]
    public async Task GetByIdOrDefaultAsyncWithNonExistingInventoryReturnsNull()
    {
        // Arrange
        var nonExistingId = Guid.NewGuid();
        
        // Act
        var result = await _inventoryService.GetByIdOrDefaultAsync(nonExistingId);
        
        // Assert
        result.Should().BeNull();
    }
    
    [Fact]
    public async Task GetCurrentInventoryAsyncReturnsLatestInventory()
    {
        // Arrange
        var olderInventory = CreateInventory();
        // Set created date using reflection
        var createdAtProperty = typeof(Inventory).GetProperty("CreatedAt");
        createdAtProperty?.SetValue(olderInventory, new DateTime(2023, 1, 1));
        
        var newerInventory = CreateInventory();
        createdAtProperty?.SetValue(newerInventory, new DateTime(2023, 2, 1));
        
        await _dbContext.Inventories.AddRangeAsync(olderInventory, newerInventory);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await inventoryDomainService.GetCurrentInventoryAsync();
        
        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(newerInventory.Id);
        result.CreatedAt.Should().Be(new DateTime(2023, 2, 1));
    }
    
    [Fact]
    public async Task GetInventoryItemByProductIdAsyncWithExistingItemReturnsItem()
    {
        // Arrange
        var productId = 1;
        var inventory = CreateInventory(productId);
        await _dbContext.Inventories.AddAsync(inventory);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _inventoryService.GetInventoryItemByProductIdAsync(productId);
        
        // Assert
        result.Should().NotBeNull();
        result!.ProductId.Should().Be(productId);
    }
    
    [Fact]
    public async Task GetInventoryItemByProductIdAsyncWithNonExistingItemReturnsNull()
    {
        // Arrange
        var nonExistingProductId = 999;
        var inventory = CreateInventory(1); // Different product ID
        await _dbContext.Inventories.AddAsync(inventory);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _inventoryService.GetInventoryItemByProductIdAsync(nonExistingProductId);
        
        // Assert
        result.Should().BeNull();
    }
    
    [Fact]
    public async Task GetOutOfStockItemsAsyncReturnsOutOfStockItems()
    {
        // Arrange
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        
        // Add in-stock item
        inventory.AddItem(1, 10, new Money(50m, _defaultCurrency), new Money(100m, _defaultCurrency), 0);
        
        // Add out-of-stock item
        inventory.AddItem(2, 0, new Money(25m, _defaultCurrency), new Money(50m, _defaultCurrency), 0);
        
        await _dbContext.Inventories.AddAsync(inventory);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _inventoryService.GetOutOfStockItemsAsync();
        
        // Assert
        result.Should().HaveCount(1);
        result.First().ProductId.Should().Be(2);
        result.First().Status.Should().Be(InventoryItemStatus.OutOfStock);
    }
    
    [Fact]
    public async Task GetPriceHistoryForProductAsyncReturnsPriceHistory()
    {
        // Arrange
        var productId = 1;
        var inventory = CreateInventory(productId);
        
        // Update price to create history
        inventory.UpdateItemPrice(
            productId, 
            new Money(60m, _defaultCurrency), 
            new Money(120m, _defaultCurrency));
        
        await _dbContext.Inventories.AddAsync(inventory);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _inventoryService.GetPriceHistoryForProductAsync(productId);
        
        // Assert
        result.Should().HaveCount(2); // Initial price + updated price
        result.Last().SellingPrice.Amount.Should().Be(120m);
    }
    
    [Fact]
    public async Task GetWasteRecordsForProductAsyncReturnsWasteRecords()
    {
        // Arrange
        var productId = 1;
        var inventory = CreateInventory(productId);
        
        // Record waste
        inventory.WasteItem(productId, 3, "Expired");
        
        await _dbContext.Inventories.AddAsync(inventory);
        await _dbContext.SaveChangesAsync();
        
        // Act
        var result = await _inventoryService.GetWasteRecordsForProductAsync(productId);
        
        // Assert
        result.Should().HaveCount(1);
        result.First().Quantity.Should().Be(3);
        result.First().Reason.Should().Be("Expired");
    }
    
    [Fact]
    public async Task AddAsyncWithValidInventoryAddsInventoryToDatabase()
    {
        // Arrange
        var inventory = CreateInventory();
        
        // Act
        await _inventoryService.AddAsync(inventory);
        await _dbContext.SaveChangesAsync();
        
        // Assert
        var savedInventory = await _dbContext.Inventories
            .Include(i => i.Items)
            .FirstOrDefaultAsync(i => i.Id == inventory.Id);
        
        savedInventory.Should().NotBeNull();
        savedInventory!.Items.Should().HaveCount(1);
    }
    
    [Fact]
    public async Task UpdateAsyncWithValidInventoryUpdatesInventoryInDatabase()
    {
        // Arrange
        var inventory = CreateInventory();
        await _dbContext.Inventories.AddAsync(inventory);
        await _dbContext.SaveChangesAsync();
        
        // Update inventory
        inventory.AddItemQuantity(1, 5);
        
        // Act
        await _inventoryService.UpdateAsync(inventory);
        await _dbContext.SaveChangesAsync();
        
        // Assert
        var updatedInventory = await _dbContext.Inventories
            .Include(i => i.Items)
            .FirstOrDefaultAsync(i => i.Id == inventory.Id);
        
        updatedInventory.Should().NotBeNull();
        updatedInventory!.Items.First().Quantity.Should().Be(15); // 10 + 5
    }
    
    private Inventory CreateInventory(int productId = 1)
    {
        var inventory = new Inventory(new Money(1000m, _defaultCurrency));
        inventory.AddItem(productId, 10, new Money(50m, _defaultCurrency), new Money(100m, _defaultCurrency), 0);
        return inventory;
    }
}
