using Domain.Enums;
using Domain.ValueObjects;
using Infrastructure.Services;

namespace Infrastructure.Tests.Services;

public class CurrencyConverterTests
{
    [Fact]
    public async Task ConvertAsyncWithSameCurrencyReturnsSameMoneyObject()
    {
        // Arrange
        var converter = new CurrencyConverter();
        var money = new Money(100m, CurrencyCode.USD);

        // Act
        var result = await converter.ConvertAsync(money, CurrencyCode.USD	);

        // Asset
        result.Should().Be(money);
    }

    [Fact]
    public async Task ConvertAsyncWithExchangeRateReturnsConvertedMoney()
    {
        // Arrange
        var exchangeRates = new Dictionary<CurrencyCode, Dictionary<CurrencyCode, decimal>>
        {
            [CurrencyCode.USD] = new Dictionary<CurrencyCode, decimal>
            {
                [CurrencyCode.EUR] = 0.85m
            }
        };

        var converter = new CurrencyConverter(exchangeRates);
        var money = new Money(100m, CurrencyCode.USD	);

        // Act
       var result = await converter.ConvertAsync(money, CurrencyCode.EUR);

        // Assert
        result.Amount.Should().Be(85m);
        result.CurrencyCode.Should().Be(CurrencyCode.EUR);
    }

    [Fact]
    public async Task ConvertAsyncWithMissingExchangeRateThrowsException()
    {
        // Arrange
        var converter = new CurrencyConverter();
        var money = new Money(100m, CurrencyCode.USD	);

        // Act
       Func<Task> act = async () => await converter.ConvertAsync(money, CurrencyCode.EUR);

        // Assert
        await act.Should().ThrowAsync<Exception>();
    }

    [Fact]
    public async Task GetExchangeRateAsyncWithDirectRateReturnsRate()
    {
        // Arrange
        var exchangeRates = new Dictionary<CurrencyCode, Dictionary<CurrencyCode, decimal>>
        {
            [CurrencyCode.USD] = new Dictionary<CurrencyCode, decimal>
            {
                [CurrencyCode.EUR] = 0.85m
            }
        };

        var converter = new CurrencyConverter(exchangeRates);

        // Act
        var result = await converter.GetExchangeRateAsync(CurrencyCode.USD, CurrencyCode.EUR);

        // Assert
        result.Should().Be(0.85m);
    }

    [Fact]
    public async Task GetExchangeRateAsyncWithInverseRateReturnsInverseRate()
    {
        //TODO
    }

    [Fact]
    public async Task GetExchangeRateAsyncWithSameCurrencyReturnsOne()
    {
        //TODO
    }

    [Fact]
    public async Task AddExchangeRateAsyncAddsNewRate()
    {
        // Arrange
        var exchangeRates = new Dictionary<CurrencyCode, Dictionary<CurrencyCode, decimal>>
        {
            [CurrencyCode.USD] = new Dictionary<CurrencyCode, decimal>
            {
                [CurrencyCode.EUR] = 0.85m
            }
        };
        var converter = new CurrencyConverter(exchangeRates);

        // Act
        var rate = await converter.GetExchangeRateAsync(CurrencyCode.USD	, CurrencyCode.EUR);

        // Assert
        rate.Should().Be(0.85m);
    }

}
