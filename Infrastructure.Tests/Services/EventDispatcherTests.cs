using Domain.Events;
using Infrastructure.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Infrastructure.Tests.Services;

public class EventDispatcherTests
{
    private readonly Mock<IServiceProvider> _serviceProviderMock;
    private readonly Mock<ILogger<EventDispatcher>> _loggerMock;
    private readonly Mock<IServiceScope> _serviceScopeMock;
    private readonly Mock<IServiceScopeFactory> _serviceScopeFactoryMock;
    private readonly EventDispatcher _eventDispatcher;
    
    public EventDispatcherTests()
    {
        _serviceProviderMock = new Mock<IServiceProvider>();
        _loggerMock = new Mock<ILogger<EventDispatcher>>();
        _serviceScopeMock = new Mock<IServiceScope>();
        _serviceScopeFactoryMock = new Mock<IServiceScopeFactory>();
        
        _serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IServiceScopeFactory)))
            .Returns(_serviceScopeFactoryMock.Object);
        
        _serviceScopeFactoryMock
            .Setup(f => f.CreateScope())
            .Returns(_serviceScopeMock.Object);
        
        _serviceScopeMock
            .Setup(s => s.ServiceProvider)
            .Returns(_serviceProviderMock.Object);
        
        _eventDispatcher = new EventDispatcher(_serviceProviderMock.Object, _loggerMock.Object);
    }
    
    [Fact]
    public async Task DispatchAsyncGenericWithRegisteredHandlersCallsHandlers()
    {
        // Arrange
        var @event = new SaleCompletedDomainEvent(Guid.NewGuid());
        
        var handler1 = new Mock<IDomainEventHandler<SaleCompletedDomainEvent>>();
        var handler2 = new Mock<IDomainEventHandler<SaleCompletedDomainEvent>>();
        
        var handlers = new List<IDomainEventHandler<SaleCompletedDomainEvent>> 
        { 
            handler1.Object, 
            handler2.Object 
        };
        
        _serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IEnumerable<IDomainEventHandler<SaleCompletedDomainEvent>>)))
            .Returns(handlers);
        
        // Act
        await _eventDispatcher.DispatchAsync(@event);
        
        // Assert
        handler1.Verify(h => h.HandleAsync(@event), Times.Once);
        handler2.Verify(h => h.HandleAsync(@event), Times.Once);
    }
    
    [Fact]
    public async Task DispatchAsyncGenericWithNoHandlersDoesNothing()
    {
        // Arrange
        var @event = new SaleCompletedDomainEvent(Guid.NewGuid());
        
        _serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IEnumerable<IDomainEventHandler<SaleCompletedDomainEvent>>)))
            .Returns(new List<IDomainEventHandler<SaleCompletedDomainEvent>>());
        
        // Act & Assert
        await _eventDispatcher.DispatchAsync(@event);
        
        // No exception should be thrown
    }
    
    [Fact]
    public async Task DispatchAsyncGenericWithHandlerExceptionLogsErrorAndRethrows()
    {
        // Arrange
        var @event = new SaleCompletedDomainEvent(Guid.NewGuid());
        var exception = new Exception("Test exception");
        
        var handler = new Mock<IDomainEventHandler<SaleCompletedDomainEvent>>();
        handler
            .Setup(h => h.HandleAsync(@event))
            .ThrowsAsync(exception);
        
        var handlers = new List<IDomainEventHandler<SaleCompletedDomainEvent>> { handler.Object };
        
        _serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IEnumerable<IDomainEventHandler<SaleCompletedDomainEvent>>)))
            .Returns(handlers);
        
        // Act
        Func<Task> act = async () => await _eventDispatcher.DispatchAsync(@event);
        
        // Assert
        await act.Should().ThrowAsync<Exception>();
        
        // Verify that the error was logged
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => true),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }
    
    [Fact]
    public async Task DispatchAsyncNonGenericWithRegisteredHandlersCallsHandlers()
    {
        // Arrange
        var @event = new SaleCompletedDomainEvent(Guid.NewGuid());
        
        var handler1 = new Mock<IDomainEventHandler<SaleCompletedDomainEvent>>();
        var handler2 = new Mock<IDomainEventHandler<SaleCompletedDomainEvent>>();
        
        var handlers = new List<object> { handler1.Object, handler2.Object };
        
        _serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IEnumerable<IDomainEventHandler<SaleCompletedDomainEvent>>)))
            .Returns(handlers);
        
        // Act
        await _eventDispatcher.DispatchAsync((DomainEvent)@event);
        
        // Assert
        handler1.Verify(h => h.HandleAsync(@event), Times.Once);
        handler2.Verify(h => h.HandleAsync(@event), Times.Once);
    }
    
    [Fact]
    public async Task DispatchAsyncNonGenericWithNoHandlersDoesNothing()
    {
        // Arrange
        var @event = new SaleCompletedDomainEvent(Guid.NewGuid());
        
        _serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IEnumerable<IDomainEventHandler<SaleCompletedDomainEvent>>)))
            .Returns(new List<IDomainEventHandler<SaleCompletedDomainEvent>>());
        
        // Act & Assert
        await _eventDispatcher.DispatchAsync((DomainEvent)@event);
        
        // No exception should be thrown
    }
    
    [Fact]
    public async Task DispatchAsyncNonGenericWithHandlerExceptionLogsErrorAndRethrows()
    {
        // Arrange
        var @event = new SaleCompletedDomainEvent(Guid.NewGuid());
        var exception = new Exception("Test exception");
        
        var handler = new Mock<IDomainEventHandler<SaleCompletedDomainEvent>>();
        handler
            .Setup(h => h.HandleAsync(@event))
            .ThrowsAsync(exception);
        
        var handlers = new List<object> { handler.Object };
        
        _serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IEnumerable<IDomainEventHandler<SaleCompletedDomainEvent>>)))
            .Returns(handlers);
        
        // Act
        Func<Task> act = async () => await _eventDispatcher.DispatchAsync((DomainEvent)@event);
        
        // Assert
        await act.Should().ThrowAsync<Exception>();
        
        // Verify that the error was logged
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => true),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }
}
