using Application.Interfaces;
using Domain.Entities;
using Domain.Events;
using Infrastructure.Data;
using Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SharedKernel;

namespace Infrastructure.Tests.Services;

public class OutboxProcessingServiceTests : IDisposable
{
    private readonly ApplicationDbContext _dbContext;
    private readonly Mock<IServiceProvider> _serviceProviderMock;
    private readonly Mock<IServiceScope> _serviceScopeMock;
    private readonly Mock<IServiceScopeFactory> _serviceScopeFactoryMock;
    private readonly Mock<IEventDispatcher> _eventDispatcherMock;
    private readonly Mock<ILogger<OutboxProcessingService>> _loggerMock;
    private readonly OutboxProcessingService _service;
    
    public OutboxProcessingServiceTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: $"OutboxProcessingServiceTests_{Guid.NewGuid()}")
            .Options;
        
        _dbContext = new ApplicationDbContext(options);
        
        _serviceProviderMock = new Mock<IServiceProvider>();
        _serviceScopeMock = new Mock<IServiceScope>();
        _serviceScopeFactoryMock = new Mock<IServiceScopeFactory>();
        _eventDispatcherMock = new Mock<IEventDispatcher>();
        _loggerMock = new Mock<ILogger<OutboxProcessingService>>();
        
        _serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IServiceScopeFactory)))
            .Returns(_serviceScopeFactoryMock.Object);
        
        _serviceScopeFactoryMock
            .Setup(f => f.CreateScope())
            .Returns(_serviceScopeMock.Object);
        
        _serviceScopeMock
            .Setup(s => s.ServiceProvider)
            .Returns(_serviceProviderMock.Object);
        
        _serviceProviderMock
            .Setup(sp => sp.GetService(typeof(ApplicationDbContext)))
            .Returns(_dbContext);
        
        _serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IEventDispatcher)))
            .Returns(_eventDispatcherMock.Object);
        
        _service = new OutboxProcessingService(_serviceProviderMock.Object, _loggerMock.Object);
    }
    
    public void Dispose()
    {
        _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }
    
    [Fact]
    public async Task ExecuteAsyncWithUnprocessedMessagesProcessesMessages()
    {
        // Arrange
        var cancellationToken = new CancellationTokenSource();
        var saleCompletedEvent = new SaleCompletedDomainEvent(Guid.NewGuid());
        
        var message = new OutboxMessage(
            saleCompletedEvent.GetType().AssemblyQualifiedName!,
            JsonConvert.SerializeObject(saleCompletedEvent, new JsonSerializerSettings { TypeNameHandling = TypeNameHandling.All }));
        
        await _dbContext.OutboxMessages.AddAsync(message);
        await _dbContext.SaveChangesAsync();
        
        // Act
        // Start the service
        var task = _service.StartAsync(cancellationToken.Token);
        
        // Wait a bit for the service to process the message
        await Task.Delay(100);
        
        // Stop the service
        await _service.StopAsync(cancellationToken.Token);
        
        // Assert
        _eventDispatcherMock.Verify(
            d => d.DispatchAsync(It.IsAny<DomainEvent>(), It.IsAny<CancellationToken>()),
            Times.AtLeastOnce);
        
        var processedMessage = await _dbContext.OutboxMessages.FirstOrDefaultAsync();
        processedMessage.Should().NotBeNull();
        processedMessage!.ProcessedAt.Should().NotBeNull();
        processedMessage.Error.Should().BeNull();
    }
    
    [Fact]
    public async Task ExecuteAsyncWithDeserializationErrorMarksMessageAsFailed()
    {
        // Arrange
        var cancellationToken = new CancellationTokenSource();
        
        // Create a message with invalid content
        var message = new OutboxMessage(
            typeof(SaleCompletedDomainEvent).AssemblyQualifiedName!,
            "Invalid JSON content");
        
        await _dbContext.OutboxMessages.AddAsync(message);
        await _dbContext.SaveChangesAsync();
        
        // Act
        // Start the service
        var task = _service.StartAsync(cancellationToken.Token);
        
        // Wait a bit for the service to process the message
        await Task.Delay(100);
        
        // Stop the service
        await _service.StopAsync(cancellationToken.Token);
        
        // Assert
        var processedMessage = await _dbContext.OutboxMessages.FirstOrDefaultAsync();
        processedMessage.Should().NotBeNull();
        processedMessage!.ProcessedAt.Should().BeNull();
        processedMessage.Error.Should().NotBeNull();
    }
    
    [Fact]
    public async Task ExecuteAsyncWithDispatchErrorMarksMessageAsFailed()
    {
        // Arrange
        var cancellationToken = new CancellationTokenSource();
        var saleCompletedEvent = new SaleCompletedDomainEvent(Guid.NewGuid());
        
        var message = new OutboxMessage(
            saleCompletedEvent.GetType().AssemblyQualifiedName!,
            JsonConvert.SerializeObject(saleCompletedEvent, new JsonSerializerSettings { TypeNameHandling = TypeNameHandling.All }));
        
        await _dbContext.OutboxMessages.AddAsync(message);
        await _dbContext.SaveChangesAsync();
        
        // Setup event dispatcher to throw an exception
        _eventDispatcherMock
            .Setup(d => d.DispatchAsync(It.IsAny<DomainEvent>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test dispatch error"));
        
        // Act
        // Start the service
        var task = _service.StartAsync(cancellationToken.Token);
        
        // Wait a bit for the service to process the message
        await Task.Delay(100);
        
        // Stop the service
        await _service.StopAsync(cancellationToken.Token);
        
        // Assert
        var processedMessage = await _dbContext.OutboxMessages.FirstOrDefaultAsync();
        processedMessage.Should().NotBeNull();
        processedMessage!.ProcessedAt.Should().BeNull();
        processedMessage.Error.Should().Be("Test dispatch error");
    }
    
    [Fact]
    public async Task ExecuteAsyncWithNoMessagesDoesNothing()
    {
        // Arrange
        var cancellationToken = new CancellationTokenSource();
        
        // Act
        // Start the service
        var task = _service.StartAsync(cancellationToken.Token);
        
        // Wait a bit for the service to run
        await Task.Delay(100);
        
        // Stop the service
        await _service.StopAsync(cancellationToken.Token);
        
        // Assert
        _eventDispatcherMock.Verify(
            d => d.DispatchAsync(It.IsAny<DomainEvent>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }
    
    [Fact]
    public async Task ExecuteAsyncWithAlreadyProcessedMessagesSkipsProcessedMessages()
    {
        // Arrange
        var cancellationToken = new CancellationTokenSource();
        var saleCompletedEvent = new SaleCompletedDomainEvent(Guid.NewGuid());
        
        var message = new OutboxMessage(
            saleCompletedEvent.GetType().AssemblyQualifiedName!,
            JsonConvert.SerializeObject(saleCompletedEvent, new JsonSerializerSettings { TypeNameHandling = TypeNameHandling.All }));
        
        // Mark as already processed
        message.MarkAsProcessed();
        
        await _dbContext.OutboxMessages.AddAsync(message);
        await _dbContext.SaveChangesAsync();
        
        // Act
        // Start the service
        var task = _service.StartAsync(cancellationToken.Token);
        
        // Wait a bit for the service to run
        await Task.Delay(100);
        
        // Stop the service
        await _service.StopAsync(cancellationToken.Token);
        
        // Assert
        _eventDispatcherMock.Verify(
            d => d.DispatchAsync(It.IsAny<DomainEvent>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }
}
