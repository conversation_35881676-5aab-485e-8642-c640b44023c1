using Application.EventHandlers;
using Application.Interfaces;
using Application.Services;
using Domain.Interfaces;
using Domain.Events;
using Domain.Managers;
using Domain.Services;
using Infrastructure.Data;
using Infrastructure.Projections;
using Infrastructure.Repositories;
using Infrastructure.Services;
using Microsoft.Extensions.DependencyInjection;
using SharedKernel;

namespace Infrastructure.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInventoryEventHandlers(this IServiceCollection services)
    {
        services.AddScoped<InventoryProjection>();

        services.AddScoped<IDomainEventHandler<InventoryItemWastedDomainEvent>>(sp =>
            sp.GetRequiredService<InventoryProjection>());

        services.AddScoped<IDomainEventHandler<InventoryItemPriceUpdatedDomainEvent>>(sp =>
            sp.GetRequiredService<InventoryProjection>());
        return services;
    }

    public static IServiceCollection AddSaleEventHandlers(this IServiceCollection services)
    {
        services.AddScoped<SaleCompletedDomainEventHandler>();
        services.AddScoped<IDomainEventHandler<SaleCompletedDomainEvent>>(sp =>
            sp.GetRequiredService<SaleCompletedDomainEventHandler>());
        return services;
    }

    public static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        // Register repositories
        services.AddScoped<IInventoryRepository, InventoryRepository>();
        services.AddScoped<ISaleRepository, SaleRepository>();
        services.AddScoped<IProductRepository, ProductRepository>();
        services.AddScoped<ITagRepository, TagRepository>();
        services.AddScoped<IProductCategoryRepository, ProductCategoryRepository>();

        // Register event dispatcher and unit of work
        services.AddScoped<IEventDispatcher, EventDispatcher>();
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Register background services
        services.AddHostedService<OutboxProcessingService>();

        return services;
    }
    
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // Register domain services
        services.AddScoped<ITagManager, TagDomainService>();
        services.AddScoped<IProductCategoryManager, ProductCategoryDomainService>();
        services.AddScoped<ISaleManager, SaleDomainService>();
        services.AddScoped<IInventoryManager, InventoryDomainService>();
        services.AddScoped<ICurrencyConverter, CurrencyConverter>();
        
        // Register application services
        services.AddScoped<ITagService, TagService>();
        services.AddScoped<IProductService, ProductService>();
        services.AddScoped<IProductCategoryService, ProductCategoryService>();
        services.AddScoped<IInventoryService, InventoryService>();
        services.AddScoped<ISaleService, SaleService>();
        
        return services;
    }
    
    public static IServiceCollection AddDatabaseSeeder(this IServiceCollection services)
    {
        services.AddScoped<DatabaseSeeder>();
        return services;
    }
}
