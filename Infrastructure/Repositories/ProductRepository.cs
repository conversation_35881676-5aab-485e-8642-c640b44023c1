using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Domain.Interfaces;
using Domain.Entities.ProductAggregate;

namespace Infrastructure.Repositories;

public class ProductRepository(ApplicationDbContext dbContext) : IProductRepository
{
    public async Task<List<Product>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await dbContext.Products
            .Include(x => x.ProductTags)
            .ToListAsync(cancellationToken);
    }

    public async Task<Product> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await dbContext.Products
            .Include(x => x.ProductTags)
            .FirstAsync(p => p.Id == id, cancellationToken);
    }

    public async Task<Product?> GetByIdOrDefaultAsync(int id, CancellationToken cancellationToken = default)
    {
        return await dbContext.Products
            .Include(x => x.ProductTags)
            .FirstOrDefaultAsync(p => p.Id == id, cancellationToken);
    }

    public Task<int> AddAsync(Product product, CancellationToken cancellationToken = default)
    {
        dbContext.Products.Add(product);
        return Task.FromResult(product.Id);
    }

    public Task UpdateAsync(Product product, CancellationToken cancellationToken = default)
    {
        dbContext.Products.Update(product);
        return Task.CompletedTask;
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var product = await GetByIdOrDefaultAsync(id, cancellationToken);
        if (product is null)
        {
            return false;
        }

        dbContext.Products.Remove(product);
        return true;
    }

    public async Task<List<Product>> GetProductsByCategoryIdAsync(int categoryId,
        CancellationToken cancellationToken = default)
    {
        return await dbContext.Products
            .Include(x => x.ProductTags)
            .Where(p => p.CategoryId == categoryId)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Product>> GetProductsByTagIdAsync(int tagId, CancellationToken cancellationToken = default)
    {
        var productIds = await dbContext.ProductTags
            .Where(pt => pt.TagId == tagId)
            .Select(pt => pt.ProductId)
            .ToListAsync(cancellationToken);

        return await dbContext.Products
            .Include(x => x.ProductTags)
            .Where(p => productIds.Contains(p.Id))
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Product>> SearchProductsAsync(string searchTerm,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            return await GetAllAsync(cancellationToken);
        }

        searchTerm = searchTerm.ToLower();

        return await dbContext.Products
            .Include(x => x.ProductTags)
            .Where(p => p.Name.ToLower().Contains(searchTerm) ||
                        p.Description.ToLower().Contains(searchTerm))
            .ToListAsync(cancellationToken);
    }
}