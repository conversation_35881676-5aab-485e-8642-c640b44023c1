using Domain.Interfaces;
using Domain.Entities.ProductAggregate;
using Domain.Entities.ProductCategoryAggregate;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repositories;

public class ProductCategoryRepository(ApplicationDbContext dbContext) : IProductCategoryRepository
{
    public async Task<List<ProductCategory>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await dbContext.Categories
            .OrderBy(c => c.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<ProductCategory> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await dbContext.Categories
            .FirstAsync(c => c.Id == id, cancellationToken);
    }

    public async Task<ProductCategory?> GetByIdOrDefaultAsync(int id, CancellationToken cancellationToken = default)
    {
        return await dbContext.Categories
            .FirstOrDefaultAsync(c => c.Id == id, cancellationToken);
    }

    public Task<int> AddAsync(ProductCategory category, CancellationToken cancellationToken = default)
    {
        dbContext.Categories.Add(category);
        return Task.FromResult(category.Id);
    }

    public Task UpdateAsync(ProductCategory category, CancellationToken cancellationToken = default)
    {
        dbContext.Categories.Update(category);
        return Task.CompletedTask;
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var category = await GetByIdOrDefaultAsync(id, cancellationToken);
        if (category is null)
        {
            return false;
        }

        dbContext.Categories.Remove(category);
        return true;
    }

    public async Task<bool> IsCategoryInUseAsync(int categoryId, CancellationToken cancellationToken = default)
    {
        return await dbContext.Products
            .AnyAsync(p => p.CategoryId == categoryId, cancellationToken);
    }

    public async Task<bool> IsCategoryNameUniqueAsync(string name, CancellationToken cancellationToken = default)
    {
        return !await dbContext.Categories
            .AnyAsync(p => p.Name == name, cancellationToken);
    }

    public async Task<List<Product>> GetProductsByCategoryIdAsync(int categoryId,
        CancellationToken cancellationToken = default)
    {
        return await dbContext.Products
            .Where(p => p.CategoryId == categoryId)
            .ToListAsync(cancellationToken);
    }
}