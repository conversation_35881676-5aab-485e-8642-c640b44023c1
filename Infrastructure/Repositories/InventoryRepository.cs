using Domain.Entities.InventoryAggregate;
using Domain.Enums;
using Domain.Interfaces;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repositories;

public class InventoryRepository(ApplicationDbContext dbContext) : IInventoryRepository
{
    public async Task<List<Inventory>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await dbContext.Inventories
            .Include(i => i.Items)
            .ThenInclude(item => item.Prices)
            .Include(i => i.Items)
            .ThenInclude(item => item.WasteRecords)
            .ToListAsync(cancellationToken);
    }

    public async Task<Inventory> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var inventory = await dbContext.Inventories
            .Include(i => i.Items)
            .ThenInclude(item => item.Prices)
            .Include(i => i.Items)
            .ThenInclude(item => item.WasteRecords)
            .FirstAsync(i => i.Id == id, cancellationToken);

        return inventory;
    }

    public async Task<Inventory?> GetByIdOrDefaultAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await dbContext.Inventories
            .Include(i => i.Items)
            .ThenInclude(item => item.Prices)
            .Include(i => i.Items)
            .ThenInclude(item => item.WasteRecords)
            .FirstOrDefaultAsync(i => i.Id == id, cancellationToken);
    }

    public async Task<Guid> AddAsync(Inventory entity, CancellationToken cancellationToken = default)
    {
        await dbContext.Inventories.AddAsync(entity, cancellationToken);
        return entity.Id;
    }

    public Task UpdateAsync(Inventory inventory, CancellationToken cancellationToken = default)
    {
        dbContext.Inventories.Update(inventory);
        return Task.CompletedTask;
    }

    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var inventory = await GetByIdOrDefaultAsync(id, cancellationToken);

        if (inventory is null)
        {
            return false;
        }

        dbContext.Inventories.Remove(inventory);
        return true;
    }

    public async Task<Inventory?> GetMostRecentInventoryAsync(CancellationToken cancellationToken = default)
    {
        var latestInventory = await dbContext.Inventories
            .Include(i => i.Items)
            .ThenInclude(item => item.Prices)
            .Include(i => i.Items)
            .ThenInclude(item => item.WasteRecords)
            .OrderByDescending(i => i.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);

        return latestInventory;
    }

    public async Task<InventoryItem?> GetInventoryItemByProductIdAsync(int productId,
        CancellationToken cancellationToken = default)
    {
        var currentInventory = await GetMostRecentInventoryAsync(cancellationToken);

        return currentInventory?.Items.FirstOrDefault(i => i.ProductId == productId);
    }

    public async Task<List<InventoryItem>> GetOutOfStockItemsAsync(CancellationToken cancellationToken = default)
    {
        var currentInventory = await GetMostRecentInventoryAsync(cancellationToken);

        return currentInventory?.Items
            .Where(i => i.Status == InventoryItemStatus.OutOfStock)
            .ToList() ?? [];
    }

    public async Task<List<InventoryItemPrice>> GetPriceHistoryForProductAsync(int productId,
        CancellationToken cancellationToken = default)
    {
        var currentInventory = await GetMostRecentInventoryAsync(cancellationToken);

        var item = currentInventory?.Items.FirstOrDefault(i => i.ProductId == productId);

        return item?.Prices.ToList() ?? [];
    }

    public async Task<List<WasteRecord>> GetWasteRecordsForProductAsync(int productId,
        CancellationToken cancellationToken = default)
    {
        var currentInventory = await GetMostRecentInventoryAsync(cancellationToken);

        var item = currentInventory?.Items.FirstOrDefault(i => i.ProductId == productId);

        return item?.WasteRecords.OrderByDescending(w => w.RecordedDate).ToList() ?? [];
    }
}