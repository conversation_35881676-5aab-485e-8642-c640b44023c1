using Domain.Interfaces;
using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repositories;

public class SaleRepository(ApplicationDbContext dbContext) : ISaleRepository
{
    public async Task<Sale> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var sale = await dbContext.Sales
            .Include(s => s.Items).FirstAsync(s => s.Id == id, cancellationToken);

        return sale;
    }

    public async Task<Sale?> GetByIdOrDefaultAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await dbContext.Sales
            .Include(s => s.Items)
            .FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
    }

    public async Task<List<Sale>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await dbContext.Sales
            .Include(s => s.Items)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Sale>> GetSalesByStatusAsync(SaleStatus status,
        CancellationToken cancellationToken = default)
    {
        return await dbContext.Sales
            .Include(s => s.Items)
            .Where(s => s.Status == status)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        return await dbContext.Sales
            .Include(s => s.Items)
            .Where(s => s.CreatedAt >= startDate && s.CreatedAt <= endDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Sale>> GetSalesByProductIdAsync(int productId, CancellationToken cancellationToken = default)
    {
        return await dbContext.Sales
            .Include(s => s.Items)
            .Where(s => s.Items.Any(i => i.ProductId == productId))
            .ToListAsync(cancellationToken);
    }

    public async Task<Guid> AddAsync(Sale sale, CancellationToken cancellationToken = default)
    {
        await dbContext.Sales.AddAsync(sale, cancellationToken);
        return sale.Id;
    }

    public Task UpdateAsync(Sale sale, CancellationToken cancellationToken = default)
    {
        dbContext.Sales.Update(sale);
        return Task.CompletedTask;
    }

    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var sale = await GetByIdOrDefaultAsync(id, cancellationToken);

        if (sale is null)
        {
            return false;
        }

        dbContext.Sales.Remove(sale);
        return true;
    }
}