using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Domain.Interfaces;
using Domain.Entities.ProductAggregate;
using Domain.Entities.TagAggregate;

namespace Infrastructure.Repositories;

public class TagRepository(ApplicationDbContext dbContext) : ITagRepository
{
    public async Task<List<Tag>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await dbContext.Tags
            .OrderBy(t => t.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<Tag> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await dbContext.Tags
            .FirstAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<Tag?> GetByIdOrDefaultAsync(int id, CancellationToken cancellationToken = default)
    {
        return await dbContext.Tags
            .FirstOrDefaultAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<Tag> GetBySlugAsync(string slug, CancellationToken cancellationToken = default)
    {
        return await dbContext.Tags.FirstAsync(t => t.Slug == slug, cancellationToken);
    }

    public Task<int> AddAsync(Tag tag, CancellationToken cancellationToken = default)
    {
        dbContext.Tags.Add(tag);
        return Task.FromResult(tag.Id);
    }

    public Task UpdateAsync(Tag tag, CancellationToken cancellationToken = default)
    {
        dbContext.Tags.Update(tag);
        return Task.CompletedTask;
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        var tag = await GetByIdOrDefaultAsync(id, cancellationToken);
        if (tag is null)
        {
            return false;
        }

        dbContext.Tags.Remove(tag);
        return true;
    }

    public async Task<List<Tag>> GetTagsByProductIdAsync(int productId, CancellationToken cancellationToken = default)
    {
        var tagIds = await dbContext.ProductTags
            .Where(pt => pt.ProductId == productId)
            .Select(pt => pt.TagId)
            .ToListAsync(cancellationToken);

        return await dbContext.Tags
            .Where(t => tagIds.Contains(t.Id))
            .ToListAsync(cancellationToken);
    }

    public async Task<List<Product>> GetProductsByTagIdAsync(int tagId, CancellationToken cancellationToken = default)
    {
        var productIds = await dbContext.ProductTags
            .Where(pt => pt.TagId == tagId)
            .Select(pt => pt.ProductId)
            .ToListAsync(cancellationToken);

        return await dbContext.Products
            .Where(p => productIds.Contains(p.Id))
            .ToListAsync(cancellationToken);
    }


    public async Task<bool> IsSlugUniqueAsync(string slug, CancellationToken cancellationToken = default)
    {
        return !await dbContext.Tags.AnyAsync(x => x.Slug == slug, cancellationToken);
    }
}