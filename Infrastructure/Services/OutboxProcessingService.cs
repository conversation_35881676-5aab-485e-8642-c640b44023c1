using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Application.Interfaces;
using Newtonsoft.Json;
using SharedKernel;

namespace Infrastructure.Services;

public class OutboxProcessingService(
    IServiceProvider serviceProvider,
    ILogger<OutboxProcessingService> logger)
    : BackgroundService
{
    private readonly TimeSpan _processInterval = TimeSpan.FromSeconds(10);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("Outbox Processing Service is starting.");

        while (!stoppingToken.IsCancellationRequested)
        {
            logger.LogDebug("Processing outbox messages...");

            try
            {
                await ProcessOutboxMessages(stoppingToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while processing outbox messages");
            }

            await Task.Delay(_processInterval, stoppingToken);
        }

        logger.LogInformation("Outbox Processing Service is stopping.");
    }

    private async Task ProcessOutboxMessages(CancellationToken stoppingToken)
    {
        using var scope = serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var eventDispatcher = scope.ServiceProvider.GetRequiredService<IEventDispatcher>();

        try
        {
            // Only process a few messages at a time to avoid overwhelming the system
            var messages = await dbContext.OutboxMessages
                .Where(m => m.ProcessedAt == null && m.Error == null)
                .OrderBy(m => m.CreatedAt)
                .Take(5) // Process in smaller batches
                .ToListAsync(stoppingToken);

            if (messages.Count == 0)
            {
                return;
            }

            logger.LogInformation("Found {Count} outbox messages to process", messages.Count);

            // Process each message individually and save changes after each one
            // This ensures that if one message fails, others can still be processed
            foreach (var message in messages)
            {
                try
                {
                    logger.LogDebug("Processing outbox message {MessageId} of type {MessageType}",
                        message.Id, message.Type);

                    var eventType = message.Type.GetTypeFromName();

                    var domainEvent = JsonConvert.DeserializeObject(message.Content, eventType);
                    if (domainEvent is not DomainEvent typedEvent)
                    {
                        message.MarkAsFailed($"Could not deserialize message to {message.Type}");
                        await dbContext.SaveChangesAsync(stoppingToken);
                        continue;
                    }

                    // Process the event
                    await eventDispatcher.DispatchAsync(typedEvent, stoppingToken);
                    message.MarkAsProcessed();

                    // Save changes after each successful message
                    await dbContext.SaveChangesAsync(stoppingToken);

                    logger.LogInformation("Successfully processed outbox message {MessageId}", message.Id);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error processing outbox message {MessageId}", message.Id);
                    message.MarkAsFailed(ex.Message);

                    // Save the error status
                    try
                    {
                        await dbContext.SaveChangesAsync(stoppingToken);
                    }
                    catch (Exception saveEx)
                    {
                        logger.LogError(saveEx, "Failed to save error status for message {MessageId}", message.Id);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving or processing outbox messages");
        }
    }
}
