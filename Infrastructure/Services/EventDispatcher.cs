using Application.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SharedKernel;

namespace Infrastructure.Services;

public class EventDispatcher(IServiceProvider serviceProvider, ILogger<EventDispatcher> logger)
    : IEventDispatcher
{
    public async Task DispatchAsync<TEvent>(TEvent @event, CancellationToken cancellationToken = default) where TEvent : DomainEvent
    {
        using var scope = serviceProvider.CreateScope();
        var handlers = scope.ServiceProvider.GetServices<IDomainEventHandler<TEvent>>();

        foreach (var handler in handlers)
        {
            try
            {
                await handler.HandleAsync(@event);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error handling event {EventType} by handler {HandlerType}",
                    typeof(TEvent).Name, handler.GetType().Name);
                throw;
            }
        }
    }

    public async Task DispatchAsync(DomainEvent @event, CancellationToken cancellationToken = default)
    {
        var eventType = @event.GetType();
        logger.LogDebug("Dispatching event of type {EventType}", eventType.Name);

        using var scope = serviceProvider.CreateScope();
        var handlerType = typeof(IDomainEventHandler<>).MakeGenericType(eventType);
        var handlers = scope.ServiceProvider.GetServices(handlerType);

        foreach (var handler in handlers)
        {
            try
            {
                var handleMethod = handlerType.GetMethod("HandleAsync");
                if (handleMethod != null)
                {
                    await (Task)handleMethod.Invoke(handler, [@event]);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error handling event {EventType} by handler {HandlerType}",
                    eventType.Name, handler?.GetType().Name);
                throw;
            }
        }
    }
}
