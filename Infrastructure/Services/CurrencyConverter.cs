using Domain.Enums;
using Domain.Interfaces;
using Domain.ValueObjects;

namespace Infrastructure.Services;

public class CurrencyConverter(Dictionary<CurrencyCode, Dictionary<CurrencyCode, decimal>>? exchangeRates = null)
    : ICurrencyConverter
{
    private readonly Dictionary<CurrencyCode, Dictionary<CurrencyCode, decimal>> _exchangeRates =
        exchangeRates ?? [];

    public async Task<Money> ConvertAsync(Money source, CurrencyCode targetCurrency, CancellationToken cancellationToken = default)
    {
        if (source.CurrencyCode == targetCurrency)
        {
            return source;
        }

        var rate = await GetExchangeRateAsync(source.CurrencyCode, targetCurrency, cancellationToken);
        return source.ConvertTo(targetCurrency, rate);
    }


    public async Task<decimal> GetExchangeRateAsync(CurrencyCode sourceCurrency, CurrencyCode targetCurrency, CancellationToken cancellationToken = default)
    {
        if (_exchangeRates.TryGetValue(sourceCurrency, out var rates) &&
            rates.TryGetValue(targetCurrency, out var rate))
        {
            return rate;
        }

        throw new KeyNotFoundException($"Exchange rate from {sourceCurrency} to {targetCurrency} not found");
    }
}