using Domain.Events;
using Infrastructure.Data;
using SharedKernel;

namespace Infrastructure.Projections;

public class InventoryProjection(ApplicationDbContext dbContext) :
    IDomainEventHandler<InventoryItemWastedDomainEvent>,
    IDomainEventHandler<InventoryItemPriceUpdatedDomainEvent>
{

    public async Task HandleAsync(InventoryItemWastedDomainEvent @event)
    {

    }

    public async Task HandleAsync(InventoryItemPriceUpdatedDomainEvent @event)
    {

    }
}