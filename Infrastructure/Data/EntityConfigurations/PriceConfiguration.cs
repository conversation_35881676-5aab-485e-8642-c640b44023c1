using Domain.Entities.InventoryAggregate;
using Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.EntityConfigurations;

public class PriceConfiguration : IEntityTypeConfiguration<InventoryItemPrice>
{
    public void Configure(EntityTypeBuilder<InventoryItemPrice> builder)
    {
        builder.HasKey(e => e.Id);
        builder.Property(e => e.CreatedAt).IsRequired();
        
        builder.OwnsOne(e => e.ActualPrice, price =>
        {
            price.Property(p => p.Amount).HasColumnType("decimal(18,2)");
            price.Property(p => p.CurrencyCode).HasConversion(c => c.ToString(), c => Enum.Parse<CurrencyCode>(c))
                .HasMaxLength(3);
        });
        
        builder.OwnsOne(e => e.SellingPrice, price =>
        {
            price.Property(p => p.Amount).HasColumnType("decimal(18,2)");
            price.Property(p => p.CurrencyCode).HasConversion(c => c.ToString(), c => Enum.Parse<CurrencyCode>(c))
                .HasMaxLength(3);
        });
    }
}
