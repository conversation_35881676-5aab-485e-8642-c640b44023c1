using Domain.Entities.ProductAggregate;
using Domain.Entities.TagAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.EntityConfigurations;

public class ProductTagConfiguration : IEntityTypeConfiguration<ProductTag>
{
    public void Configure(EntityTypeBuilder<ProductTag> builder)
    {
        builder
            .HasKey(e => new { e.ProductId, e.TagId });

        builder
            .HasOne<Product>()
            .WithMany(x => x.ProductTags)
            .HasForeignKey(x => x.ProductId);

        builder
            .HasOne<Tag>()
            .WithMany()
            .HasForeignKey(x => x.TagId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}