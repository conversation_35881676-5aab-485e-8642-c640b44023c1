using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.EntityConfigurations;

public class SaleItemConfiguration : IEntityTypeConfiguration<SaleItem>
{
    public void Configure(EntityTypeBuilder<SaleItem> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.ProductId).IsRequired();
        builder.Property(e => e.ProductName).IsRequired().HasMaxLength(100);
        builder.Property(e => e.Quantity).IsRequired();

        builder.OwnsOne(e => e.Price, money =>
        {
            money.Property(p => p.Amount).HasColumnType("decimal(18,2)");
            money.Property(p => p.CurrencyCode).HasConversion(c => c.ToString(), c => Enum.Parse<CurrencyCode>(c))
                .HasMaxLength(3);
        });
    }
}