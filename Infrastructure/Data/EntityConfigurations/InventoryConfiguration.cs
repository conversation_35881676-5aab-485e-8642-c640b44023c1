using Domain.Entities.InventoryAggregate;
using Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.EntityConfigurations;

public class InventoryConfiguration : IEntityTypeConfiguration<Inventory>
{
    public void Configure(EntityTypeBuilder<Inventory> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.CreatedAt).IsRequired();

        builder.OwnsOne(e => e.Balance, balance =>
        {
            balance.Property(p => p.Amount).HasColumnType("decimal(18,2)");
            balance.Property(p => p.CurrencyCode).HasConversion(c => c.ToString(), c => Enum.Parse<CurrencyCode>(c))
                .HasMaxLength(3);
        });

        builder
            .HasMany(e => e.Items)
            .WithOne()
            .HasForeignKey("InventoryId")
            .OnDelete(DeleteBehavior.Cascade);
    }
}