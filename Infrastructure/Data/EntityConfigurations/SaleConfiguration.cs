using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.EntityConfigurations;

public class SaleConfiguration : IEntityTypeConfiguration<Sale>
{
    public void Configure(EntityTypeBuilder<Sale> builder)
    {
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.CreatedAt).IsRequired();
        builder.Property(e => e.Status).IsRequired();
        builder.Property(e => e.CancellationReason);
        
        builder.OwnsOne(e => e.TotalAmount, money =>
        {
            money.Property(p => p.Amount).HasColumnType("decimal(18,2)");
            money.Property(p => p.CurrencyCode).HasConversion(c => c.ToString(), c => Enum.Parse<CurrencyCode>(c))
                .HasMaxLength(3);
        });
        
        
        builder.OwnsOne(e => e.DiscountAmount, money =>
        {
            money.Property(p => p.Amount).HasColumnType("decimal(18,2)");
            money.Property(p => p.CurrencyCode).HasConversion(c => c.ToString(), c => Enum.Parse<CurrencyCode>(c))
                .HasMaxLength(3);
        });
        
        builder.OwnsOne(e => e.FinalAmount, money =>
        {
            money.Property(p => p.Amount).HasColumnType("decimal(18,2)");
            money.Property(p => p.CurrencyCode).HasConversion(c => c.ToString(), c => Enum.Parse<CurrencyCode>(c))
                .HasMaxLength(3);
        });
        
        builder
            .HasMany(e => e.Items)
            .WithOne()
            .HasForeignKey("SaleId")
            .OnDelete(DeleteBehavior.Cascade);
    }
}
