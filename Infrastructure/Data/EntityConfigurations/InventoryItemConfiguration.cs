using Domain.Entities.InventoryAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Data.EntityConfigurations;

public class InventoryItemConfiguration : IEntityTypeConfiguration<InventoryItem>
{
    public void Configure(EntityTypeBuilder<InventoryItem> builder)
    {
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.ProductId).IsRequired();
        builder.Property(e => e.Quantity).IsRequired();
        builder.Property(e => e.SoldQuantity).IsRequired();
        builder.Property(e => e.TotalSoldQuantity).IsRequired();
        builder.Property(e => e.TotalWastedQuantity).IsRequired();
        builder.Property(e => e.Status).IsRequired();
        

        builder
            .HasMany(e => e.Prices)
            .WithOne()
            .OnDelete(DeleteBehavior.Cascade);
            
        builder
            .HasMany(e => e.WasteRecords)
            .WithOne()
            .OnDelete(DeleteBehavior.Cascade);
    }
}
