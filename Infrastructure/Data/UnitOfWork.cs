using Application.Interfaces;
using Domain.Entities;
using Newtonsoft.Json;
using SharedKernel;

namespace Infrastructure.Data;

public class UnitOfWork(ApplicationDbContext dbContext) : IUnitOfWork
{
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        await ConvertDomainEventsToOutboxMessagesAsync(cancellationToken);
        return await dbContext.SaveChangesAsync(cancellationToken);
    }

    private async Task ConvertDomainEventsToOutboxMessagesAsync(CancellationToken cancellationToken = default)
    {
        var outboxMessages = dbContext.ChangeTracker
            .Entries<IEntity>()
            .Select(x => x.Entity)
            .SelectMany(aggregateRoot =>
            {
                var domainEvents = aggregateRoot.DomainEvents.ToList();
                aggregateRoot.ClearDomainEvents();
                return domainEvents;
            })
            .Select(domainEvent => new OutboxMessage(domainEvent.GetTypeName(),
                JsonConvert.SerializeObject(domainEvent,
                    new JsonSerializerSettings { TypeNameHandling = TypeNameHandling.All })))
            .ToList();

        await dbContext.OutboxMessages.AddRangeAsync(outboxMessages, cancellationToken);
    }
}