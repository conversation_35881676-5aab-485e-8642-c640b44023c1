using Domain.Entities;
using Domain.Entities.ProductAggregate;
using Domain.Entities.ProductCategoryAggregate;
using Domain.Entities.TagAggregate;
using Domain.Entities.InventoryAggregate;
using Domain.Entities.SaleAggregate;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Data;

public class ApplicationDbContext : DbContext
{
    public DbSet<Product> Products { get; set; }
    public DbSet<ProductCategory> Categories { get; set; }
    public DbSet<Tag> Tags { get; set; }
    public DbSet<ProductTag> ProductTags { get; set; }
    public DbSet<Inventory> Inventories { get; set; }
    public DbSet<InventoryItem> InventoryItems { get; set; }
    public DbSet<InventoryItemPrice> Prices { get; set; }
    public DbSet<WasteRecord> WasteRecords { get; set; }
    public DbSet<OutboxMessage> OutboxMessages { get; set; }
    public DbSet<Sale> Sales { get; set; }
    public DbSet<SaleItem> SaleItems { get; set; }

    public ApplicationDbContext() : base()
    {
    }

    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Ignore DomainEvent and its derived types
        modelBuilder.Ignore<SharedKernel.DomainEvent>();

        ApplyEntityConfigurations(modelBuilder);
    }

    private static void ApplyEntityConfigurations(ModelBuilder builder)
    {
        builder.ApplyConfigurationsFromAssembly(System.Reflection.Assembly.GetExecutingAssembly());
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder.EnableDetailedErrors().EnableSensitiveDataLogging();
    }
}
