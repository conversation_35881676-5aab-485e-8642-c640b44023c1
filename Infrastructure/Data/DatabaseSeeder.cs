using Application.DTOs.InventoryDTOs;
using Application.DTOs.ProductDTOs;
using Application.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Data;

public class DatabaseSeeder(IServiceProvider serviceProvider, ILogger<DatabaseSeeder> logger)
{
    private readonly string _defaultCurrency = "USD";

    public async Task SeedAsync()
    {
        try
        {
            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            // Apply migrations
            if ((await dbContext.Database.GetPendingMigrationsAsync()).Any())
            {
                await dbContext.Database.MigrateAsync();
            }

            // Seed data only if the database is empty
            if (!await dbContext.Categories.AnyAsync())
            {
                logger.LogInformation("Seeding database...");

                // Get services
                var categoryService = scope.ServiceProvider.GetRequiredService<IProductCategoryService>();
                var tagService = scope.ServiceProvider.GetRequiredService<ITagService>();
                var productService = scope.ServiceProvider.GetRequiredService<IProductService>();
                var inventoryService = scope.ServiceProvider.GetRequiredService<IInventoryService>();

                // Seed categories
                var categoryIds = await SeedCategoriesAsync(categoryService);

                // Seed tags
                var tagIds = await SeedTagsAsync(tagService);

                // Seed products
                var productIds = await SeedProductsAsync(productService, categoryIds, tagIds);

                // Seed inventory
                await SeedInventoryAsync(inventoryService, productIds);

                logger.LogInformation("Database seeding completed successfully.");
            }
            else
            {
                logger.LogInformation("Database already contains data. Skipping seeding.");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while seeding the database.");
            throw;
        }
    }

    private async Task<List<int>> SeedCategoriesAsync(IProductCategoryService categoryService)
    {
        var categoryNames = new List<string>
        {
            "Electronics",
            "Clothing",
            "Books",
            "Home & Kitchen",
            "Sports & Outdoors"
        };

        var categoryIds = new List<int>();
        foreach (var name in categoryNames)
        {
            var id = await categoryService.CreateCategoryAsync(name, CancellationToken.None);
            categoryIds.Add(id);
        }

        return categoryIds;
    }

    private async Task<List<int>> SeedTagsAsync(ITagService tagService)
    {
        var tagNames = new List<string>
        {
            "New Arrival",
            "Best Seller",
            "Sale",
            "Limited Edition",
            "Eco-Friendly",
            "Premium",
            "Clearance"
        };

        var tagIds = new List<int>();
        foreach (var name in tagNames)
        {
            var id = await tagService.CreateTagAsync(name, CancellationToken.None);
            tagIds.Add(id);
        }

        return tagIds;
    }

    private async Task<List<int>> SeedProductsAsync(
        IProductService productService,
        List<int> categoryIds,
        List<int> tagIds)
    {
        var productData = new List<(string Name, string Description, int CategoryId, List<int> TagIds)>
        {
            ("Smartphone X", "Latest smartphone with advanced features", categoryIds[0], [tagIds[0], tagIds[5]]),
            ("Laptop Pro", "High-performance laptop for professionals", categoryIds[0], [tagIds[1], tagIds[5]]),
            ("T-shirt Basic", "Comfortable cotton t-shirt", categoryIds[1], [tagIds[2]]),
            ("Jeans Classic", "Classic fit jeans for everyday wear", categoryIds[1], [tagIds[1]]),
            ("Novel Adventure", "Bestselling adventure novel", categoryIds[2], [tagIds[1], tagIds[3]]),
            ("Cookbook Deluxe", "Collection of gourmet recipes", categoryIds[2], [tagIds[0]]),
            ("Coffee Maker", "Automatic coffee maker for home use", categoryIds[3], [tagIds[4]]),
            ("Blender Pro", "Professional-grade blender", categoryIds[3], [tagIds[5]]),
            ("Yoga Mat", "Non-slip yoga mat for exercise", categoryIds[4], [tagIds[4]]),
            ("Dumbbells Set", "Set of adjustable dumbbells", categoryIds[4], [tagIds[1]])
        };

        var productIds = new List<int>();
        foreach (var (name, description, categoryId, tagIdsv) in productData)
        {
            var productDto = new CreateProductDto
            (
                Name: name,
                Description: description,
                CategoryId: categoryId,
                TagIds: tagIdsv
            );

            var id = await productService.CreateProductAsync(productDto, CancellationToken.None);
            productIds.Add(id);
        }

        return productIds;
    }

    private async Task SeedInventoryAsync(IInventoryService inventoryService, List<int> productIds)
    {
        var inventoryDto = new CreateInventoryDto(10_000m, _defaultCurrency, null);
        await inventoryService.CreateInventoryAsync(inventoryDto, CancellationToken.None);

        var inventoryItems = new List<(int ProductId, int Quantity, decimal ActualPrice, decimal SellingPrice)>
        {
            (productIds[0], 20, 400m, 799.99m),
            (productIds[1], 15, 800m, 1299.99m),
            (productIds[2], 100, 5m, 19.99m),
            (productIds[3], 50, 20m, 49.99m),
            (productIds[4], 30, 10m, 24.99m),
            (productIds[5], 25, 15m, 34.99m),
            (productIds[6], 10, 50m, 89.99m),
            (productIds[7], 8, 100m, 199.99m),
            (productIds[8], 40, 8m, 29.99m),
            (productIds[9], 12, 60m, 119.99m)
        };

        foreach (var (productId, quantity, actualPrice, sellingPrice) in inventoryItems)
        {
            var inventoryItemDto =
                new AddInventoryItemDto(productId, quantity, actualPrice, sellingPrice, _defaultCurrency, 0);
            await inventoryService.AddItemAsync(
                inventoryItemDto,
                CancellationToken.None);
        }
    }
}