/* ===================================
   UTILITY CLASSES
   =================================== */

/* ===== LAYOUT UTILITIES ===== */

/* Display */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* Flexbox */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-evenly { justify-content: space-evenly !important; }

.items-start { align-items: flex-start !important; }
.items-center { align-items: center !important; }
.items-end { align-items: flex-end !important; }
.items-stretch { align-items: stretch !important; }
.items-baseline { align-items: baseline !important; }

.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-none { flex: none !important; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)) !important; }

.gap-1 { gap: var(--spacing-1) !important; }
.gap-2 { gap: var(--spacing-2) !important; }
.gap-3 { gap: var(--spacing-3) !important; }
.gap-4 { gap: var(--spacing-4) !important; }
.gap-6 { gap: var(--spacing-6) !important; }
.gap-8 { gap: var(--spacing-8) !important; }

/* Position */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* ===== SPACING UTILITIES ===== */

/* Margin */
.m-0 { margin: var(--spacing-0) !important; }
.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-5 { margin: var(--spacing-5) !important; }
.m-6 { margin: var(--spacing-6) !important; }
.m-8 { margin: var(--spacing-8) !important; }
.m-auto { margin: auto !important; }

.mx-0 { margin-left: var(--spacing-0) !important; margin-right: var(--spacing-0) !important; }
.mx-1 { margin-left: var(--spacing-1) !important; margin-right: var(--spacing-1) !important; }
.mx-2 { margin-left: var(--spacing-2) !important; margin-right: var(--spacing-2) !important; }
.mx-3 { margin-left: var(--spacing-3) !important; margin-right: var(--spacing-3) !important; }
.mx-4 { margin-left: var(--spacing-4) !important; margin-right: var(--spacing-4) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-top: var(--spacing-0) !important; margin-bottom: var(--spacing-0) !important; }
.my-1 { margin-top: var(--spacing-1) !important; margin-bottom: var(--spacing-1) !important; }
.my-2 { margin-top: var(--spacing-2) !important; margin-bottom: var(--spacing-2) !important; }
.my-3 { margin-top: var(--spacing-3) !important; margin-bottom: var(--spacing-3) !important; }
.my-4 { margin-top: var(--spacing-4) !important; margin-bottom: var(--spacing-4) !important; }

.mt-0 { margin-top: var(--spacing-0) !important; }
.mt-1 { margin-top: var(--spacing-1) !important; }
.mt-2 { margin-top: var(--spacing-2) !important; }
.mt-3 { margin-top: var(--spacing-3) !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mt-6 { margin-top: var(--spacing-6) !important; }
.mt-8 { margin-top: var(--spacing-8) !important; }

.mb-0 { margin-bottom: var(--spacing-0) !important; }
.mb-1 { margin-bottom: var(--spacing-1) !important; }
.mb-2 { margin-bottom: var(--spacing-2) !important; }
.mb-3 { margin-bottom: var(--spacing-3) !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }
.mb-6 { margin-bottom: var(--spacing-6) !important; }
.mb-8 { margin-bottom: var(--spacing-8) !important; }

/* Padding */
.p-0 { padding: var(--spacing-0) !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }
.p-6 { padding: var(--spacing-6) !important; }
.p-8 { padding: var(--spacing-8) !important; }

.px-0 { padding-left: var(--spacing-0) !important; padding-right: var(--spacing-0) !important; }
.px-1 { padding-left: var(--spacing-1) !important; padding-right: var(--spacing-1) !important; }
.px-2 { padding-left: var(--spacing-2) !important; padding-right: var(--spacing-2) !important; }
.px-3 { padding-left: var(--spacing-3) !important; padding-right: var(--spacing-3) !important; }
.px-4 { padding-left: var(--spacing-4) !important; padding-right: var(--spacing-4) !important; }
.px-6 { padding-left: var(--spacing-6) !important; padding-right: var(--spacing-6) !important; }

.py-0 { padding-top: var(--spacing-0) !important; padding-bottom: var(--spacing-0) !important; }
.py-1 { padding-top: var(--spacing-1) !important; padding-bottom: var(--spacing-1) !important; }
.py-2 { padding-top: var(--spacing-2) !important; padding-bottom: var(--spacing-2) !important; }
.py-3 { padding-top: var(--spacing-3) !important; padding-bottom: var(--spacing-3) !important; }
.py-4 { padding-top: var(--spacing-4) !important; padding-bottom: var(--spacing-4) !important; }
.py-6 { padding-top: var(--spacing-6) !important; padding-bottom: var(--spacing-6) !important; }

/* ===== TYPOGRAPHY UTILITIES ===== */

/* Font Sizes */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }
.text-4xl { font-size: var(--font-size-4xl) !important; }

/* Font Weights */
.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

/* Text Alignment */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* Text Colors */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-success { color: var(--status-success) !important; }
.text-warning { color: var(--status-warning) !important; }
.text-error { color: var(--status-error) !important; }
.text-info { color: var(--status-info) !important; }

/* ===== BACKGROUND UTILITIES ===== */
.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-surface { background-color: var(--bg-surface) !important; }
.bg-success { background-color: var(--status-success-bg) !important; }
.bg-warning { background-color: var(--status-warning-bg) !important; }
.bg-error { background-color: var(--status-error-bg) !important; }
.bg-info { background-color: var(--status-info-bg) !important; }

/* ===== BORDER UTILITIES ===== */
.border { border: 1px solid var(--border-primary) !important; }
.border-0 { border: 0 !important; }
.border-t { border-top: 1px solid var(--border-primary) !important; }
.border-b { border-bottom: 1px solid var(--border-primary) !important; }
.border-l { border-left: 1px solid var(--border-primary) !important; }
.border-r { border-right: 1px solid var(--border-primary) !important; }

.rounded { border-radius: var(--border-radius-base) !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-md { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-full { border-radius: var(--border-radius-full) !important; }

/* ===== SHADOW UTILITIES ===== */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-base) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-none { box-shadow: none !important; }

/* ===== WIDTH & HEIGHT UTILITIES ===== */
.w-full { width: 100% !important; }
.w-auto { width: auto !important; }
.h-full { height: 100% !important; }
.h-auto { height: auto !important; }
.min-h-screen { min-height: 100vh !important; }

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 767px) {
  .d-md-none { display: none !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
}

@media (min-width: 768px) {
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
  .d-sm-none { display: none !important; }
}
