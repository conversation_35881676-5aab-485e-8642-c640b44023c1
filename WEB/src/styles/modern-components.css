/* ===================================
   MODERN COMPONENTS - TELE-SHOP
   =================================== */

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-family: inherit;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: 1;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Button Variants */
.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.btn-primary:hover {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--bg-surface);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover {
  background-color: var(--bg-surface-hover);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background-color: var(--success-500);
  color: white;
  border-color: var(--success-500);
}

.btn-success:hover {
  background-color: var(--success-700);
  border-color: var(--success-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-warning {
  background-color: var(--warning-500);
  color: white;
  border-color: var(--warning-500);
}

.btn-warning:hover {
  background-color: var(--warning-700);
  border-color: var(--warning-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-error {
  background-color: var(--error-500);
  color: white;
  border-color: var(--error-500);
}

.btn-error:hover {
  background-color: var(--error-700);
  border-color: var(--error-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-base);
}

.btn-icon {
  padding: var(--space-3);
  aspect-ratio: 1;
}

/* ===== CARDS ===== */
.card {
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-surface);
}

.card-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.card-subtitle {
  margin: var(--space-1) 0 0 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

/* ===== FORMS ===== */
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.form-label.required::after {
  content: " *";
  color: var(--error-500);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-family: inherit;
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--bg-secondary);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-error {
  margin-top: var(--space-1);
  font-size: var(--text-xs);
  color: var(--error-500);
}

.form-help {
  margin-top: var(--space-1);
  font-size: var(--text-xs);
  color: var(--text-muted);
}

/* ===== TABLES ===== */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-primary);
  background-color: var(--bg-surface);
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.table th,
.table td {
  padding: var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.table th {
  background-color: var(--bg-secondary);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.table tbody tr:hover {
  background-color: var(--bg-surface-hover);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* ===== BADGES ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  line-height: 1;
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-primary {
  background-color: var(--primary-100);
  color: var(--primary-800);
}

.badge-secondary {
  background-color: var(--neutral-100);
  color: var(--neutral-800);
}

.badge-success {
  background-color: var(--success-50);
  color: var(--success-700);
}

.badge-warning {
  background-color: var(--warning-50);
  color: var(--warning-700);
}

.badge-error {
  background-color: var(--error-50);
  color: var(--error-700);
}

/* ===== ALERTS ===== */
.alert {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid;
  margin-bottom: var(--space-4);
}

.alert-success {
  background-color: var(--success-50);
  border-color: var(--success-200);
  color: var(--success-800);
}

.alert-warning {
  background-color: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-800);
}

.alert-error {
  background-color: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-800);
}

.alert-info {
  background-color: var(--info-50);
  border-color: var(--info-200);
  color: var(--info-800);
}

/* ===== LOADING STATES ===== */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-primary);
  border-radius: 50%;
  border-top-color: var(--primary-500);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.skeleton {
  background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.skeleton-text {
  height: 1em;
  margin-bottom: var(--space-2);
}

.skeleton-text:last-child {
  margin-bottom: 0;
  width: 60%;
}

/* ===== NAVIGATION ===== */
.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-weight: var(--font-medium);
}

.nav-link:hover {
  background-color: var(--bg-surface-hover);
  color: var(--text-primary);
  text-decoration: none;
}

.nav-link.active {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

[data-theme="dark"] .nav-link.active {
  background-color: var(--primary-900);
  color: var(--primary-300);
}

/* ===== UTILITIES ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }
