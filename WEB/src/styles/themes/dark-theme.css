/* ===================================
   DARK THEME
   =================================== */

.theme-dark {
  /* ===== BACKGROUND COLORS ===== */
  --bg-primary: var(--color-neutral-900);
  --bg-secondary: var(--color-neutral-800);
  --bg-tertiary: var(--color-neutral-700);
  --bg-inverse: var(--color-neutral-50);
  --bg-surface: var(--color-neutral-950);
  --bg-surface-hover: var(--color-neutral-800);
  --bg-surface-active: var(--color-neutral-700);
  --bg-overlay: rgba(0, 0, 0, 0.7);

  /* ===== TEXT COLORS ===== */
  --text-primary: var(--color-neutral-50);
  --text-secondary: var(--color-neutral-300);
  --text-tertiary: var(--color-neutral-400);
  --text-inverse: var(--color-neutral-900);
  --text-muted: var(--color-neutral-500);
  --text-disabled: var(--color-neutral-600);

  /* ===== BORDER COLORS ===== */
  --border-primary: var(--color-neutral-700);
  --border-secondary: var(--color-neutral-600);
  --border-focus: var(--color-primary-400);
  --border-error: var(--color-error-400);
  --border-success: var(--color-success-400);
  --border-warning: var(--color-warning-400);

  /* ===== INTERACTIVE COLORS ===== */
  --interactive-primary: var(--color-primary-500);
  --interactive-primary-hover: var(--color-primary-400);
  --interactive-primary-active: var(--color-primary-300);
  --interactive-primary-disabled: var(--color-neutral-600);

  --interactive-secondary: var(--color-neutral-800);
  --interactive-secondary-hover: var(--color-neutral-700);
  --interactive-secondary-active: var(--color-neutral-600);

  /* ===== STATUS COLORS ===== */
  --status-success: var(--color-success-400);
  --status-success-bg: rgba(34, 197, 94, 0.1);
  --status-success-border: var(--color-success-800);

  --status-warning: var(--color-warning-400);
  --status-warning-bg: rgba(245, 158, 11, 0.1);
  --status-warning-border: var(--color-warning-800);

  --status-error: var(--color-error-400);
  --status-error-bg: rgba(239, 68, 68, 0.1);
  --status-error-border: var(--color-error-800);

  --status-info: var(--color-info-400);
  --status-info-bg: rgba(6, 182, 212, 0.1);
  --status-info-border: var(--color-info-800);

  /* ===== COMPONENT OVERRIDES ===== */
  
  /* Navigation */
  --nav-bg: var(--color-neutral-950);
  --nav-border: var(--color-neutral-800);
  --nav-link-color: var(--color-neutral-300);
  --nav-link-hover: var(--color-neutral-50);
  --nav-link-active: var(--color-primary-400);

  /* Cards */
  --card-bg: var(--color-neutral-900);
  --card-border: var(--color-neutral-800);
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --card-hover-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);

  /* Forms */
  --form-bg: var(--color-neutral-900);
  --form-border: var(--color-neutral-700);
  --form-border-focus: var(--color-primary-400);
  --form-text: var(--color-neutral-50);
  --form-placeholder: var(--color-neutral-500);
  --form-disabled-bg: var(--color-neutral-800);
  --form-disabled-text: var(--color-neutral-600);

  /* Tables */
  --table-bg: var(--color-neutral-900);
  --table-border: var(--color-neutral-800);
  --table-header-bg: var(--color-neutral-800);
  --table-row-hover: var(--color-neutral-800);
  --table-row-selected: rgba(59, 130, 246, 0.1);

  /* Sidebar */
  --sidebar-bg: var(--color-neutral-950);
  --sidebar-border: var(--color-neutral-800);
  --sidebar-item-hover: var(--color-neutral-800);
  --sidebar-item-active: rgba(59, 130, 246, 0.1);

  /* Header */
  --header-bg: var(--color-neutral-950);
  --header-border: var(--color-neutral-800);
  --header-text: var(--color-neutral-50);

  /* Footer */
  --footer-bg: var(--color-neutral-900);
  --footer-text: var(--color-neutral-300);
  --footer-border: var(--color-neutral-800);

  /* Modals */
  --modal-bg: var(--color-neutral-900);
  --modal-backdrop: rgba(0, 0, 0, 0.7);
  --modal-border: var(--color-neutral-800);
  --modal-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.5);

  /* Tooltips */
  --tooltip-bg: var(--color-neutral-700);
  --tooltip-text: var(--color-neutral-50);
  --tooltip-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);

  /* Loading */
  --loading-bg: var(--color-neutral-800);
  --loading-shimmer: var(--color-neutral-700);

  /* Scrollbars */
  --scrollbar-track: var(--color-neutral-800);
  --scrollbar-thumb: var(--color-neutral-600);
  --scrollbar-thumb-hover: var(--color-neutral-500);
}
