/* ===================================
   THEME BASE - SEMANTIC COLOR MAPPING
   =================================== */

/* Base theme variables that will be overridden by specific themes */
:root {
  /* ===== SEMANTIC COLOR TOKENS ===== */
  
  /* Background Colors */
  --bg-primary: var(--color-neutral-50);
  --bg-secondary: var(--color-neutral-100);
  --bg-tertiary: var(--color-neutral-200);
  --bg-inverse: var(--color-neutral-900);
  --bg-surface: #ffffff;
  --bg-surface-hover: var(--color-neutral-50);
  --bg-surface-active: var(--color-neutral-100);
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors */
  --text-primary: var(--color-neutral-900);
  --text-secondary: var(--color-neutral-600);
  --text-tertiary: var(--color-neutral-500);
  --text-inverse: #ffffff;
  --text-muted: var(--color-neutral-400);
  --text-disabled: var(--color-neutral-300);

  /* Border Colors */
  --border-primary: var(--color-neutral-200);
  --border-secondary: var(--color-neutral-300);
  --border-focus: var(--color-primary-500);
  --border-error: var(--color-error-500);
  --border-success: var(--color-success-500);
  --border-warning: var(--color-warning-500);

  /* Interactive Colors */
  --interactive-primary: var(--color-primary-600);
  --interactive-primary-hover: var(--color-primary-700);
  --interactive-primary-active: var(--color-primary-800);
  --interactive-primary-disabled: var(--color-neutral-300);

  --interactive-secondary: var(--color-neutral-100);
  --interactive-secondary-hover: var(--color-neutral-200);
  --interactive-secondary-active: var(--color-neutral-300);

  /* Status Colors */
  --status-success: var(--color-success-500);
  --status-success-bg: var(--color-success-50);
  --status-success-border: var(--color-success-200);

  --status-warning: var(--color-warning-500);
  --status-warning-bg: var(--color-warning-50);
  --status-warning-border: var(--color-warning-200);

  --status-error: var(--color-error-500);
  --status-error-bg: var(--color-error-50);
  --status-error-border: var(--color-error-200);

  --status-info: var(--color-info-500);
  --status-info-bg: var(--color-info-50);
  --status-info-border: var(--color-info-200);

  /* ===== COMPONENT-SPECIFIC TOKENS ===== */
  
  /* Navigation */
  --nav-bg: var(--bg-surface);
  --nav-border: var(--border-primary);
  --nav-link-color: var(--text-secondary);
  --nav-link-hover: var(--text-primary);
  --nav-link-active: var(--interactive-primary);

  /* Cards */
  --card-bg: var(--bg-surface);
  --card-border: var(--border-primary);
  --card-shadow: var(--shadow-base);
  --card-hover-shadow: var(--shadow-md);

  /* Forms */
  --form-bg: var(--bg-surface);
  --form-border: var(--border-primary);
  --form-border-focus: var(--border-focus);
  --form-text: var(--text-primary);
  --form-placeholder: var(--text-muted);
  --form-disabled-bg: var(--bg-secondary);
  --form-disabled-text: var(--text-disabled);

  /* Buttons */
  --btn-primary-bg: var(--interactive-primary);
  --btn-primary-hover: var(--interactive-primary-hover);
  --btn-primary-active: var(--interactive-primary-active);
  --btn-primary-text: #ffffff;
  --btn-primary-disabled: var(--interactive-primary-disabled);

  --btn-secondary-bg: var(--interactive-secondary);
  --btn-secondary-hover: var(--interactive-secondary-hover);
  --btn-secondary-active: var(--interactive-secondary-active);
  --btn-secondary-text: var(--text-primary);
  --btn-secondary-border: var(--border-primary);

  /* Tables */
  --table-bg: var(--bg-surface);
  --table-border: var(--border-primary);
  --table-header-bg: var(--bg-secondary);
  --table-row-hover: var(--bg-surface-hover);
  --table-row-selected: var(--color-primary-50);

  /* Sidebar */
  --sidebar-bg: var(--bg-surface);
  --sidebar-border: var(--border-primary);
  --sidebar-item-hover: var(--bg-surface-hover);
  --sidebar-item-active: var(--color-primary-50);

  /* Header */
  --header-bg: var(--bg-surface);
  --header-border: var(--border-primary);
  --header-text: var(--text-primary);

  /* Footer */
  --footer-bg: var(--bg-secondary);
  --footer-text: var(--text-secondary);
  --footer-border: var(--border-primary);

  /* Modals */
  --modal-bg: var(--bg-surface);
  --modal-backdrop: var(--bg-overlay);
  --modal-border: var(--border-primary);
  --modal-shadow: var(--shadow-2xl);

  /* Tooltips */
  --tooltip-bg: var(--bg-inverse);
  --tooltip-text: var(--text-inverse);
  --tooltip-shadow: var(--shadow-lg);

  /* Loading */
  --loading-bg: var(--bg-secondary);
  --loading-shimmer: var(--bg-tertiary);

  /* Scrollbars */
  --scrollbar-track: var(--bg-secondary);
  --scrollbar-thumb: var(--border-secondary);
  --scrollbar-thumb-hover: var(--text-tertiary);
}
