/* ===================================
   COMPONENT BASE STYLES
   =================================== */

/* ===== BUTTON STYLES ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  font-family: var(--font-family-sans);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Button Variants */
.btn-primary {
  background-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
  border-color: var(--btn-primary-bg);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--btn-primary-hover);
  border-color: var(--btn-primary-hover);
}

.btn-primary:active:not(:disabled) {
  background-color: var(--btn-primary-active);
  border-color: var(--btn-primary-active);
}

.btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border-color: var(--btn-secondary-border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--btn-secondary-hover);
}

.btn-secondary:active:not(:disabled) {
  background-color: var(--btn-secondary-active);
}

.btn-outline {
  background-color: transparent;
  color: var(--interactive-primary);
  border-color: var(--interactive-primary);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--interactive-primary);
  color: var(--btn-primary-text);
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--bg-surface-hover);
  color: var(--text-primary);
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
}

.btn-icon {
  padding: var(--spacing-2);
  width: 2.5rem;
  height: 2.5rem;
}

/* ===== FORM STYLES ===== */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  font-family: var(--font-family-sans);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--form-text);
  background-color: var(--form-bg);
  border: 1px solid var(--form-border);
  border-radius: var(--border-radius-md);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--form-border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:disabled {
  background-color: var(--form-disabled-bg);
  color: var(--form-disabled-text);
  cursor: not-allowed;
}

.form-control::placeholder {
  color: var(--form-placeholder);
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-2) center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: var(--spacing-8);
}

.form-error {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--status-error);
}

.form-help {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* ===== CARD STYLES ===== */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--card-shadow);
  transition: box-shadow var(--transition-base);
}

.card:hover {
  box-shadow: var(--card-hover-shadow);
}

.card-header {
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--card-border);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--card-border);
  background-color: var(--bg-secondary);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

.card-title {
  margin: 0 0 var(--spacing-2) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.card-subtitle {
  margin: 0 0 var(--spacing-3) 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* ===== TABLE STYLES ===== */
.table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--table-bg);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: var(--spacing-3) var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--table-border);
}

.table th {
  background-color: var(--table-header-bg);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: var(--table-row-hover);
}

.table tbody tr.selected {
  background-color: var(--table-row-selected);
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* ===== ALERT STYLES ===== */
.alert {
  padding: var(--spacing-3) var(--spacing-4);
  margin-bottom: var(--spacing-4);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
}

.alert-success {
  color: var(--status-success);
  background-color: var(--status-success-bg);
  border-color: var(--status-success-border);
}

.alert-warning {
  color: var(--status-warning);
  background-color: var(--status-warning-bg);
  border-color: var(--status-warning-border);
}

.alert-error {
  color: var(--status-error);
  background-color: var(--status-error-bg);
  border-color: var(--status-error-border);
}

.alert-info {
  color: var(--status-info);
  background-color: var(--status-info-bg);
  border-color: var(--status-info-border);
}

/* ===== BADGE STYLES ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border-radius: var(--border-radius-full);
}

.badge-primary {
  color: var(--btn-primary-text);
  background-color: var(--interactive-primary);
}

.badge-secondary {
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
}

.badge-success {
  color: var(--status-success);
  background-color: var(--status-success-bg);
}

.badge-warning {
  color: var(--status-warning);
  background-color: var(--status-warning-bg);
}

.badge-error {
  color: var(--status-error);
  background-color: var(--status-error-bg);
}

/* ===== LOADING STYLES ===== */
.loading-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--border-primary);
  border-radius: 50%;
  border-top-color: var(--interactive-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-skeleton {
  background: linear-gradient(90deg, var(--loading-bg) 25%, var(--loading-shimmer) 50%, var(--loading-bg) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--border-radius-base);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
