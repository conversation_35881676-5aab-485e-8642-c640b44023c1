import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { 
  ViewSaleDto, 
  CreateSaleDto, 
  SaleItemDto, 
  ApplySaleDiscountDto, 
  CompleteSaleDto, 
  CancelSaleDto,
  DateRangeDto 
} from '../models';

@Injectable({
  providedIn: 'root'
})
export class SaleService {
  private readonly baseUrl = `${environment.apiUrl}/sales`;

  constructor(private http: HttpClient) { }

  getSales(): Observable<ViewSaleDto[]> {
    return this.http.get<ViewSaleDto[]>(this.baseUrl);
  }

  getSale(id: string): Observable<ViewSaleDto> {
    return this.http.get<ViewSaleDto>(`${this.baseUrl}/${id}`);
  }

  getSalesByDateRange(dateRange: DateRangeDto): Observable<ViewSaleDto[]> {
    const params = new HttpParams()
      .set('startDate', dateRange.startDate)
      .set('endDate', dateRange.endDate);
    return this.http.get<ViewSaleDto[]>(`${this.baseUrl}/date-range`, { params });
  }

  createSale(sale: CreateSaleDto): Observable<{ id: string }> {
    return this.http.post<{ id: string }>(this.baseUrl, sale);
  }

  addItemToSale(saleId: string, item: SaleItemDto): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/${saleId}/items`, item);
  }

  removeItemFromSale(saleId: string, productId: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${saleId}/items/${productId}`);
  }

  applySaleDiscount(discount: ApplySaleDiscountDto): Observable<void> {
    return this.http.patch<void>(`${this.baseUrl}/discount`, discount);
  }

  completeSale(completion: CompleteSaleDto): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/complete`, completion);
  }

  cancelSale(cancellation: CancelSaleDto): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/cancel`, cancellation);
  }

  getSalesByProduct(productId: number): Observable<ViewSaleDto[]> {
    return this.http.get<ViewSaleDto[]>(`${this.baseUrl}/product/${productId}`);
  }
}
