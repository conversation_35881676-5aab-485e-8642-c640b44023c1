import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';

export type Theme = 'light' | 'dark' | 'system';

@Injectable({
  providedIn: 'root'
})
export class ModernThemeService {
  private readonly THEME_KEY = 'tele-shop-theme';
  private currentThemeSubject = new BehaviorSubject<Theme>('system');
  private effectiveThemeSubject = new BehaviorSubject<'light' | 'dark'>('light');

  public currentTheme$ = this.currentThemeSubject.asObservable();
  public effectiveTheme$ = this.effectiveThemeSubject.asObservable();

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    if (isPlatformBrowser(this.platformId)) {
      this.initializeTheme();
      this.listenToSystemThemeChanges();
    }
  }

  private initializeTheme(): void {
    const savedTheme = this.getSavedTheme();
    this.setTheme(savedTheme);
  }

  private getSavedTheme(): Theme {
    try {
      const saved = localStorage.getItem(this.THEME_KEY) as Theme;
      return saved && ['light', 'dark', 'system'].includes(saved) ? saved : 'system';
    } catch {
      return 'system';
    }
  }

  private saveTheme(theme: Theme): void {
    try {
      localStorage.setItem(this.THEME_KEY, theme);
    } catch {
      // Handle localStorage errors silently
    }
  }

  public setTheme(theme: Theme): void {
    this.currentThemeSubject.next(theme);
    this.saveTheme(theme);
    this.applyTheme(theme);
  }

  private applyTheme(theme: Theme): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const effectiveTheme = this.getEffectiveTheme(theme);
    this.effectiveThemeSubject.next(effectiveTheme);

    // Apply theme to document
    document.documentElement.setAttribute('data-theme', effectiveTheme);
    
    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(effectiveTheme);
  }

  private getEffectiveTheme(theme: Theme): 'light' | 'dark' {
    if (theme === 'system') {
      return this.getSystemTheme();
    }
    return theme;
  }

  private getSystemTheme(): 'light' | 'dark' {
    if (!isPlatformBrowser(this.platformId)) return 'light';
    
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  private updateMetaThemeColor(theme: 'light' | 'dark'): void {
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    const color = theme === 'dark' ? '#1f2937' : '#ffffff';
    
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', color);
    } else {
      const meta = document.createElement('meta');
      meta.name = 'theme-color';
      meta.content = color;
      document.head.appendChild(meta);
    }
  }

  public listenToSystemThemeChanges(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      if (this.currentThemeSubject.value === 'system') {
        this.applyTheme('system');
      }
    };

    // Modern browsers
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange);
    }
  }

  public toggleTheme(): void {
    const currentTheme = this.currentThemeSubject.value;
    const effectiveTheme = this.effectiveThemeSubject.value;
    
    if (currentTheme === 'system') {
      // If system, switch to opposite of current effective theme
      this.setTheme(effectiveTheme === 'light' ? 'dark' : 'light');
    } else {
      // If manual theme, toggle between light and dark
      this.setTheme(currentTheme === 'light' ? 'dark' : 'light');
    }
  }

  public getCurrentTheme(): Theme {
    return this.currentThemeSubject.value;
  }

  public getEffectiveThemeValue(): 'light' | 'dark' {
    return this.effectiveThemeSubject.value;
  }

  public isSystemTheme(): boolean {
    return this.currentThemeSubject.value === 'system';
  }

  public isDarkMode(): boolean {
    return this.effectiveThemeSubject.value === 'dark';
  }

  public isLightMode(): boolean {
    return this.effectiveThemeSubject.value === 'light';
  }

  // Utility method to get theme-aware colors
  public getThemeColor(lightColor: string, darkColor: string): string {
    return this.isDarkMode() ? darkColor : lightColor;
  }

  // Method to preload theme before app initialization
  public static preloadTheme(): void {
    if (typeof window === 'undefined') return;

    try {
      const savedTheme = localStorage.getItem('tele-shop-theme') as Theme;
      const theme = savedTheme && ['light', 'dark', 'system'].includes(savedTheme) ? savedTheme : 'system';
      
      let effectiveTheme: 'light' | 'dark';
      if (theme === 'system') {
        effectiveTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      } else {
        effectiveTheme = theme;
      }

      document.documentElement.setAttribute('data-theme', effectiveTheme);
      
      // Prevent flash of unstyled content
      document.documentElement.style.setProperty('--theme-transition', 'none');
      
      // Re-enable transitions after a short delay
      setTimeout(() => {
        document.documentElement.style.removeProperty('--theme-transition');
      }, 100);
      
    } catch {
      // Fallback to light theme
      document.documentElement.setAttribute('data-theme', 'light');
    }
  }
}
