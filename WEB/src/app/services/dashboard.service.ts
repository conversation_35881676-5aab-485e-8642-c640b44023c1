import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin, map } from 'rxjs';
import { environment } from '../../environments/environment';
import { ViewProductDto, ViewSaleDto, ViewInventoryDto, ProductCategoryDto, SaleStatus, InventoryItemStatus } from '../models';

export interface DashboardStats {
  totalProducts: number;
  totalCategories: number;
  totalSales: number;
  totalRevenue: number;
  lowStockItems: number;
  pendingSales: number;
  outOfStockItems: number;
  completedSalesToday: number;
  averageOrderValue: number;
  topSellingProducts: TopSellingProduct[];
  recentSales: ViewSaleDto[];
  lowStockProducts: LowStockProduct[];
}

export interface TopSellingProduct {
  productId: number;
  productName: string;
  totalQuantitySold: number;
  totalRevenue: number;
}

export interface LowStockProduct {
  productId: number;
  productName: string;
  currentStock: number;
  threshold: number;
  status: string;
}

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  private readonly baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  getDashboardStats(): Observable<DashboardStats> {
    return forkJoin({
      products: this.http.get<ViewProductDto[]>(`${this.baseUrl}/products`),
      categories: this.http.get<ProductCategoryDto[]>(`${this.baseUrl}/categories`),
      sales: this.http.get<ViewSaleDto[]>(`${this.baseUrl}/sales`),
      inventory: this.http.get<ViewInventoryDto>(`${this.baseUrl}/inventory/current`),
      pendingSales: this.http.get<ViewSaleDto[]>(`${this.baseUrl}/sales/status/${SaleStatus.Pending}`)
    }).pipe(
      map(({ products, categories, sales, inventory, pendingSales }) => {
        const completedSales = sales.filter(s => s.status === SaleStatus.Completed);
        const today = new Date().toISOString().split('T')[0];
        const salesToday = completedSales.filter(s => s.createdAt.startsWith(today));
        
        const totalRevenue = completedSales.reduce((sum, sale) => sum + sale.finalAmount, 0);
        const averageOrderValue = completedSales.length > 0 ? totalRevenue / completedSales.length : 0;
        
        const lowStockItems = inventory.items.filter(item => 
          item.status === InventoryItemStatus.LowStock || item.availableQuantity <= item.threshold
        );
        
        const outOfStockItems = inventory.items.filter(item => 
          item.status === InventoryItemStatus.OutOfStock || item.availableQuantity === 0
        );

        // Calculate top selling products
        const productSales = new Map<number, { name: string, quantity: number, revenue: number }>();
        completedSales.forEach(sale => {
          sale.items.forEach(item => {
            const existing = productSales.get(item.productId) || { name: item.productName, quantity: 0, revenue: 0 };
            existing.quantity += item.quantity;
            existing.revenue += item.totalPrice;
            productSales.set(item.productId, existing);
          });
        });

        const topSellingProducts: TopSellingProduct[] = Array.from(productSales.entries())
          .map(([productId, data]) => ({
            productId,
            productName: data.name,
            totalQuantitySold: data.quantity,
            totalRevenue: data.revenue
          }))
          .sort((a, b) => b.totalQuantitySold - a.totalQuantitySold)
          .slice(0, 5);

        const lowStockProducts: LowStockProduct[] = lowStockItems.map(item => ({
          productId: item.productId,
          productName: item.productName,
          currentStock: item.availableQuantity,
          threshold: item.threshold,
          status: item.status
        }));

        const recentSales = sales
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 10);

        return {
          totalProducts: products.length,
          totalCategories: categories.length,
          totalSales: completedSales.length,
          totalRevenue,
          lowStockItems: lowStockItems.length,
          pendingSales: pendingSales.length,
          outOfStockItems: outOfStockItems.length,
          completedSalesToday: salesToday.length,
          averageOrderValue,
          topSellingProducts,
          recentSales,
          lowStockProducts
        };
      })
    );
  }

  getRecentActivity(): Observable<any[]> {
    return forkJoin({
      recentSales: this.http.get<ViewSaleDto[]>(`${this.baseUrl}/sales`),
      inventory: this.http.get<ViewInventoryDto>(`${this.baseUrl}/inventory/current`)
    }).pipe(
      map(({ recentSales, inventory }) => {
        const activities: any[] = [];
        
        // Add recent sales
        recentSales
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 5)
          .forEach(sale => {
            activities.push({
              type: 'sale',
              title: `Sale #${sale.id.substring(0, 8)}`,
              description: `${sale.items.length} items - $${sale.finalAmount.toFixed(2)}`,
              timestamp: sale.createdAt,
              status: sale.status,
              icon: '💰'
            });
          });

        // Add low stock alerts
        inventory.items
          .filter(item => item.availableQuantity <= item.threshold)
          .slice(0, 3)
          .forEach(item => {
            activities.push({
              type: 'alert',
              title: 'Low Stock Alert',
              description: `${item.productName} - ${item.availableQuantity} remaining`,
              timestamp: new Date().toISOString(),
              status: 'warning',
              icon: '⚠️'
            });
          });

        return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      })
    );
  }
}
