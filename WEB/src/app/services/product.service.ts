import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  ViewProductDto,
  CreateProductDto,
  UpdateProductDto,
  PatchProductDto
} from '../models';
import { ErrorHandlerService } from './error-handler.service';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private readonly baseUrl = `${environment.apiUrl}/products`;

  constructor(
    private http: HttpClient,
    private errorHandler: ErrorHandlerService
  ) { }

  getProducts(): Observable<ViewProductDto[]> {
    return this.http.get<ViewProductDto[]>(this.baseUrl)
      .pipe(catchError(this.errorHandler.handleError));
  }

  getProduct(id: number): Observable<ViewProductDto> {
    return this.http.get<ViewProductDto>(`${this.baseUrl}/${id}`)
      .pipe(catchError(this.errorHandler.handleError));
  }

  getProductsByCategory(categoryId: number): Observable<ViewProductDto[]> {
    return this.http.get<ViewProductDto[]>(`${this.baseUrl}/category/${categoryId}`)
      .pipe(catchError(this.errorHandler.handleError));
  }

  getProductsByTag(tagId: number): Observable<ViewProductDto[]> {
    return this.http.get<ViewProductDto[]>(`${this.baseUrl}/tag/${tagId}`)
      .pipe(catchError(this.errorHandler.handleError));
  }

  searchProducts(term: string): Observable<ViewProductDto[]> {
    const params = new HttpParams().set('term', term);
    return this.http.get<ViewProductDto[]>(`${this.baseUrl}/search`, { params })
      .pipe(catchError(this.errorHandler.handleError));
  }

  createProduct(product: CreateProductDto): Observable<ViewProductDto> {
    return this.http.post<ViewProductDto>(this.baseUrl, product)
      .pipe(catchError(this.errorHandler.handleError));
  }

  updateProduct(product: UpdateProductDto): Observable<ViewProductDto> {
    return this.http.put<ViewProductDto>(`${this.baseUrl}/${product.id}`, product)
      .pipe(catchError(this.errorHandler.handleError));
  }

  patchProduct(patch: PatchProductDto): Observable<ViewProductDto> {
    return this.http.patch<ViewProductDto>(this.baseUrl, patch)
      .pipe(catchError(this.errorHandler.handleError));
  }

  deleteProduct(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`)
      .pipe(catchError(this.errorHandler.handleError));
  }
}
