import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { TagDto, PatchTagDto } from '../models';

@Injectable({
  providedIn: 'root'
})
export class TagService {
  private readonly baseUrl = `${environment.apiUrl}/tags`;

  constructor(private http: HttpClient) { }

  getTags(): Observable<TagDto[]> {
    return this.http.get<TagDto[]>(this.baseUrl);
  }

  getTag(id: number): Observable<TagDto> {
    return this.http.get<TagDto>(`${this.baseUrl}/${id}`);
  }

  getTagBySlug(slug: string): Observable<TagDto> {
    return this.http.get<TagDto>(`${this.baseUrl}/slug/${slug}`);
  }

  getTagsByProduct(productId: number): Observable<TagDto[]> {
    return this.http.get<TagDto[]>(`${this.baseUrl}/product/${productId}`);
  }

  createTag(name: string): Observable<TagDto> {
    return this.http.post<TagDto>(this.baseUrl, name, {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  patchTag(patch: PatchTagDto): Observable<TagDto> {
    return this.http.patch<TagDto>(this.baseUrl, patch);
  }

  deleteTag(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`);
  }
}
