import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { ErrorResponse } from '../models';

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService {

  handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      if (error.error && typeof error.error === 'object') {
        const apiError = error.error as ErrorResponse;
        errorMessage = apiError.message || `Server Error: ${error.status} ${error.statusText}`;
      } else {
        errorMessage = `Server Error: ${error.status} ${error.statusText}`;
      }
    }

    console.error('HTTP Error:', error);
    return throwError(() => new Error(errorMessage));
  }

  logError(error: any, context?: string): void {
    const timestamp = new Date().toISOString();
    const contextInfo = context ? ` [${context}]` : '';
    console.error(`${timestamp}${contextInfo}:`, error);
  }
}
