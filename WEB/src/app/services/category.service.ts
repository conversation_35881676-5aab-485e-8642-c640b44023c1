import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { ProductCategoryDto, PatchProductCategoryDto } from '../models';

@Injectable({
  providedIn: 'root'
})
export class CategoryService {
  private readonly baseUrl = `${environment.apiUrl}/categories`;

  constructor(private http: HttpClient) { }

  getCategories(): Observable<ProductCategoryDto[]> {
    return this.http.get<ProductCategoryDto[]>(this.baseUrl);
  }

  getCategory(id: number): Observable<ProductCategoryDto> {
    return this.http.get<ProductCategoryDto>(`${this.baseUrl}/${id}`);
  }

  createCategory(name: string): Observable<ProductCategoryDto> {
    return this.http.post<ProductCategoryDto>(this.baseUrl, name, {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  patchCategory(patch: PatchProductCategoryDto): Observable<ProductCategoryDto> {
    return this.http.patch<ProductCategoryDto>(this.baseUrl, patch);
  }

  deleteCategory(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`);
  }
}
