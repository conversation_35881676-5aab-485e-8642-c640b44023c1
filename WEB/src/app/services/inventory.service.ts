import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { 
  ViewInventoryDto, 
  CreateInventoryDto, 
  AddInventoryItemDto, 
  WasteInventoryItemDto,
  UpdateInventoryItemPriceDto 
} from '../models';

@Injectable({
  providedIn: 'root'
})
export class InventoryService {
  private readonly baseUrl = `${environment.apiUrl}/inventory`;

  constructor(private http: HttpClient) { }

  getCurrentInventory(): Observable<ViewInventoryDto> {
    return this.http.get<ViewInventoryDto>(`${this.baseUrl}/current`);
  }

  createInventory(inventory: CreateInventoryDto): Observable<{ id: string }> {
    return this.http.post<{ id: string }>(this.baseUrl, inventory);
  }

  addItemToInventory(item: AddInventoryItemDto): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/items`, item);
  }

  wasteInventoryItem(waste: WasteInventoryItemDto): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/items/waste`, waste);
  }

  updateInventoryItemPrice(priceUpdate: UpdateInventoryItemPriceDto): Observable<void> {
    return this.http.patch<void>(`${this.baseUrl}/items/price`, priceUpdate);
  }
}
