import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  ViewProductDto,
  CreateProductDto,
  UpdateProductDto,
  PatchProductDto,
  PagedResult,
  QueryParams
} from '../models';
import { ErrorHandlerService } from './error-handler.service';
import { BaseApiService } from '../core/services/base-api.service';

export interface ProductFilters {
  categoryId?: number;
  tagIds?: number[];
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  status?: string;
}

export interface BulkUpdateDto {
  id: number;
  updates: Partial<UpdateProductDto>;
}

@Injectable({
  providedIn: 'root'
})
export class EnhancedProductService extends BaseApiService<ViewProductDto, CreateProductDto, UpdateProductDto> {
  protected endpoint = 'products';

  constructor(
    http: HttpClient,
    errorHandler: ErrorHandlerService
  ) { 
    super(http, errorHandler);
  }

  getByCategory(categoryId: number, params?: QueryParams): Observable<PagedResult<ViewProductDto>> {
    const categoryParams = { ...params, filters: { ...params?.filters, categoryId } };
    return this.getAll(categoryParams);
  }

  getByTag(tagId: number, params?: QueryParams): Observable<PagedResult<ViewProductDto>> {
    const tagParams = { ...params, filters: { ...params?.filters, tagId } };
    return this.getAll(tagParams);
  }

  getLowStock(threshold?: number): Observable<ViewProductDto[]> {
    const params = threshold ? new HttpParams().set('threshold', threshold.toString()) : undefined;
    return this.http.get<ViewProductDto[]>(this.getFullUrl('low-stock'), { params })
      .pipe(catchError(this.errorHandler.handleError));
  }

  bulkUpdate(updates: BulkUpdateDto[]): Observable<void> {
    return this.http.patch<void>(this.getFullUrl('bulk'), updates)
      .pipe(catchError(this.errorHandler.handleError));
  }

  export(format: 'csv' | 'excel', filters?: QueryParams): Observable<Blob> {
    const params = this.buildHttpParams(filters).set('format', format);
    return this.http.get(this.getFullUrl('export'), { 
      params, 
      responseType: 'blob' 
    }).pipe(catchError(this.errorHandler.handleError));
  }

  getProductsByCategory(categoryId: number): Observable<ViewProductDto[]> {
    return this.http.get<ViewProductDto[]>(this.getFullUrl(`category/${categoryId}`))
      .pipe(catchError(this.errorHandler.handleError));
  }

  getProductsByTag(tagId: number): Observable<ViewProductDto[]> {
    return this.http.get<ViewProductDto[]>(this.getFullUrl(`tag/${tagId}`))
      .pipe(catchError(this.errorHandler.handleError));
  }

  searchProducts(term: string): Observable<ViewProductDto[]> {
    const params = new HttpParams().set('term', term);
    return this.http.get<ViewProductDto[]>(this.getFullUrl('search'), { params })
      .pipe(catchError(this.errorHandler.handleError));
  }

  patchProduct(patch: PatchProductDto): Observable<ViewProductDto> {
    return this.http.patch<ViewProductDto>(this.getFullUrl(), patch)
      .pipe(catchError(this.errorHandler.handleError));
  }
}
