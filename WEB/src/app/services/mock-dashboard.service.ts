import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';

export interface DashboardStats {
  totalProducts: number;
  totalCategories: number;
  totalSales: number;
  totalRevenue: number;
  lowStockItems: number;
  pendingSales: number;
  outOfStockItems: number;
  completedSalesToday: number;
  averageOrderValue: number;
  topSellingProducts: TopSellingProduct[];
  recentSales: any[];
  lowStockProducts: LowStockProduct[];
}

export interface TopSellingProduct {
  productId: number;
  productName: string;
  totalQuantitySold: number;
  totalRevenue: number;
}

export interface LowStockProduct {
  productId: number;
  productName: string;
  currentStock: number;
  threshold: number;
  status: string;
}

@Injectable({
  providedIn: 'root'
})
export class MockDashboardService {
  private mockStats: DashboardStats = {
    totalProducts: 156,
    totalCategories: 12,
    totalSales: 89,
    totalRevenue: 15420.75,
    lowStockItems: 8,
    pendingSales: 3,
    outOfStockItems: 2,
    completedSalesToday: 12,
    averageOrderValue: 173.26,
    topSellingProducts: [
      {
        productId: 1,
        productName: 'Wireless Mouse',
        totalQuantitySold: 45,
        totalRevenue: 1350.00
      },
      {
        productId: 2,
        productName: 'Laptop Stand',
        totalQuantitySold: 32,
        totalRevenue: 1600.00
      },
      {
        productId: 3,
        productName: 'USB Cable',
        totalQuantitySold: 78,
        totalRevenue: 780.00
      }
    ],
    recentSales: [
      {
        id: 'sale-001',
        items: [{ productName: 'Wireless Mouse', quantity: 2 }],
        finalAmount: 60.00,
        status: 'Completed',
        createdAt: new Date(Date.now() - 1000 * 60 * 5).toISOString()
      },
      {
        id: 'sale-002',
        items: [{ productName: 'Laptop Stand', quantity: 1 }],
        finalAmount: 50.00,
        status: 'Completed',
        createdAt: new Date(Date.now() - 1000 * 60 * 15).toISOString()
      }
    ],
    lowStockProducts: [
      {
        productId: 4,
        productName: 'Wireless Keyboard',
        currentStock: 3,
        threshold: 10,
        status: 'LowStock'
      },
      {
        productId: 5,
        productName: 'Monitor Cable',
        currentStock: 1,
        threshold: 5,
        status: 'LowStock'
      }
    ]
  };

  private mockActivity = [
    {
      type: 'sale',
      title: 'Sale #12345678',
      description: '3 items - $125.50',
      timestamp: new Date(Date.now() - 1000 * 60 * 2).toISOString(),
      status: 'completed',
      icon: '💰'
    },
    {
      type: 'product',
      title: 'Product Added',
      description: 'Laptop Stand added to inventory',
      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
      status: 'success',
      icon: '📦'
    },
    {
      type: 'alert',
      title: 'Low Stock Alert',
      description: 'Wireless Mouse - 3 remaining',
      timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
      status: 'warning',
      icon: '⚠️'
    },
    {
      type: 'category',
      title: 'Category Updated',
      description: 'Electronics category modified',
      timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
      status: 'info',
      icon: '📂'
    }
  ];

  getDashboardStats(): Observable<DashboardStats> {
    // Simulate API delay
    return of(this.mockStats).pipe(delay(500));
  }

  getRecentActivity(): Observable<any[]> {
    // Simulate API delay
    return of(this.mockActivity).pipe(delay(300));
  }
}
