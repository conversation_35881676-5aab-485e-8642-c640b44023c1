import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';

export type Theme = 'light' | 'dark' | 'system';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'tele-shop-theme';
  private readonly themeSubject = new BehaviorSubject<Theme>('light');
  
  public theme$: Observable<Theme> = this.themeSubject.asObservable();

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    this.initializeTheme();
  }

  private initializeTheme(): void {
    if (isPlatformBrowser(this.platformId)) {
      const savedTheme = this.getSavedTheme();
      const systemTheme = this.getSystemTheme();
      
      if (savedTheme) {
        this.setTheme(savedTheme);
      } else if (systemTheme) {
        this.setTheme('system');
      } else {
        this.setTheme('light');
      }
    }
  }

  public setTheme(theme: Theme): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    this.themeSubject.next(theme);
    
    // Remove existing theme classes
    document.documentElement.classList.remove('theme-light', 'theme-dark');
    
    // Apply new theme
    if (theme === 'system') {
      const systemTheme = this.getSystemTheme();
      document.documentElement.classList.add(`theme-${systemTheme}`);
    } else {
      document.documentElement.classList.add(`theme-${theme}`);
    }
    
    // Save to localStorage
    localStorage.setItem(this.THEME_KEY, theme);
  }

  public getCurrentTheme(): Theme {
    return this.themeSubject.value;
  }

  public toggleTheme(): void {
    const currentTheme = this.getCurrentTheme();
    const newTheme: Theme = currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  private getSavedTheme(): Theme | null {
    if (!isPlatformBrowser(this.platformId)) {
      return null;
    }
    
    const saved = localStorage.getItem(this.THEME_KEY);
    return (saved === 'light' || saved === 'dark' || saved === 'system') ? saved : null;
  }

  private getSystemTheme(): 'light' | 'dark' {
    if (!isPlatformBrowser(this.platformId)) {
      return 'light';
    }
    
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  public listenToSystemThemeChanges(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (this.getCurrentTheme() === 'system') {
        document.documentElement.classList.remove('theme-light', 'theme-dark');
        document.documentElement.classList.add(`theme-${e.matches ? 'dark' : 'light'}`);
      }
    });
  }
}
