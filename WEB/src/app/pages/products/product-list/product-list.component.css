/* Placeholder Page Styles */
.page-container {
  min-height: calc(100vh - 200px);
  padding: var(--spacing-6) 0;
}

.page-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.page-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin: 0;
}

.placeholder-content {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  padding: var(--spacing-12);
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-6);
}

.placeholder-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.placeholder-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-6) 0;
  line-height: var(--line-height-relaxed);
}

.placeholder-features {
  text-align: left;
  margin: 0 0 var(--spacing-8) 0;
  padding: 0;
  list-style: none;
}

.placeholder-features li {
  padding: var(--spacing-2) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--spacing-6);
}

.placeholder-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--status-success);
  font-weight: var(--font-weight-bold);
}

.placeholder-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.placeholder-actions .btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Responsive Design */
@media (max-width: 767px) {
  .placeholder-content {
    padding: var(--spacing-8);
    margin: 0 var(--spacing-4);
  }
  
  .placeholder-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .placeholder-actions .btn {
    width: 100%;
    max-width: 200px;
  }
}
