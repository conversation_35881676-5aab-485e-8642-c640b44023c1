.product-list-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary, #333);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color, #007bff);
  color: white;
  border-color: var(--primary-color, #007bff);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover, #0056b3);
  border-color: var(--primary-hover, #0056b3);
}

.btn-secondary {
  background: var(--surface-color, #fff);
  color: var(--text-primary, #333);
  border-color: var(--border-color, #ddd);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--surface-hover, #f5f5f5);
}

.btn-icon {
  font-size: 16px;
}

/* Filters Section */
.filters-section {
  background: var(--surface-color, #fff);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.filters-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
  flex-wrap: wrap;
}

.search-container {
  flex: 1;
  min-width: 300px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 150px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #333);
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  font-size: 14px;
  background: var(--surface-color, #fff);
  color: var(--text-primary, #333);
}

.tags-filter {
  max-width: 300px;
}

.tag-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 100px;
  overflow-y: auto;
}

.tag-checkbox {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

.tag-checkbox input[type="checkbox"] {
  margin: 0;
}

.tag-label {
  color: var(--text-primary, #333);
}

/* Active Filters */
.active-filters {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light, #f0f0f0);
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.active-filters-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary, #666);
}

.filter-chip {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: var(--primary-light, #e3f2fd);
  color: var(--primary-color, #007bff);
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.filter-chip-remove {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  padding: 0;
  margin-left: 4px;
}

.clear-all-filters {
  background: none;
  border: none;
  color: var(--danger-color, #dc3545);
  cursor: pointer;
  font-size: 12px;
  text-decoration: underline;
  padding: 4px 8px;
}

/* Results Summary */
.results-summary {
  margin-bottom: 16px;
  padding: 12px 0;
}

.results-count,
.loading-text {
  font-size: 14px;
  color: var(--text-secondary, #666);
}

/* Error Message */
.error-message {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--error-light, #f8d7da);
  border: 1px solid var(--error-border, #f5c6cb);
  border-radius: 6px;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-icon {
  font-size: 20px;
}

.error-text {
  flex: 1;
  color: var(--error-color, #721c24);
  font-size: 14px;
}

.error-retry {
  background: var(--error-color, #dc3545);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

/* Table Container */
.table-container {
  margin-bottom: 20px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary, #333);
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 16px;
  color: var(--text-secondary, #666);
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-list-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .filters-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-container {
    min-width: auto;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .tags-filter {
    max-width: none;
  }
  
  .tag-checkboxes {
    max-height: 80px;
  }
  
  .active-filters {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .page-title {
    font-size: 24px;
    text-align: center;
  }
  
  .empty-icon {
    font-size: 48px;
  }
  
  .empty-title {
    font-size: 20px;
  }
  
  .empty-description {
    font-size: 14px;
  }
}
