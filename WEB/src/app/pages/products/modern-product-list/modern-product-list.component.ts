import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';

interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
  status: 'active' | 'inactive' | 'out-of-stock';
  image?: string;
  createdAt: Date;
  updatedAt: Date;
}

@Component({
  selector: 'app-modern-product-list',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './modern-product-list.component.html',
  styleUrl: './modern-product-list.component.css'
})
export class ModernProductListComponent implements OnInit {
  products: Product[] = [];
  filteredProducts: Product[] = [];
  loading = true;
  searchTerm = '';
  selectedCategory = '';
  selectedStatus = '';
  sortBy = 'name';
  sortDirection: 'asc' | 'desc' = 'asc';
  
  categories = ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports', 'Toys'];
  statuses = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'out-of-stock', label: 'Out of Stock' }
  ];

  currentPage = 1;
  itemsPerPage = 12;
  totalItems = 0;

  ngOnInit(): void {
    this.loadProducts();
  }

  private async loadProducts(): Promise<void> {
    try {
      await this.delay(1000);
      
      this.products = this.generateMockProducts();
      this.totalItems = this.products.length;
      this.applyFilters();
      this.loading = false;
    } catch (error) {
      console.error('Error loading products:', error);
      this.loading = false;
    }
  }

  private generateMockProducts(): Product[] {
    const mockProducts: Product[] = [];
    const productNames = [
      'Wireless Bluetooth Headphones',
      'Smart Watch Series 5',
      'Gaming Mechanical Keyboard',
      'Portable Power Bank',
      'Wireless Mouse',
      'USB-C Hub',
      'Laptop Stand',
      'Phone Case',
      'Tablet Screen Protector',
      'Bluetooth Speaker',
      'Fitness Tracker',
      'Wireless Charger'
    ];

    for (let i = 0; i < 50; i++) {
      const name = productNames[i % productNames.length];
      const category = this.categories[Math.floor(Math.random() * this.categories.length)];
      const price = Math.floor(Math.random() * 500) + 10;
      const stock = Math.floor(Math.random() * 100);
      const status: Product['status'] = stock === 0 ? 'out-of-stock' : 
                                       Math.random() > 0.1 ? 'active' : 'inactive';

      mockProducts.push({
        id: i + 1,
        name: `${name} ${i + 1}`,
        description: `High-quality ${name.toLowerCase()} with premium features and excellent performance.`,
        price,
        category,
        stock,
        status,
        image: `https://picsum.photos/300/300?random=${i + 1}`,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
      });
    }

    return mockProducts;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  onSearch(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onCategoryChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onStatusChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onSort(field: string): void {
    if (this.sortBy === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortDirection = 'asc';
    }
    this.applyFilters();
  }

  private applyFilters(): void {
    let filtered = [...this.products];

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(term) ||
        product.description.toLowerCase().includes(term) ||
        product.category.toLowerCase().includes(term)
      );
    }

    if (this.selectedCategory) {
      filtered = filtered.filter(product => product.category === this.selectedCategory);
    }

    if (this.selectedStatus) {
      filtered = filtered.filter(product => product.status === this.selectedStatus);
    }

    filtered.sort((a, b) => {
      let aValue: any = a[this.sortBy as keyof Product];
      let bValue: any = b[this.sortBy as keyof Product];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return this.sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return this.sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    this.filteredProducts = filtered;
    this.totalItems = filtered.length;
  }

  get paginatedProducts(): Product[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredProducts.slice(startIndex, endIndex);
  }

  get totalPages(): number {
    return Math.ceil(this.totalItems / this.itemsPerPage);
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  getStatusBadgeClass(status: string): string {
    const classes: { [key: string]: string } = {
      'active': 'badge-success',
      'inactive': 'badge-secondary',
      'out-of-stock': 'badge-error'
    };
    return classes[status] || 'badge-secondary';
  }

  getStockStatusClass(stock: number): string {
    if (stock === 0) return 'text-error';
    if (stock < 10) return 'text-warning';
    return 'text-success';
  }

  onRefresh(): void {
    this.loading = true;
    this.loadProducts();
  }

  onClearFilters(): void {
    this.searchTerm = '';
    this.selectedCategory = '';
    this.selectedStatus = '';
    this.currentPage = 1;
    this.applyFilters();
  }

  onDeleteProduct(product: Product): void {
    if (confirm(`Are you sure you want to delete "${product.name}"?`)) {
      console.log('Deleting product:', product.id);
    }
  }

  trackByProductId(index: number, product: Product): number {
    return product.id;
  }
}
