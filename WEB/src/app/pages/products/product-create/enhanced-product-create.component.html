<div class="product-create-container">
  
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <div class="breadcrumb">
        <a routerLink="/products" class="breadcrumb-link">Products</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-current">Create</span>
      </div>
      
      <h1 class="page-title">{{ pageTitle }}</h1>
      <p class="page-description">{{ pageDescription }}</p>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading()" class="loading-container">
    <app-loading-spinner></app-loading-spinner>
    <p class="loading-text">Loading form data...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error()" class="error-container">
    <div class="error-content">
      <div class="error-icon">⚠️</div>
      <div class="error-details">
        <h3 class="error-title">Unable to Load Form</h3>
        <p class="error-message">{{ error() }}</p>
        <button (click)="onRetry()" class="btn btn-primary">
          Try Again
        </button>
      </div>
    </div>
  </div>

  <!-- Form Container -->
  <div *ngIf="canShowForm" class="form-container">
    
    <!-- Global Error Message -->
    <div *ngIf="error() && !loading()" class="global-error">
      <div class="error-content">
        <span class="error-icon">❌</span>
        <span class="error-text">{{ error() }}</span>
        <button (click)="error.set(null)" class="error-dismiss">×</button>
      </div>
    </div>

    <!-- Product Form -->
    <app-product-form
      [config]="formConfig()"
      [loading]="submitting()"
      [disabled]="submitting()"
      (formSubmit)="onFormSubmit($event)"
      (formCancel)="onFormCancel()"
      (formChange)="onFormChange($event)">
    </app-product-form>

    <!-- Help Section -->
    <div class="help-section">
      <h3 class="help-title">💡 Tips for Creating Products</h3>
      <ul class="help-list">
        <li class="help-item">
          <strong>Product Name:</strong> Use a clear, descriptive name that customers will easily understand.
        </li>
        <li class="help-item">
          <strong>Description:</strong> Provide detailed information about features, benefits, and specifications.
        </li>
        <li class="help-item">
          <strong>Category:</strong> Choose the most appropriate category to help with organization and filtering.
        </li>
        <li class="help-item">
          <strong>Tags:</strong> Add relevant tags to improve searchability and product discovery.
        </li>
      </ul>
    </div>

  </div>

  <!-- Quick Actions -->
  <div *ngIf="canShowForm" class="quick-actions">
    <div class="quick-actions-header">
      <h4 class="quick-actions-title">Quick Actions</h4>
    </div>
    <div class="quick-actions-grid">
      <a routerLink="/categories/create" class="quick-action-card">
        <div class="quick-action-icon">📁</div>
        <div class="quick-action-content">
          <h5 class="quick-action-title">Create Category</h5>
          <p class="quick-action-description">Add a new product category</p>
        </div>
      </a>
      
      <a routerLink="/tags/create" class="quick-action-card">
        <div class="quick-action-icon">🏷️</div>
        <div class="quick-action-content">
          <h5 class="quick-action-title">Create Tag</h5>
          <p class="quick-action-description">Add a new product tag</p>
        </div>
      </a>
      
      <a routerLink="/products" class="quick-action-card">
        <div class="quick-action-icon">📋</div>
        <div class="quick-action-content">
          <h5 class="quick-action-title">View All Products</h5>
          <p class="quick-action-description">Browse existing products</p>
        </div>
      </a>
    </div>
  </div>

</div>
