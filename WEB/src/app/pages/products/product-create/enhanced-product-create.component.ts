import { Component, OnInit, OnDestroy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject, takeUntil, forkJoin } from 'rxjs';

import { ProductFormComponent, ProductFormConfig, ProductFormData } from '../../../shared/components/product-form/product-form.component';
import { LoadingSpinnerComponent } from '../../../components/ui/loading-spinner/loading-spinner.component';

import { EnhancedProductService } from '../../../services/enhanced-product.service';
import { CategoryService } from '../../../services/category.service';
import { TagService } from '../../../services/tag.service';

import { ProductCategoryDto, TagDto, CreateProductDto } from '../../../models';

@Component({
  selector: 'app-enhanced-product-create',
  standalone: true,
  imports: [CommonModule, ProductFormComponent, LoadingSpinnerComponent],
  templateUrl: './enhanced-product-create.component.html',
  styleUrl: './enhanced-product-create.component.css'
})
export class EnhancedProductCreateComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // State signals
  loading = signal(false);
  submitting = signal(false);
  error = signal<string | null>(null);
  categories = signal<ProductCategoryDto[]>([]);
  tags = signal<TagDto[]>([]);
  dataLoaded = signal(false);

  // Form configuration
  formConfig = signal<ProductFormConfig>({
    mode: 'create',
    categories: [],
    tags: [],
    showAdvanced: false
  });

  constructor(
    private productService: EnhancedProductService,
    private categoryService: CategoryService,
    private tagService: TagService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadFormData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadFormData(): void {
    this.loading.set(true);
    this.error.set(null);

    forkJoin({
      categories: this.categoryService.getCategories(),
      tags: this.tagService.getTags()
    })
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: ({ categories, tags }) => {
        this.categories.set(categories);
        this.tags.set(tags);
        
        this.formConfig.set({
          mode: 'create',
          categories: categories,
          tags: tags,
          showAdvanced: false
        });
        
        this.dataLoaded.set(true);
        this.loading.set(false);
      },
      error: (error) => {
        this.error.set('Failed to load form data. Please try again.');
        this.loading.set(false);
        console.error('Error loading form data:', error);
      }
    });
  }

  onFormSubmit(formData: ProductFormData): void {
    this.submitting.set(true);
    this.error.set(null);

    const createDto: CreateProductDto = {
      name: formData.name,
      description: formData.description,
      categoryId: formData.categoryId,
      tagIds: formData.tagIds
    };

    this.productService.create(createDto)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (product) => {
          this.submitting.set(false);
          this.showSuccessMessage(`Product "${product.name}" created successfully!`);
          this.router.navigate(['/products', product.id, 'view']);
        },
        error: (error) => {
          this.submitting.set(false);
          this.error.set(this.getErrorMessage(error));
          console.error('Error creating product:', error);
        }
      });
  }

  onFormCancel(): void {
    this.router.navigate(['/products']);
  }

  onFormChange(formData: ProductFormData): void {
    // Handle form changes if needed (e.g., auto-save)
    console.log('Form changed:', formData);
  }

  onRetry(): void {
    this.loadFormData();
  }

  private getErrorMessage(error: any): string {
    if (error?.error?.message) {
      return error.error.message;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    if (error?.status === 409) {
      return 'A product with this name already exists.';
    }
    
    if (error?.status === 400) {
      return 'Invalid product data. Please check your input.';
    }
    
    if (error?.status === 403) {
      return 'You do not have permission to create products.';
    }
    
    return 'Failed to create product. Please try again.';
  }

  private showSuccessMessage(message: string): void {
    // In a real app, you might use a toast service or notification system
    console.log('Success:', message);
    
    // For now, we'll use a simple alert
    // In production, replace with proper notification system
    setTimeout(() => {
      alert(message);
    }, 100);
  }

  get isLoading(): boolean {
    return this.loading() || this.submitting();
  }

  get canShowForm(): boolean {
    return this.dataLoaded() && !this.loading() && !this.error();
  }

  get pageTitle(): string {
    return 'Create New Product';
  }

  get pageDescription(): string {
    return 'Add a new product to your inventory. Fill in the required information below.';
  }
}
