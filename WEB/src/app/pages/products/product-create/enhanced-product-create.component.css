.product-create-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
}

/* Page Header */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  text-align: center;
}

.breadcrumb {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 14px;
}

.breadcrumb-link {
  color: var(--primary-color, #007bff);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover {
  color: var(--primary-hover, #0056b3);
  text-decoration: underline;
}

.breadcrumb-separator {
  color: var(--text-secondary, #666);
}

.breadcrumb-current {
  color: var(--text-primary, #333);
  font-weight: 500;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  color: var(--text-primary, #333);
  margin: 0 0 12px 0;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary, #666);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: var(--text-secondary, #666);
}

/* Error State */
.error-container {
  display: flex;
  justify-content: center;
  padding: 40px 20px;
}

.error-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  max-width: 500px;
  padding: 24px;
  background: var(--error-light, #f8d7da);
  border: 1px solid var(--error-border, #f5c6cb);
  border-radius: 8px;
}

.error-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.error-details {
  flex: 1;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--error-color, #721c24);
  margin: 0 0 8px 0;
}

.error-message {
  font-size: 14px;
  color: var(--error-color, #721c24);
  margin: 0 0 16px 0;
  line-height: 1.4;
}

/* Form Container */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Global Error */
.global-error {
  margin-bottom: 20px;
}

.global-error .error-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--error-light, #f8d7da);
  border: 1px solid var(--error-border, #f5c6cb);
  border-radius: 6px;
  margin: 0;
  max-width: none;
}

.global-error .error-icon {
  font-size: 16px;
}

.global-error .error-text {
  flex: 1;
  font-size: 14px;
  color: var(--error-color, #721c24);
}

.error-dismiss {
  background: none;
  border: none;
  color: var(--error-color, #721c24);
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Help Section */
.help-section {
  background: var(--surface-variant, #f8f9fa);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  padding: 24px;
}

.help-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333);
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.help-item {
  font-size: 14px;
  color: var(--text-primary, #333);
  line-height: 1.5;
}

.help-item strong {
  color: var(--primary-color, #007bff);
}

/* Quick Actions */
.quick-actions {
  background: var(--surface-color, #ffffff);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  padding: 24px;
}

.quick-actions-header {
  margin-bottom: 20px;
}

.quick-actions-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333);
  margin: 0;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.quick-action-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--surface-variant, #f8f9fa);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 6px;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
}

.quick-action-card:hover {
  background: var(--surface-hover, #e9ecef);
  border-color: var(--primary-color, #007bff);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-action-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.quick-action-content {
  flex: 1;
}

.quick-action-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary, #333);
  margin: 0 0 4px 0;
}

.quick-action-description {
  font-size: 12px;
  color: var(--text-secondary, #666);
  margin: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--primary-color, #007bff);
  color: white;
  border-color: var(--primary-color, #007bff);
}

.btn-primary:hover {
  background: var(--primary-hover, #0056b3);
  border-color: var(--primary-hover, #0056b3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-create-container {
    padding: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .page-description {
    font-size: 14px;
  }
  
  .breadcrumb {
    font-size: 12px;
  }
  
  .error-content {
    flex-direction: column;
    text-align: center;
  }
  
  .help-section,
  .quick-actions {
    padding: 16px;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-action-card {
    padding: 12px;
  }
}
