import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil, switchMap } from 'rxjs';
import { LoadingSpinnerComponent } from '../../../components/ui/loading-spinner/loading-spinner.component';
import { ProductService } from '../../../services/product.service';
import { TagService } from '../../../services/tag.service';
import { CategoryService } from '../../../services/category.service';
import { InventoryService } from '../../../services/inventory.service';
import { SaleService } from '../../../services/sale.service';
import { ViewProductDto, TagDto, ProductCategoryDto, ViewInventoryItemDto, ViewSaleDto } from '../../../models';

@Component({
  selector: 'app-enhanced-product-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, LoadingSpinnerComponent],
  templateUrl: './enhanced-product-detail.component.html',
  styleUrl: '../product-list/product-list.component.css'
})
export class EnhancedProductDetailComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  loading = signal(true);
  error = signal<string | null>(null);
  product = signal<ViewProductDto | null>(null);
  inventoryItem = signal<ViewInventoryItemDto | null>(null);
  recentSales = signal<ViewSaleDto[]>([]);
  allTags = signal<TagDto[]>([]);
  allCategories = signal<ProductCategoryDto[]>([]);

  // Computed values
  hasProduct = computed(() => this.product() !== null);
  hasInventory = computed(() => this.inventoryItem() !== null);
  isInStock = computed(() => {
    const item = this.inventoryItem();
    return item ? item.availableQuantity > 0 : false;
  });
  isLowStock = computed(() => {
    const item = this.inventoryItem();
    return item ? item.availableQuantity <= item.threshold : false;
  });
  stockStatus = computed(() => {
    const item = this.inventoryItem();
    if (!item) return 'Unknown';
    if (item.availableQuantity === 0) return 'Out of Stock';
    if (item.availableQuantity <= item.threshold) return 'Low Stock';
    return 'In Stock';
  });
  stockStatusClass = computed(() => {
    const status = this.stockStatus();
    switch (status) {
      case 'Out of Stock': return 'badge-danger';
      case 'Low Stock': return 'badge-warning';
      case 'In Stock': return 'badge-success';
      default: return 'badge-secondary';
    }
  });

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private productService: ProductService,
    private tagService: TagService,
    private categoryService: CategoryService,
    private inventoryService: InventoryService,
    private saleService: SaleService
  ) {}

  ngOnInit(): void {
    this.loadProductDetails();
    this.loadReferenceData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadProductDetails(): void {
    this.loading.set(true);
    this.error.set(null);

    this.route.params.pipe(
      switchMap(params => {
        const productId = +params['id'];
        if (!productId || isNaN(productId)) {
          throw new Error('Invalid product ID');
        }
        return this.productService.getProduct(productId);
      }),
      takeUntil(this.destroy$)
    ).subscribe({
      next: (product) => {
        this.product.set(product);
        this.loadInventoryData(product.id);
        this.loadSalesData(product.id);
        this.loading.set(false);
      },
      error: (error) => {
        this.error.set(error.message || 'Failed to load product details');
        this.loading.set(false);
        console.error('Error loading product:', error);
      }
    });
  }

  private loadInventoryData(productId: number): void {
    this.inventoryService.getCurrentInventory()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (inventory) => {
          const item = inventory.items.find(item => item.productId === productId);
          this.inventoryItem.set(item || null);
        },
        error: (error) => {
          console.error('Error loading inventory data:', error);
        }
      });
  }

  private loadSalesData(productId: number): void {
    this.saleService.getSalesByProduct(productId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (sales) => {
          // Get recent completed sales
          const recentSales = sales
            .filter(sale => sale.status === 'Completed')
            .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
            .slice(0, 5);
          this.recentSales.set(recentSales);
        },
        error: (error) => {
          console.error('Error loading sales data:', error);
        }
      });
  }

  private loadReferenceData(): void {
    this.tagService.getTags()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tags) => this.allTags.set(tags),
        error: (error) => console.error('Error loading tags:', error)
      });

    this.categoryService.getCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (categories) => this.allCategories.set(categories),
        error: (error) => console.error('Error loading categories:', error)
      });
  }

  editProduct(): void {
    const product = this.product();
    if (product) {
      this.router.navigate(['/products', product.id, 'edit']);
    }
  }

  deleteProduct(): void {
    const product = this.product();
    if (product && confirm(`Are you sure you want to delete "${product.name}"?`)) {
      this.productService.deleteProduct(product.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.router.navigate(['/products']);
          },
          error: (error) => {
            this.error.set(error.message || 'Failed to delete product');
            console.error('Error deleting product:', error);
          }
        });
    }
  }

  goBack(): void {
    this.router.navigate(['/products']);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getTotalSold(): number {
    return this.recentSales().reduce((total, sale) => {
      const productItem = sale.items.find(item => item.productId === this.product()?.id);
      return total + (productItem?.quantity || 0);
    }, 0);
  }

  getSaleItemQuantity(sale: ViewSaleDto): number {
    const item = sale.items.find(item => item.productId === this.product()?.id);
    return item?.quantity || 0;
  }

  getSaleItemPrice(sale: ViewSaleDto): number {
    const item = sale.items.find(item => item.productId === this.product()?.id);
    return item?.totalPrice || 0;
  }

  getTotalRevenue(): number {
    return this.recentSales().reduce((total, sale) => {
      const productItem = sale.items.find(item => item.productId === this.product()?.id);
      return total + (productItem?.totalPrice || 0);
    }, 0);
  }
}
