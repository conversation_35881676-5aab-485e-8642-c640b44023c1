<div class="page-container">
  <div class="container">
    <!-- Loading State -->
    <div *ngIf="loading()" class="loading-container">
      <app-loading-spinner></app-loading-spinner>
      <p class="loading-text">Loading product details...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error() && !loading()" class="alert alert-danger">
      <h4>Error Loading Product</h4>
      <p>{{ error() }}</p>
      <div class="error-actions">
        <button class="btn btn-danger" (click)="loadProductDetails()">Try Again</button>
        <button class="btn btn-secondary" (click)="goBack()">← Back to Products</button>
      </div>
    </div>

    <!-- Product Details -->
    <div *ngIf="hasProduct() && !loading()" class="product-detail-content">
      
      <!-- Header -->
      <div class="page-header">
        <div class="header-content">
          <div class="breadcrumb">
            <a routerLink="/products" class="breadcrumb-link">Products</a>
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-current">{{ product()?.name }}</span>
          </div>
          <h1 class="page-title">{{ product()?.name }}</h1>
          <p class="page-description">{{ product()?.description }}</p>
        </div>
        <div class="header-actions">
          <button class="btn btn-outline-primary" (click)="editProduct()">
            <span class="btn-icon">✏️</span>
            Edit Product
          </button>
          <button class="btn btn-outline-danger" (click)="deleteProduct()">
            <span class="btn-icon">🗑️</span>
            Delete
          </button>
          <button class="btn btn-secondary" (click)="goBack()">
            <span class="btn-icon">←</span>
            Back
          </button>
        </div>
      </div>

      <!-- Product Information Grid -->
      <div class="product-grid">
        
        <!-- Basic Information -->
        <div class="product-card">
          <div class="card-header">
            <h3 class="card-title">Product Information</h3>
          </div>
          <div class="card-body">
            <div class="info-grid">
              <div class="info-item">
                <label class="info-label">Product ID</label>
                <div class="info-value">#{{ product()?.id }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">Name</label>
                <div class="info-value">{{ product()?.name }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">Description</label>
                <div class="info-value">{{ product()?.description }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">Category</label>
                <div class="info-value">
                  <span class="badge badge-secondary">{{ product()?.categoryName }}</span>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">Tags</label>
                <div class="info-value">
                  <div class="tags-container">
                    <span 
                      *ngFor="let tag of product()?.tags" 
                      class="badge badge-outline">
                      {{ tag }}
                    </span>
                    <span *ngIf="!product()?.tags?.length" class="text-muted">No tags</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Inventory Information -->
        <div class="product-card">
          <div class="card-header">
            <h3 class="card-title">Inventory Status</h3>
            <span class="badge" [ngClass]="stockStatusClass()">{{ stockStatus() }}</span>
          </div>
          <div class="card-body">
            <div *ngIf="hasInventory()" class="inventory-info">
              <div class="info-grid">
                <div class="info-item">
                  <label class="info-label">Available Quantity</label>
                  <div class="info-value">
                    <span class="quantity-value">{{ inventoryItem()?.availableQuantity }}</span>
                    <span class="quantity-unit">units</span>
                  </div>
                </div>
                <div class="info-item">
                  <label class="info-label">Total Quantity</label>
                  <div class="info-value">{{ inventoryItem()?.quantity }} units</div>
                </div>
                <div class="info-item">
                  <label class="info-label">Reserved</label>
                  <div class="info-value">{{ inventoryItem()?.reservedQuantity }} units</div>
                </div>
                <div class="info-item">
                  <label class="info-label">Low Stock Threshold</label>
                  <div class="info-value">{{ inventoryItem()?.threshold }} units</div>
                </div>
                <div class="info-item">
                  <label class="info-label">Actual Price</label>
                  <div class="info-value">{{ formatCurrency(inventoryItem()?.actualPrice || 0) }}</div>
                </div>
                <div class="info-item">
                  <label class="info-label">Selling Price</label>
                  <div class="info-value">{{ formatCurrency(inventoryItem()?.sellingPrice || 0) }}</div>
                </div>
              </div>

              <!-- Stock Alert -->
              <div *ngIf="isLowStock()" class="alert alert-warning">
                <span class="alert-icon">⚠️</span>
                <span class="alert-message">
                  This product is running low on stock. Consider restocking soon.
                </span>
              </div>

              <div *ngIf="!isInStock()" class="alert alert-danger">
                <span class="alert-icon">❌</span>
                <span class="alert-message">
                  This product is currently out of stock.
                </span>
              </div>
            </div>

            <div *ngIf="!hasInventory()" class="empty-state">
              <div class="empty-icon">📦</div>
              <h4 class="empty-title">No Inventory Data</h4>
              <p class="empty-description">This product is not currently in inventory.</p>
              <a routerLink="/inventory/current" class="btn btn-primary">
                Manage Inventory
              </a>
            </div>
          </div>
        </div>

        <!-- Sales Performance -->
        <div class="product-card">
          <div class="card-header">
            <h3 class="card-title">Sales Performance</h3>
            <a routerLink="/sales" [queryParams]="{productId: product()?.id}" class="card-link">
              View All Sales
            </a>
          </div>
          <div class="card-body">
            <div *ngIf="recentSales().length > 0" class="sales-info">
              <div class="sales-summary">
                <div class="summary-item">
                  <div class="summary-value">{{ getTotalSold() }}</div>
                  <div class="summary-label">Total Sold</div>
                </div>
                <div class="summary-item">
                  <div class="summary-value">{{ formatCurrency(getTotalRevenue()) }}</div>
                  <div class="summary-label">Total Revenue</div>
                </div>
                <div class="summary-item">
                  <div class="summary-value">{{ recentSales().length }}</div>
                  <div class="summary-label">Recent Sales</div>
                </div>
              </div>

              <div class="sales-list">
                <h4 class="sales-list-title">Recent Sales</h4>
                <div *ngFor="let sale of recentSales()" class="sale-item">
                  <div class="sale-info">
                    <div class="sale-id">Sale #{{ sale.id.substring(0, 8) }}</div>
                    <div class="sale-date">{{ formatDate(sale.createdAt) }}</div>
                  </div>
                  <div class="sale-details">
                    <div class="sale-quantity">
                      {{ getSaleItemQuantity(sale) }} units
                    </div>
                    <div class="sale-amount">
                      {{ formatCurrency(getSaleItemPrice(sale)) }}
                    </div>
                  </div>
                  <div class="sale-status">
                    <span class="badge badge-success">{{ sale.status }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div *ngIf="recentSales().length === 0" class="empty-state">
              <div class="empty-icon">💰</div>
              <h4 class="empty-title">No Sales Data</h4>
              <p class="empty-description">This product hasn't been sold yet.</p>
              <a routerLink="/sales/create" class="btn btn-primary">
                Create Sale
              </a>
            </div>
          </div>
        </div>

        <!-- Inventory History -->
        <div class="product-card" *ngIf="hasInventory()">
          <div class="card-header">
            <h3 class="card-title">Inventory History</h3>
          </div>
          <div class="card-body">
            <div class="history-grid">
              <div class="history-item">
                <label class="history-label">Total Sold</label>
                <div class="history-value">{{ inventoryItem()?.totalSoldQuantity }} units</div>
              </div>
              <div class="history-item">
                <label class="history-label">Total Wasted</label>
                <div class="history-value">{{ inventoryItem()?.totalWastedQuantity }} units</div>
              </div>
              <div class="history-item">
                <label class="history-label">Current Sold</label>
                <div class="history-value">{{ inventoryItem()?.soldQuantity }} units</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="product-card">
          <div class="card-header">
            <h3 class="card-title">Quick Actions</h3>
          </div>
          <div class="card-body">
            <div class="actions-grid">
              <button class="action-btn" (click)="editProduct()">
                <span class="action-icon">✏️</span>
                <span class="action-text">Edit Product</span>
              </button>
              <a routerLink="/sales/create" [queryParams]="{productId: product()?.id}" class="action-btn">
                <span class="action-icon">💰</span>
                <span class="action-text">Create Sale</span>
              </a>
              <a routerLink="/inventory/current" class="action-btn">
                <span class="action-icon">📦</span>
                <span class="action-text">Manage Stock</span>
              </a>
              <button class="action-btn danger" (click)="deleteProduct()">
                <span class="action-icon">🗑️</span>
                <span class="action-text">Delete Product</span>
              </button>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
