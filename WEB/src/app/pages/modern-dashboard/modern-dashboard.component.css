/* Modern Dashboard Styles */

.dashboard-container {
  min-height: calc(100vh - 200px);
  animation: fadeIn 0.5s ease-out;
}

/* Page Header */
.page-header {
  margin-bottom: var(--space-8);
}

.page-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-6);
}

.page-title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-shrink: 0;
}

/* Loading Section */
.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
}

.loading-text {
  color: var(--text-secondary);
  font-size: var(--text-base);
  margin: 0;
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
}

.stat-card {
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card.stat-warning::before {
  background: linear-gradient(90deg, var(--warning-500), var(--warning-600));
}

.stat-card.stat-success::before {
  background: linear-gradient(90deg, var(--success-500), var(--success-600));
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-2xl);
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  color: var(--primary-700);
}

.stat-trend {
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-trend.positive {
  background-color: var(--success-50);
  color: var(--success-700);
}

.stat-trend.warning {
  background-color: var(--warning-50);
  color: var(--warning-700);
}

.stat-trend.neutral {
  background-color: var(--neutral-100);
  color: var(--neutral-700);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.stat-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

/* Main Grid */
.main-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
}

/* Quick Actions */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.quick-action-card {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  text-decoration: none;
  transition: all var(--transition-base);
  background: var(--bg-surface);
  position: relative;
  overflow: hidden;
}

.quick-action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-500);
  transition: width var(--transition-fast);
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
}

.quick-action-card:hover::before {
  width: 100%;
  opacity: 0.1;
}

.action-primary::before { background: var(--primary-500); }
.action-success::before { background: var(--success-500); }
.action-info::before { background: var(--info-500); }
.action-warning::before { background: var(--warning-500); }
.action-secondary::before { background: var(--secondary-500); }

.action-icon {
  font-size: var(--text-2xl);
  flex-shrink: 0;
}

.action-content {
  flex: 1;
  min-width: 0;
}

.action-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  line-height: 1;
}

.action-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
}

/* Recent Activity */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  transition: background-color var(--transition-fast);
}

.activity-item:hover {
  background-color: var(--bg-surface-hover);
}

.activity-icon {
  font-size: var(--text-lg);
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-message {
  font-size: var(--text-sm);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  line-height: var(--leading-normal);
}

.activity-time {
  font-size: var(--text-xs);
  color: var(--text-muted);
  font-weight: var(--font-medium);
}

.activity-footer {
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
  text-align: center;
}

.view-all-link {
  color: var(--interactive-primary);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: color var(--transition-fast);
}

.view-all-link:hover {
  color: var(--interactive-primary-hover);
  text-decoration: underline;
}

/* Navigation Cards */
.nav-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.nav-card {
  display: flex;
  flex-direction: column;
  padding: var(--space-6);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  text-decoration: none;
  transition: all var(--transition-base);
  background: linear-gradient(135deg, var(--bg-surface) 0%, var(--bg-surface-hover) 100%);
  position: relative;
  overflow: hidden;
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  transform: scaleX(0);
  transition: transform var(--transition-base);
}

.nav-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-2xl);
  text-decoration: none;
}

.nav-card:hover::before {
  transform: scaleX(1);
}

.nav-card-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-4);
  align-self: flex-start;
}

.nav-card-content {
  flex: 1;
}

.nav-card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  line-height: 1;
}

.nav-card-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-3);
}

.nav-card-stats {
  font-size: var(--text-xs);
  color: var(--text-muted);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive Design */
@media (max-width: 1023px) {
  .main-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--space-4);
  }
  
  .page-header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }
  
  .page-actions {
    justify-content: flex-start;
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: var(--text-3xl);
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .nav-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .page-actions {
    flex-direction: column;
    align-items: stretch;
  }
}
