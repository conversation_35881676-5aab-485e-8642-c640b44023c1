<div class="dashboard-container">
  <!-- Page Header -->
  <div class="page-header">
    <div class="page-header-content">
      <div class="page-title-section">
        <h1 class="page-title">Dashboard</h1>
        <p class="page-subtitle">Welcome back! Here's what's happening with your store today.</p>
      </div>
      <div class="page-actions">
        <button 
          class="btn btn-secondary"
          (click)="onRefreshData()"
          [disabled]="loading"
          title="Refresh data"
        >
          <span class="btn-icon">🔄</span>
          <span>Refresh</span>
        </button>
        <a routerLink="/sales/create" class="btn btn-primary">
          <span class="btn-icon">➕</span>
          <span>New Sale</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-section">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <p class="loading-text">Loading dashboard data...</p>
    </div>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!loading" class="dashboard-content">
    <!-- Stats Grid -->
    <div class="stats-grid">
      <div class="stat-card" [class]="'stat-' + getStatColor('totalProducts')">
        <div class="stat-header">
          <div class="stat-icon">{{ getStatIcon('totalProducts') }}</div>
          <div class="stat-trend positive">+12%</div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(stats.totalProducts) }}</div>
          <div class="stat-label">Total Products</div>
        </div>
      </div>

      <div class="stat-card" [class]="'stat-' + getStatColor('totalCategories')">
        <div class="stat-header">
          <div class="stat-icon">{{ getStatIcon('totalCategories') }}</div>
          <div class="stat-trend positive">+3</div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(stats.totalCategories) }}</div>
          <div class="stat-label">Categories</div>
        </div>
      </div>

      <div class="stat-card" [class]="'stat-' + getStatColor('totalSales')">
        <div class="stat-header">
          <div class="stat-icon">{{ getStatIcon('totalSales') }}</div>
          <div class="stat-trend positive">+8%</div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(stats.totalSales) }}</div>
          <div class="stat-label">Sales Today</div>
        </div>
      </div>

      <div class="stat-card" [class]="'stat-' + getStatColor('totalRevenue')">
        <div class="stat-header">
          <div class="stat-icon">{{ getStatIcon('totalRevenue') }}</div>
          <div class="stat-trend positive">+15%</div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatCurrency(stats.totalRevenue) }}</div>
          <div class="stat-label">Revenue Today</div>
        </div>
      </div>

      <div class="stat-card" [class]="'stat-' + getStatColor('lowStockItems')">
        <div class="stat-header">
          <div class="stat-icon">{{ getStatIcon('lowStockItems') }}</div>
          <div class="stat-trend warning">Alert</div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(stats.lowStockItems) }}</div>
          <div class="stat-label">Low Stock Items</div>
        </div>
      </div>

      <div class="stat-card" [class]="'stat-' + getStatColor('pendingSales')">
        <div class="stat-header">
          <div class="stat-icon">{{ getStatIcon('pendingSales') }}</div>
          <div class="stat-trend neutral">Pending</div>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(stats.pendingSales) }}</div>
          <div class="stat-label">Pending Sales</div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="main-grid">
      <!-- Quick Actions -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Quick Actions</h3>
          <p class="card-subtitle">Frequently used actions for faster workflow</p>
        </div>
        <div class="card-body">
          <div class="quick-actions-grid">
            <a 
              *ngFor="let action of quickActions; trackBy: trackByActionTitle"
              [routerLink]="action.route"
              class="quick-action-card"
              [class]="'action-' + action.color"
            >
              <div class="action-icon">{{ action.icon }}</div>
              <div class="action-content">
                <div class="action-title">{{ action.title }}</div>
                <div class="action-description">{{ action.description }}</div>
              </div>
            </a>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Recent Activity</h3>
          <p class="card-subtitle">Latest updates and notifications</p>
        </div>
        <div class="card-body">
          <div class="activity-list">
            <div 
              *ngFor="let activity of recentActivities; trackBy: trackByActivityId"
              class="activity-item"
              [class]="'activity-' + activity.type"
            >
              <div class="activity-icon">{{ activity.icon }}</div>
              <div class="activity-content">
                <div class="activity-message">{{ activity.message }}</div>
                <div class="activity-time">{{ getRelativeTime(activity.timestamp) }}</div>
              </div>
            </div>
          </div>
          <div class="activity-footer">
            <a routerLink="/activity" class="view-all-link">
              View all activity →
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Cards -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Explore Tele-Shop</h3>
        <p class="card-subtitle">Navigate to different sections of your store</p>
      </div>
      <div class="card-body">
        <div class="nav-cards-grid">
          <a routerLink="/products" class="nav-card">
            <div class="nav-card-icon">📦</div>
            <div class="nav-card-content">
              <div class="nav-card-title">Products</div>
              <div class="nav-card-description">Manage your product catalog and inventory</div>
              <div class="nav-card-stats">{{ formatNumber(stats.totalProducts) }} products</div>
            </div>
          </a>

          <a routerLink="/inventory" class="nav-card">
            <div class="nav-card-icon">📋</div>
            <div class="nav-card-content">
              <div class="nav-card-title">Inventory</div>
              <div class="nav-card-description">Track stock levels and manage inventory</div>
              <div class="nav-card-stats">{{ formatNumber(stats.lowStockItems) }} low stock alerts</div>
            </div>
          </a>

          <a routerLink="/sales" class="nav-card">
            <div class="nav-card-icon">💰</div>
            <div class="nav-card-content">
              <div class="nav-card-title">Sales</div>
              <div class="nav-card-description">Process sales and view transaction history</div>
              <div class="nav-card-stats">{{ formatNumber(stats.totalSales) }} sales today</div>
            </div>
          </a>

          <a routerLink="/analytics" class="nav-card">
            <div class="nav-card-icon">📊</div>
            <div class="nav-card-content">
              <div class="nav-card-title">Analytics</div>
              <div class="nav-card-description">View detailed reports and insights</div>
              <div class="nav-card-stats">{{ formatCurrency(stats.totalRevenue) }} revenue</div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
