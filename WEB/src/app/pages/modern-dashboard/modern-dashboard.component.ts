import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

interface DashboardStats {
  totalProducts: number;
  totalCategories: number;
  totalSales: number;
  totalRevenue: number;
  lowStockItems: number;
  pendingSales: number;
}

interface QuickAction {
  title: string;
  description: string;
  icon: string;
  route: string;
  color: 'primary' | 'success' | 'info' | 'warning' | 'secondary';
}

interface RecentActivity {
  id: string;
  type: 'sale' | 'product' | 'inventory' | 'user';
  message: string;
  timestamp: Date;
  icon: string;
}

@Component({
  selector: 'app-modern-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './modern-dashboard.component.html',
  styleUrl: './modern-dashboard.component.css'
})
export class ModernDashboardComponent implements OnInit {
  loading = true;
  
  stats: DashboardStats = {
    totalProducts: 0,
    totalCategories: 0,
    totalSales: 0,
    totalRevenue: 0,
    lowStockItems: 0,
    pendingSales: 0
  };

  quickActions: QuickAction[] = [
    {
      title: 'Add Product',
      description: 'Create a new product',
      icon: '📦',
      route: '/products/create',
      color: 'primary'
    },
    {
      title: 'New Sale',
      description: 'Process a new sale',
      icon: '💰',
      route: '/sales/create',
      color: 'success'
    },
    {
      title: 'Check Inventory',
      description: 'View current stock levels',
      icon: '📋',
      route: '/inventory/current',
      color: 'info'
    },
    {
      title: 'Add Category',
      description: 'Create a new category',
      icon: '📂',
      route: '/categories/create',
      color: 'secondary'
    },
    {
      title: 'Sales Report',
      description: 'View sales analytics',
      icon: '📊',
      route: '/sales/reports',
      color: 'warning'
    },
    {
      title: 'Settings',
      description: 'Configure system settings',
      icon: '⚙️',
      route: '/settings',
      color: 'secondary'
    }
  ];

  recentActivities: RecentActivity[] = [];

  ngOnInit(): void {
    this.loadDashboardData();
  }

  private async loadDashboardData(): Promise<void> {
    try {
      await this.delay(1500);
      
      this.stats = {
        totalProducts: 1247,
        totalCategories: 23,
        totalSales: 89,
        totalRevenue: 45678.90,
        lowStockItems: 12,
        pendingSales: 5
      };

      this.recentActivities = [
        {
          id: '1',
          type: 'sale',
          message: 'New sale completed for $234.50',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          icon: '💰'
        },
        {
          id: '2',
          type: 'product',
          message: 'Product "Wireless Headphones" added to inventory',
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          icon: '📦'
        },
        {
          id: '3',
          type: 'inventory',
          message: 'Low stock alert: "Gaming Mouse" (3 items left)',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          icon: '⚠️'
        },
        {
          id: '4',
          type: 'user',
          message: 'New user "John Doe" registered',
          timestamp: new Date(Date.now() - 45 * 60 * 1000),
          icon: '👤'
        },
        {
          id: '5',
          type: 'sale',
          message: 'Sale #1234 marked as completed',
          timestamp: new Date(Date.now() - 60 * 60 * 1000),
          icon: '✅'
        }
      ];

      this.loading = false;
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      this.loading = false;
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatNumber(num: number): string {
    return new Intl.NumberFormat('en-US').format(num);
  }

  getRelativeTime(date: Date): string {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  }

  getStatIcon(statType: string): string {
    const icons: { [key: string]: string } = {
      totalProducts: '📦',
      totalCategories: '📂',
      totalSales: '💰',
      totalRevenue: '💵',
      lowStockItems: '⚠️',
      pendingSales: '⏳'
    };
    return icons[statType] || '📊';
  }

  getStatColor(statType: string): string {
    const colors: { [key: string]: string } = {
      totalProducts: 'primary',
      totalCategories: 'secondary',
      totalSales: 'success',
      totalRevenue: 'success',
      lowStockItems: 'warning',
      pendingSales: 'info'
    };
    return colors[statType] || 'primary';
  }

  onRefreshData(): void {
    this.loading = true;
    this.loadDashboardData();
  }

  trackByActivityId(index: number, activity: RecentActivity): string {
    return activity.id;
  }

  trackByActionTitle(index: number, action: QuickAction): string {
    return action.title;
  }
}
