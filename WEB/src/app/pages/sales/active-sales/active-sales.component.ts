import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-active-sales',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Active Sales</h1>
          <p class="page-description">Manage pending and in-progress sales</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">⏳</div>
          <h2 class="placeholder-title">Active Sales Management</h2>
          <p class="placeholder-description">
            This page will display and manage all active, pending, and in-progress sales transactions.
          </p>
          <div class="placeholder-actions">
            <a routerLink="/sales" class="btn btn-secondary">← Back to Sales</a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class ActiveSalesComponent {}
