<div class="page-container">
  <div class="container">
    <!-- <PERSON> Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">Sales History</h1>
        <p class="page-description">View and analyze completed sales transactions</p>
      </div>
      <div class="header-actions">
        <button 
          class="btn btn-outline-success" 
          (click)="exportSales()"
          [disabled]="loading() || filteredCount() === 0">
          <span class="btn-icon">📥</span>
          Export CSV
        </button>
        <button 
          class="btn btn-outline-primary" 
          (click)="refreshData()"
          [disabled]="loading()">
          <span class="btn-icon">🔄</span>
          Refresh
        </button>
        <a routerLink="/sales/create" class="btn btn-primary">
          <span class="btn-icon">➕</span>
          New Sale
        </a>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading()" class="loading-container">
      <app-loading-spinner></app-loading-spinner>
      <p class="loading-text">Loading sales history...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error() && !loading()" class="alert alert-danger">
      <h4>Error Loading Sales History</h4>
      <p>{{ error() }}</p>
      <button class="btn btn-danger" (click)="refreshData()">Try Again</button>
    </div>

    <!-- Sales History Content -->
    <div *ngIf="!loading() && !error()" class="sales-history-content">
      
      <!-- Summary Stats -->
      <div class="summary-stats">
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <div class="stat-value">{{ filteredCount() }}</div>
            <div class="stat-label">Sales Found</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💰</div>
          <div class="stat-content">
            <div class="stat-value">{{ formatCurrency(totalRevenue()) }}</div>
            <div class="stat-label">Total Revenue</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📈</div>
          <div class="stat-content">
            <div class="stat-value">{{ formatCurrency(averageOrderValue()) }}</div>
            <div class="stat-label">Average Order</div>
          </div>
        </div>
      </div>

      <!-- Filters Section -->
      <div class="filters-section">
        <form [formGroup]="filterForm" class="filters-form">
          <div class="filter-row">
            <div class="filter-group">
              <label for="search" class="filter-label">Search</label>
              <input
                type="text"
                id="search"
                formControlName="search"
                class="form-control"
                placeholder="Search by sale ID or product...">
            </div>
            
            <div class="filter-group">
              <label for="status" class="filter-label">Status</label>
              <select id="status" formControlName="status" class="form-control">
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            
            <div class="filter-group">
              <label for="dateRange" class="filter-label">Date Range</label>
              <select id="dateRange" formControlName="dateRange" class="form-control">
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
              </select>
            </div>
            
            <div class="filter-group">
              <label for="sortBy" class="filter-label">Sort By</label>
              <select id="sortBy" formControlName="sortBy" class="form-control">
                <option value="date">Date</option>
                <option value="amount">Amount</option>
                <option value="status">Status</option>
                <option value="items">Items Count</option>
              </select>
            </div>
            
            <div class="filter-group">
              <label for="sortDirection" class="filter-label">Order</label>
              <select id="sortDirection" formControlName="sortDirection" class="form-control">
                <option value="desc">Newest First</option>
                <option value="asc">Oldest First</option>
              </select>
            </div>
            
            <div class="filter-actions">
              <button type="button" class="btn btn-outline-secondary" (click)="clearFilters()">
                Clear Filters
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Empty State -->
      <div *ngIf="filteredCount() === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <h2 class="empty-title">No Sales Found</h2>
        <p class="empty-description">
          {{ totalSales() === 0 ? 'No sales have been recorded yet.' : 'No sales match your current filters.' }}
        </p>
        <div class="empty-actions">
          <button *ngIf="totalSales() > 0" class="btn btn-secondary" (click)="clearFilters()">
            Clear Filters
          </button>
          <a routerLink="/sales/create" class="btn btn-primary">
            <span class="btn-icon">➕</span>
            {{ totalSales() === 0 ? 'Create First Sale' : 'Create New Sale' }}
          </a>
        </div>
      </div>

      <!-- Sales Table -->
      <div *ngIf="filteredCount() > 0" class="sales-table-container">
        <table class="sales-table">
          <thead>
            <tr>
              <th>Sale ID</th>
              <th>Date</th>
              <th>Status</th>
              <th>Items</th>
              <th>Total</th>
              <th>Discount</th>
              <th>Final Amount</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let sale of filteredSales()" class="sale-row">
              <td class="sale-id-cell">
                <div class="sale-id">
                  <span class="id-short">#{{ sale.id.substring(0, 8) }}</span>
                  <span class="id-full" title="{{ sale.id }}">{{ sale.id }}</span>
                </div>
              </td>
              
              <td class="date-cell">
                <div class="date-info">
                  <div class="date-primary">{{ formatDate(sale.createdAt) }}</div>
                </div>
              </td>
              
              <td class="status-cell">
                <span class="badge" [ngClass]="'badge-' + getStatusColor(sale.status)">
                  {{ getStatusIcon(sale.status) }} {{ sale.status }}
                </span>
              </td>
              
              <td class="items-cell">
                <div class="items-info">
                  <div class="items-count">{{ getSaleItemsCount(sale) }} items</div>
                  <div class="items-detail">{{ sale.items.length }} product{{ sale.items.length !== 1 ? 's' : '' }}</div>
                </div>
              </td>
              
              <td class="amount-cell">
                {{ formatCurrency(sale.totalAmount) }}
              </td>
              
              <td class="discount-cell">
                <span *ngIf="sale.discountAmount > 0" class="discount-amount">
                  -{{ formatCurrency(sale.discountAmount) }}
                </span>
                <span *ngIf="sale.discountAmount === 0" class="no-discount">-</span>
              </td>
              
              <td class="final-amount-cell">
                <strong>{{ formatCurrency(sale.finalAmount) }}</strong>
              </td>
              
              <td class="actions-cell">
                <div class="action-buttons">
                  <a
                    routerLink="/sales"
                    [routerLink]="['/sales', sale.id, 'view']"
                    class="btn btn-sm btn-outline-info"
                    title="View details">
                    👁️
                  </a>
                  
                  <button
                    *ngIf="sale.status === 'Pending'"
                    class="btn btn-sm btn-outline-success"
                    title="Complete sale">
                    ✅
                  </button>
                  
                  <button
                    *ngIf="sale.status === 'Pending'"
                    class="btn btn-sm btn-outline-danger"
                    title="Cancel sale">
                    ❌
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination would go here if needed -->
      <div *ngIf="filteredCount() > 0" class="pagination-info">
        <p class="results-info">
          Showing {{ filteredCount() }} of {{ totalSales() }} sales
          <span *ngIf="filteredCount() !== totalSales()">
            (filtered from {{ totalSales() }} total)
          </span>
        </p>
      </div>

    </div>
  </div>
</div>
