import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { LoadingSpinnerComponent } from '../../../components/ui/loading-spinner/loading-spinner.component';
import { SaleService } from '../../../services/sale.service';
import { ViewSaleDto, SaleStatus } from '../../../models';

@Component({
  selector: 'app-enhanced-sales-history',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, LoadingSpinnerComponent],
  templateUrl: './enhanced-sales-history.component.html',
  styleUrl: './enhanced-sales-history.component.css'
})
export class EnhancedSalesHistoryComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  loading = signal(true);
  error = signal<string | null>(null);
  allSales = signal<ViewSaleDto[]>([]);
  searchTerm = signal('');
  statusFilter = signal<string>('all');
  dateFilter = signal<string>('all');
  sortBy = signal<string>('date');
  sortDirection = signal<'asc' | 'desc'>('desc');

  filterForm: FormGroup;

  // Computed values
  filteredSales = computed(() => {
    let sales = this.allSales();
    const search = this.searchTerm().toLowerCase();
    const status = this.statusFilter();
    const dateFilter = this.dateFilter();
    
    // Apply search filter
    if (search) {
      sales = sales.filter(sale => 
        sale.id.toLowerCase().includes(search) ||
        sale.items.some(item => item.productName.toLowerCase().includes(search))
      );
    }
    
    // Apply status filter
    if (status !== 'all') {
      sales = sales.filter(sale => sale.status.toLowerCase() === status.toLowerCase());
    }
    
    // Apply date filter
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          sales = sales.filter(sale => new Date(sale.createdAt) >= filterDate);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          sales = sales.filter(sale => new Date(sale.createdAt) >= filterDate);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          sales = sales.filter(sale => new Date(sale.createdAt) >= filterDate);
          break;
      }
    }
    
    // Apply sorting
    const sortBy = this.sortBy();
    const direction = this.sortDirection();
    
    sales.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'date':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'amount':
          aValue = a.finalAmount;
          bValue = b.finalAmount;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'items':
          aValue = a.items.length;
          bValue = b.items.length;
          break;
        default:
          return 0;
      }
      
      if (direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    
    return sales;
  });

  totalSales = computed(() => this.allSales().length);
  filteredCount = computed(() => this.filteredSales().length);
  totalRevenue = computed(() => 
    this.filteredSales()
      .filter(sale => sale.status === SaleStatus.Completed)
      .reduce((sum, sale) => sum + sale.finalAmount, 0)
  );
  averageOrderValue = computed(() => {
    const completedSales = this.filteredSales().filter(sale => sale.status === SaleStatus.Completed);
    return completedSales.length > 0 ? this.totalRevenue() / completedSales.length : 0;
  });

  constructor(
    private fb: FormBuilder,
    private saleService: SaleService
  ) {
    this.filterForm = this.createFilterForm();
  }

  ngOnInit(): void {
    this.loadSales();
    this.setupFormSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createFilterForm(): FormGroup {
    return this.fb.group({
      search: [''],
      status: ['all'],
      dateRange: ['all'],
      sortBy: ['date'],
      sortDirection: ['desc']
    });
  }

  private setupFormSubscriptions(): void {
    this.filterForm.get('search')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => this.searchTerm.set(value || ''));

    this.filterForm.get('status')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => this.statusFilter.set(value));

    this.filterForm.get('dateRange')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => this.dateFilter.set(value));

    this.filterForm.get('sortBy')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => this.sortBy.set(value));

    this.filterForm.get('sortDirection')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => this.sortDirection.set(value));
  }

  private loadSales(): void {
    this.loading.set(true);
    this.error.set(null);

    this.saleService.getSales()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (sales) => {
          this.allSales.set(sales);
          this.loading.set(false);
        },
        error: (error) => {
          this.error.set(error.message || 'Failed to load sales history');
          this.loading.set(false);
          console.error('Error loading sales:', error);
        }
      });
  }

  clearFilters(): void {
    this.filterForm.reset({
      search: '',
      status: 'all',
      dateRange: 'all',
      sortBy: 'date',
      sortDirection: 'desc'
    });
  }

  refreshData(): void {
    this.loadSales();
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'cancelled': return 'danger';
      default: return 'secondary';
    }
  }

  getStatusIcon(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed': return '✅';
      case 'pending': return '⏳';
      case 'cancelled': return '❌';
      default: return '📋';
    }
  }

  getSaleItemsCount(sale: ViewSaleDto): number {
    return sale.items.reduce((sum, item) => sum + item.quantity, 0);
  }

  viewSaleDetail(sale: ViewSaleDto): void {
    // Navigate to sale detail page
    console.log('Navigate to sale detail:', sale.id);
  }

  exportSales(): void {
    // Export filtered sales to CSV
    const sales = this.filteredSales();
    const csvContent = this.generateCSV(sales);
    this.downloadCSV(csvContent, 'sales-history.csv');
  }

  private generateCSV(sales: ViewSaleDto[]): string {
    const headers = ['Sale ID', 'Date', 'Status', 'Items', 'Total Amount', 'Discount', 'Final Amount'];
    const rows = sales.map(sale => [
      sale.id,
      this.formatDate(sale.createdAt),
      sale.status,
      this.getSaleItemsCount(sale).toString(),
      sale.totalAmount.toString(),
      sale.discountAmount.toString(),
      sale.finalAmount.toString()
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  private downloadCSV(content: string, filename: string): void {
    const blob = new Blob([content], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }
}
