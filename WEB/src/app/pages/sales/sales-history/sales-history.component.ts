import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-sales-history',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Sales History</h1>
          <p class="page-description">View completed sales transactions</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">📋</div>
          <h2 class="placeholder-title">Sales Transaction History</h2>
          <p class="placeholder-description">
            This page will display historical sales data with search and filtering capabilities.
          </p>
          <div class="placeholder-actions">
            <a routerLink="/sales" class="btn btn-secondary">← Back to Sales</a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class SalesHistoryComponent {}
