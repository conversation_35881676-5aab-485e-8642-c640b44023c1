import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Subject, takeUntil, forkJoin } from 'rxjs';
import { LoadingSpinnerComponent } from '../../../components/ui/loading-spinner/loading-spinner.component';
import { SaleService } from '../../../services/sale.service';
import { ProductService } from '../../../services/product.service';
import { ViewSaleDto, ViewProductDto, SaleStatus } from '../../../models';

interface SalesStats {
  totalSales: number;
  completedSales: number;
  pendingSales: number;
  cancelledSales: number;
  totalRevenue: number;
  averageOrderValue: number;
  todaysSales: number;
  todaysRevenue: number;
  topSellingProducts: TopSellingProduct[];
  recentSales: ViewSaleDto[];
  salesByStatus: { [key: string]: number };
}

interface TopSellingProduct {
  productId: number;
  productName: string;
  totalQuantitySold: number;
  totalRevenue: number;
}

@Component({
  selector: 'app-enhanced-sales-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule, LoadingSpinnerComponent],
  templateUrl: './enhanced-sales-dashboard.component.html',
  styleUrl: './enhanced-sales-dashboard.component.css'
})
export class EnhancedSalesDashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  loading = signal(true);
  error = signal<string | null>(null);
  stats = signal<SalesStats | null>(null);
  allSales = signal<ViewSaleDto[]>([]);
  allProducts = signal<ViewProductDto[]>([]);

  // Computed values
  hasStats = computed(() => this.stats() !== null);
  hasPendingSales = computed(() => (this.stats()?.pendingSales || 0) > 0);
  hasCompletedSales = computed(() => (this.stats()?.completedSales || 0) > 0);

  constructor(
    private saleService: SaleService,
    private productService: ProductService
  ) {}

  ngOnInit(): void {
    this.loadSalesData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadSalesData(): void {
    this.loading.set(true);
    this.error.set(null);

    forkJoin({
      sales: this.saleService.getSales(),
      products: this.productService.getProducts()
    }).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: ({ sales, products }) => {
        this.allSales.set(sales);
        this.allProducts.set(products);
        this.calculateStats(sales, products);
        this.loading.set(false);
      },
      error: (error) => {
        this.error.set(error.message || 'Failed to load sales data');
        this.loading.set(false);
        console.error('Error loading sales data:', error);
      }
    });
  }

  private calculateStats(sales: ViewSaleDto[], products: ViewProductDto[]): void {
    const today = new Date().toISOString().split('T')[0];
    
    const completedSales = sales.filter(s => s.status === SaleStatus.Completed);
    const pendingSales = sales.filter(s => s.status === SaleStatus.Pending);
    const cancelledSales = sales.filter(s => s.status === SaleStatus.Cancelled);
    
    const todaysSales = completedSales.filter(s => s.createdAt.startsWith(today));
    
    const totalRevenue = completedSales.reduce((sum, sale) => sum + sale.finalAmount, 0);
    const todaysRevenue = todaysSales.reduce((sum, sale) => sum + sale.finalAmount, 0);
    const averageOrderValue = completedSales.length > 0 ? totalRevenue / completedSales.length : 0;

    // Calculate top selling products
    const productSales = new Map<number, { name: string, quantity: number, revenue: number }>();
    completedSales.forEach(sale => {
      sale.items.forEach(item => {
        const existing = productSales.get(item.productId) || { 
          name: item.productName, 
          quantity: 0, 
          revenue: 0 
        };
        existing.quantity += item.quantity;
        existing.revenue += item.totalPrice;
        productSales.set(item.productId, existing);
      });
    });

    const topSellingProducts: TopSellingProduct[] = Array.from(productSales.entries())
      .map(([productId, data]) => ({
        productId,
        productName: data.name,
        totalQuantitySold: data.quantity,
        totalRevenue: data.revenue
      }))
      .sort((a, b) => b.totalQuantitySold - a.totalQuantitySold)
      .slice(0, 5);

    const recentSales = sales
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 10);

    const salesByStatus = {
      [SaleStatus.Completed]: completedSales.length,
      [SaleStatus.Pending]: pendingSales.length,
      [SaleStatus.Cancelled]: cancelledSales.length
    };

    const stats: SalesStats = {
      totalSales: sales.length,
      completedSales: completedSales.length,
      pendingSales: pendingSales.length,
      cancelledSales: cancelledSales.length,
      totalRevenue,
      averageOrderValue,
      todaysSales: todaysSales.length,
      todaysRevenue,
      topSellingProducts,
      recentSales,
      salesByStatus
    };

    this.stats.set(stats);
  }

  refreshData(): void {
    this.loadSalesData();
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'cancelled': return 'danger';
      default: return 'secondary';
    }
  }

  getStatusIcon(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed': return '✅';
      case 'pending': return '⏳';
      case 'cancelled': return '❌';
      default: return '📋';
    }
  }

  getSaleItemsCount(sale: ViewSaleDto): number {
    return sale.items.reduce((sum, item) => sum + item.quantity, 0);
  }

  getPercentageChange(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }

  // Quick actions
  createNewSale(): void {
    // This would typically navigate to create sale page
    console.log('Navigate to create sale');
  }

  viewPendingSales(): void {
    // This would typically navigate to pending sales page
    console.log('Navigate to pending sales');
  }

  viewSalesHistory(): void {
    // This would typically navigate to sales history page
    console.log('Navigate to sales history');
  }
}
