import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-sales-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Sales Dashboard</h1>
          <p class="page-description">Track sales performance and manage transactions</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">💰</div>
          <h2 class="placeholder-title">Sales Management System</h2>
          <p class="placeholder-description">
            This dashboard will provide comprehensive sales management including transaction processing, 
            sales analytics, and performance reporting.
          </p>
          <ul class="placeholder-features">
            <li>Point of Sale (POS) interface</li>
            <li>Sales transaction history</li>
            <li>Revenue and performance analytics</li>
            <li>Customer purchase tracking</li>
            <li>Sales reports and insights</li>
          </ul>
          <div class="placeholder-actions">
            <a routerLink="/sales/create" class="btn btn-primary">
              <span>➕</span> New Sale
            </a>
            <a routerLink="/sales/active" class="btn btn-secondary">
              <span>⏳</span> Active Sales
            </a>
            <a routerLink="/dashboard" class="btn btn-secondary">
              <span>🏠</span> Back to Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class SalesDashboardComponent {}
