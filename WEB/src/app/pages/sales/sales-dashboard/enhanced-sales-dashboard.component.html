<div class="page-container">
  <div class="container">
    <!-- <PERSON> Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">Sales Dashboard</h1>
        <p class="page-description">Track sales performance and manage transactions</p>
      </div>
      <div class="header-actions">
        <a routerLink="/sales/create" class="btn btn-primary">
          <span class="btn-icon">➕</span>
          New Sale
        </a>
        <button 
          class="btn btn-outline-primary" 
          (click)="refreshData()"
          [disabled]="loading()">
          <span class="btn-icon">🔄</span>
          Refresh
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading()" class="loading-container">
      <app-loading-spinner></app-loading-spinner>
      <p class="loading-text">Loading sales data...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error() && !loading()" class="alert alert-danger">
      <h4>Error Loading Sales Data</h4>
      <p>{{ error() }}</p>
      <button class="btn btn-danger" (click)="refreshData()">Try Again</button>
    </div>

    <!-- Sales Dashboard Content -->
    <div *ngIf="hasStats() && !loading()" class="sales-dashboard-content">
      
      <!-- Key Metrics -->
      <div class="metrics-grid">
        <div class="metric-card primary">
          <div class="metric-icon">💰</div>
          <div class="metric-content">
            <div class="metric-value">{{ formatCurrency(stats()?.totalRevenue || 0) }}</div>
            <div class="metric-label">Total Revenue</div>
          </div>
          <div class="metric-trend">
            <span class="trend-value">{{ stats()?.completedSales || 0 }} sales</span>
          </div>
        </div>

        <div class="metric-card success">
          <div class="metric-icon">✅</div>
          <div class="metric-content">
            <div class="metric-value">{{ stats()?.completedSales || 0 }}</div>
            <div class="metric-label">Completed Sales</div>
          </div>
          <div class="metric-trend">
            <span class="trend-value">{{ formatCurrency(stats()?.averageOrderValue || 0) }} avg</span>
          </div>
        </div>

        <div class="metric-card warning" *ngIf="hasPendingSales()">
          <div class="metric-icon">⏳</div>
          <div class="metric-content">
            <div class="metric-value">{{ stats()?.pendingSales || 0 }}</div>
            <div class="metric-label">Pending Sales</div>
          </div>
          <a routerLink="/sales/active" class="metric-link">Manage</a>
        </div>

        <div class="metric-card info">
          <div class="metric-icon">📅</div>
          <div class="metric-content">
            <div class="metric-value">{{ formatCurrency(stats()?.todaysRevenue || 0) }}</div>
            <div class="metric-label">Today's Revenue</div>
          </div>
          <div class="metric-trend">
            <span class="trend-value">{{ stats()?.todaysSales || 0 }} sales</span>
          </div>
        </div>
      </div>

      <!-- Dashboard Grid -->
      <div class="dashboard-grid">
        
        <!-- Top Selling Products -->
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">Top Selling Products</h3>
            <a routerLink="/products" class="card-link">View All</a>
          </div>
          <div class="card-body">
            <div *ngIf="stats()?.topSellingProducts?.length === 0" class="empty-state">
              <p>No sales data available</p>
            </div>
            <div *ngFor="let product of stats()?.topSellingProducts" class="product-item">
              <div class="product-info">
                <div class="product-name">{{ product.productName }}</div>
                <div class="product-stats">
                  {{ product.totalQuantitySold }} sold • {{ formatCurrency(product.totalRevenue) }}
                </div>
              </div>
              <div class="product-badge">{{ product.totalQuantitySold }}</div>
            </div>
          </div>
        </div>

        <!-- Recent Sales -->
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">Recent Sales</h3>
            <a routerLink="/sales/history" class="card-link">View All</a>
          </div>
          <div class="card-body">
            <div *ngIf="stats()?.recentSales?.length === 0" class="empty-state">
              <p>No recent sales</p>
            </div>
            <div *ngFor="let sale of stats()?.recentSales?.slice(0, 5)" class="sale-item">
              <div class="sale-info">
                <div class="sale-id">Sale #{{ sale.id.substring(0, 8) }}</div>
                <div class="sale-date">{{ formatDate(sale.createdAt) }}</div>
              </div>
              <div class="sale-details">
                <div class="sale-items">{{ getSaleItemsCount(sale) }} items</div>
                <div class="sale-amount">{{ formatCurrency(sale.finalAmount) }}</div>
              </div>
              <div class="sale-status">
                <span class="badge" [ngClass]="'badge-' + getStatusColor(sale.status)">
                  {{ getStatusIcon(sale.status) }} {{ sale.status }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Sales by Status -->
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">Sales by Status</h3>
          </div>
          <div class="card-body">
            <div class="status-grid">
              <div class="status-item">
                <div class="status-icon success">✅</div>
                <div class="status-content">
                  <div class="status-value">{{ stats()?.completedSales || 0 }}</div>
                  <div class="status-label">Completed</div>
                </div>
              </div>
              
              <div class="status-item" *ngIf="hasPendingSales()">
                <div class="status-icon warning">⏳</div>
                <div class="status-content">
                  <div class="status-value">{{ stats()?.pendingSales || 0 }}</div>
                  <div class="status-label">Pending</div>
                </div>
              </div>
              
              <div class="status-item" *ngIf="(stats()?.cancelledSales || 0) > 0">
                <div class="status-icon danger">❌</div>
                <div class="status-content">
                  <div class="status-value">{{ stats()?.cancelledSales || 0 }}</div>
                  <div class="status-label">Cancelled</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Summary -->
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">Performance Summary</h3>
          </div>
          <div class="card-body">
            <div class="summary-grid">
              <div class="summary-item">
                <div class="summary-label">Total Sales</div>
                <div class="summary-value">{{ stats()?.totalSales || 0 }}</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">Average Order Value</div>
                <div class="summary-value">{{ formatCurrency(stats()?.averageOrderValue || 0) }}</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">Today's Sales</div>
                <div class="summary-value">{{ stats()?.todaysSales || 0 }}</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">Today's Revenue</div>
                <div class="summary-value">{{ formatCurrency(stats()?.todaysRevenue || 0) }}</div>
              </div>
            </div>
          </div>
        </div>

      </div>

      <!-- Quick Actions -->
      <div class="quick-actions-section">
        <h2 class="section-title">Quick Actions</h2>
        <div class="quick-actions-grid">
          <a routerLink="/sales/create" class="quick-action-card primary">
            <div class="action-icon">➕</div>
            <div class="action-content">
              <h3 class="action-title">New Sale</h3>
              <p class="action-description">Process a new transaction</p>
            </div>
            <div class="action-arrow">→</div>
          </a>
          
          <a routerLink="/sales/active" class="quick-action-card warning" *ngIf="hasPendingSales()">
            <div class="action-icon">⏳</div>
            <div class="action-content">
              <h3 class="action-title">Pending Sales</h3>
              <p class="action-description">{{ stats()?.pendingSales }} sales waiting</p>
            </div>
            <div class="action-arrow">→</div>
          </a>
          
          <a routerLink="/sales/history" class="quick-action-card info">
            <div class="action-icon">📋</div>
            <div class="action-content">
              <h3 class="action-title">Sales History</h3>
              <p class="action-description">View completed transactions</p>
            </div>
            <div class="action-arrow">→</div>
          </a>
          
          <a routerLink="/sales/reports" class="quick-action-card secondary">
            <div class="action-icon">📊</div>
            <div class="action-content">
              <h3 class="action-title">Reports</h3>
              <p class="action-description">Analyze sales performance</p>
            </div>
            <div class="action-arrow">→</div>
          </a>
        </div>
      </div>

    </div>

    <!-- Empty State -->
    <div *ngIf="!hasStats() && !loading() && !error()" class="empty-state">
      <div class="empty-icon">💰</div>
      <h2 class="empty-title">No Sales Data</h2>
      <p class="empty-description">
        Start processing sales to see your dashboard metrics.
      </p>
      <a routerLink="/sales/create" class="btn btn-primary">
        <span class="btn-icon">➕</span>
        Create First Sale
      </a>
    </div>

  </div>
</div>
