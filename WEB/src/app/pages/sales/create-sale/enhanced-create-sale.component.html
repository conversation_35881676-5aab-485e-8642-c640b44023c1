<div class="page-container">
  <div class="container">
    <!-- <PERSON> Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">{{ saleCreated() ? 'Sale Created' : 'Create New Sale' }}</h1>
        <p class="page-description">{{ saleCreated() ? 'Sale has been created successfully' : 'Point of Sale (POS) Interface' }}</p>
      </div>
      <div class="header-actions">
        <a routerLink="/sales" class="btn btn-outline-secondary">
          <span class="btn-icon">←</span>
          Back to Sales
        </a>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading()" class="loading-container">
      <app-loading-spinner></app-loading-spinner>
      <p class="loading-text">Loading products and inventory...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error() && !loading()" class="alert alert-danger">
      <h4>Error</h4>
      <p>{{ error() }}</p>
      <button *ngIf="!saleCreated()" class="btn btn-danger" (click)="loadData()">Try Again</button>
    </div>

    <!-- Sale Created Success -->
    <div *ngIf="saleCreated() && !loading()" class="sale-success-container">
      <div class="success-card">
        <div class="success-icon">✅</div>
        <h2 class="success-title">Sale Created Successfully!</h2>
        <p class="success-description">
          Sale ID: <strong>{{ createdSaleId()?.substring(0, 8) }}</strong>
        </p>
        
        <div class="success-actions">
          <button 
            class="btn btn-primary"
            (click)="completeSale()"
            [disabled]="submitting()">
            <span *ngIf="submitting()" class="btn-spinner">⏳</span>
            <span class="btn-text">{{ submitting() ? 'Completing...' : 'Complete Sale' }}</span>
          </button>
          
          <a 
            routerLink="/sales"
            [routerLink]="['/sales', createdSaleId(), 'view']"
            class="btn btn-outline-info">
            <span class="btn-icon">👁️</span>
            View Sale Details
          </a>
          
          <button 
            class="btn btn-outline-secondary"
            (click)="startNewSale()"
            [disabled]="submitting()">
            <span class="btn-icon">➕</span>
            Create Another Sale
          </button>
        </div>
      </div>
    </div>

    <!-- POS Interface -->
    <div *ngIf="!saleCreated() && !loading() && !error()" class="pos-interface">
      
      <!-- POS Grid -->
      <div class="pos-grid">
        
        <!-- Product Selection -->
        <div class="products-section">
          <div class="section-header">
            <h2 class="section-title">Select Products</h2>
            <div class="product-search">
              <input
                type="text"
                class="form-control"
                placeholder="Search products..."
                [value]="searchTerm()"
                (input)="onSearchChange($event)">
              <button 
                *ngIf="searchTerm()" 
                class="search-clear-btn"
                (click)="clearSearch()">
                ×
              </button>
            </div>
          </div>

          <!-- Add Item Form -->
          <div class="add-item-form">
            <form [formGroup]="addItemForm" (ngSubmit)="addToCart()" class="item-form">
              <div class="form-row">
                <div class="form-group flex-grow">
                  <select
                    formControlName="productId"
                    class="form-control"
                    [class.is-invalid]="isFieldInvalid(addItemForm, 'productId')">
                    <option value="">Select a product</option>
                    <option *ngFor="let product of filteredProducts()" [value]="product.id">
                      {{ product.name }} - {{ formatCurrency(getInventoryItem(product.id)?.sellingPrice || 0) }}
                      ({{ getInventoryItem(product.id)?.availableQuantity }} available)
                    </option>
                  </select>
                </div>
                <div class="form-group quantity-group">
                  <input
                    type="number"
                    formControlName="quantity"
                    class="form-control"
                    [class.is-invalid]="isFieldInvalid(addItemForm, 'quantity')"
                    placeholder="Qty"
                    min="1">
                </div>
                <div class="form-group">
                  <button
                    type="submit"
                    class="btn btn-primary"
                    [disabled]="addItemForm.invalid">
                    <span class="btn-icon">➕</span>
                    Add
                  </button>
                </div>
              </div>
            </form>
          </div>

          <!-- Quick Add Products -->
          <div class="quick-products">
            <div *ngIf="filteredProducts().length === 0" class="empty-products">
              <p>{{ searchTerm() ? 'No products match your search' : 'No products available in inventory' }}</p>
            </div>
            
            <div class="products-grid">
              <div 
                *ngFor="let product of filteredProducts().slice(0, 12)" 
                class="product-card"
                (click)="quickAddProduct(product)">
                <div class="product-info">
                  <h4 class="product-name">{{ product.name }}</h4>
                  <p class="product-price">{{ formatCurrency(getInventoryItem(product.id)?.sellingPrice || 0) }}</p>
                  <p class="product-stock">{{ getInventoryItem(product.id)?.availableQuantity }} in stock</p>
                </div>
                <div class="product-action">
                  <span class="add-icon">+</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Shopping Cart -->
        <div class="cart-section">
          <div class="section-header">
            <h2 class="section-title">Shopping Cart</h2>
            <div class="cart-summary">
              <span class="cart-count">{{ cartItemCount() }} items</span>
              <span class="cart-total">{{ formatCurrency(cartTotal()) }}</span>
            </div>
          </div>

          <!-- Empty Cart -->
          <div *ngIf="!hasCartItems()" class="empty-cart">
            <div class="empty-icon">🛒</div>
            <h3 class="empty-title">Cart is Empty</h3>
            <p class="empty-description">Add products to start creating a sale</p>
          </div>

          <!-- Cart Items -->
          <div *ngIf="hasCartItems()" class="cart-items">
            <div *ngFor="let item of cart()" class="cart-item">
              <div class="item-info">
                <h4 class="item-name">{{ item.productName }}</h4>
                <p class="item-price">{{ formatCurrency(item.unitPrice) }} each</p>
              </div>
              
              <div class="item-quantity">
                <button 
                  class="qty-btn"
                  (click)="updateCartItemQuantity(item.productId, item.quantity - 1)"
                  [disabled]="item.quantity <= 1">
                  -
                </button>
                <span class="qty-value">{{ item.quantity }}</span>
                <button 
                  class="qty-btn"
                  (click)="updateCartItemQuantity(item.productId, item.quantity + 1)"
                  [disabled]="item.quantity >= item.availableStock">
                  +
                </button>
              </div>
              
              <div class="item-total">
                <div class="total-price">{{ formatCurrency(item.totalPrice) }}</div>
                <button 
                  class="remove-btn"
                  (click)="removeFromCart(item.productId)"
                  title="Remove item">
                  🗑️
                </button>
              </div>
            </div>
          </div>

          <!-- Discount Section -->
          <div *ngIf="hasCartItems()" class="discount-section">
            <button 
              class="btn btn-outline-secondary btn-sm"
              (click)="toggleDiscountForm()">
              <span class="btn-icon">{{ showDiscountForm() ? '×' : '💰' }}</span>
              {{ showDiscountForm() ? 'Cancel' : 'Add Discount' }}
            </button>
            
            <div *ngIf="showDiscountForm()" class="discount-form">
              <form [formGroup]="discountForm" class="discount-form-content">
                <div class="form-group">
                  <input
                    type="number"
                    formControlName="discountAmount"
                    class="form-control"
                    placeholder="Discount amount"
                    min="0"
                    step="0.01">
                </div>
                <div class="form-group">
                  <select formControlName="currency" class="form-control">
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                  </select>
                </div>
              </form>
            </div>
          </div>

          <!-- Cart Total -->
          <div *ngIf="hasCartItems()" class="cart-total-section">
            <div class="total-breakdown">
              <div class="total-line">
                <span class="total-label">Subtotal:</span>
                <span class="total-value">{{ formatCurrency(cartTotal()) }}</span>
              </div>
              <div *ngIf="discountForm.value.discountAmount > 0" class="total-line discount">
                <span class="total-label">Discount:</span>
                <span class="total-value">-{{ formatCurrency(discountForm.value.discountAmount) }}</span>
              </div>
              <div class="total-line final">
                <span class="total-label">Total:</span>
                <span class="total-value">{{ formatCurrency(cartTotal() - (discountForm.value.discountAmount || 0)) }}</span>
              </div>
            </div>
          </div>

          <!-- Cart Actions -->
          <div *ngIf="hasCartItems()" class="cart-actions">
            <button 
              class="btn btn-outline-danger"
              (click)="clearCart()"
              [disabled]="submitting()">
              <span class="btn-icon">🗑️</span>
              Clear Cart
            </button>
            
            <button 
              class="btn btn-primary"
              (click)="createSale()"
              [disabled]="submitting() || !hasCartItems()">
              <span *ngIf="submitting()" class="btn-spinner">⏳</span>
              <span class="btn-text">{{ submitting() ? 'Creating...' : 'Create Sale' }}</span>
            </button>
          </div>
        </div>

      </div>
    </div>

  </div>
</div>
