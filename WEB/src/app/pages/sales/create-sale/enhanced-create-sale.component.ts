import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil, forkJoin } from 'rxjs';
import { LoadingSpinnerComponent } from '../../../components/ui/loading-spinner/loading-spinner.component';
import { SaleService } from '../../../services/sale.service';
import { ProductService } from '../../../services/product.service';
import { InventoryService } from '../../../services/inventory.service';
import { CreateSaleDto, SaleItemDto, ViewProductDto, ViewInventoryDto, ViewInventoryItemDto, CompleteSaleDto } from '../../../models';

interface CartItem {
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  availableStock: number;
}

@Component({
  selector: 'app-enhanced-create-sale',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, LoadingSpinnerComponent],
  templateUrl: './enhanced-create-sale.component.html',
  styleUrl: './enhanced-create-sale.component.css'
})
export class EnhancedCreateSaleComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  loading = signal(true);
  submitting = signal(false);
  error = signal<string | null>(null);
  products = signal<ViewProductDto[]>([]);
  inventory = signal<ViewInventoryDto | null>(null);
  cart = signal<CartItem[]>([]);
  searchTerm = signal('');
  showDiscountForm = signal(false);
  saleCreated = signal(false);
  createdSaleId = signal<string | null>(null);

  addItemForm: FormGroup;
  discountForm: FormGroup;

  // Computed values
  filteredProducts = computed(() => {
    const products = this.products();
    const search = this.searchTerm().toLowerCase();
    const inventory = this.inventory();
    
    if (!inventory) return [];
    
    // Only show products that are in inventory and available
    const availableProducts = products.filter(product => {
      const inventoryItem = inventory.items.find(item => item.productId === product.id);
      return inventoryItem && inventoryItem.availableQuantity > 0;
    });
    
    if (!search) return availableProducts;
    
    return availableProducts.filter(product => 
      product.name.toLowerCase().includes(search) ||
      product.description.toLowerCase().includes(search)
    );
  });

  cartTotal = computed(() => 
    this.cart().reduce((sum, item) => sum + item.totalPrice, 0)
  );
  
  cartItemCount = computed(() => 
    this.cart().reduce((sum, item) => sum + item.quantity, 0)
  );
  
  hasCartItems = computed(() => this.cart().length > 0);

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private saleService: SaleService,
    private productService: ProductService,
    private inventoryService: InventoryService
  ) {
    this.addItemForm = this.createAddItemForm();
    this.discountForm = this.createDiscountForm();
  }

  ngOnInit(): void {
    this.loadData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createAddItemForm(): FormGroup {
    return this.fb.group({
      productId: ['', [Validators.required]],
      quantity: [1, [Validators.required, Validators.min(1)]]
    });
  }

  private createDiscountForm(): FormGroup {
    return this.fb.group({
      discountAmount: [0, [Validators.min(0)]],
      currency: ['USD', [Validators.required]]
    });
  }

  loadData(): void {
    this.loading.set(true);
    this.error.set(null);

    forkJoin({
      products: this.productService.getProducts(),
      inventory: this.inventoryService.getCurrentInventory()
    }).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: ({ products, inventory }) => {
        this.products.set(products);
        this.inventory.set(inventory);
        this.loading.set(false);
      },
      error: (error) => {
        this.error.set(error.message || 'Failed to load data');
        this.loading.set(false);
        console.error('Error loading data:', error);
      }
    });
  }

  // Search functionality
  onSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm.set(target.value);
  }

  clearSearch(): void {
    this.searchTerm.set('');
  }

  // Cart management
  addToCart(): void {
    if (this.addItemForm.valid) {
      const formValue = this.addItemForm.value;
      const productId = parseInt(formValue.productId);
      const quantity = parseInt(formValue.quantity);
      
      const product = this.products().find(p => p.id === productId);
      const inventoryItem = this.inventory()?.items.find(item => item.productId === productId);
      
      if (!product || !inventoryItem) {
        this.error.set('Product not found or not in inventory');
        return;
      }

      if (quantity > inventoryItem.availableQuantity) {
        this.error.set(`Only ${inventoryItem.availableQuantity} units available`);
        return;
      }

      // Check if item already in cart
      const existingCartItem = this.cart().find(item => item.productId === productId);
      if (existingCartItem) {
        const newQuantity = existingCartItem.quantity + quantity;
        if (newQuantity > inventoryItem.availableQuantity) {
          this.error.set(`Cannot add more. Only ${inventoryItem.availableQuantity} units available`);
          return;
        }
        this.updateCartItemQuantity(productId, newQuantity);
      } else {
        const cartItem: CartItem = {
          productId,
          productName: product.name,
          quantity,
          unitPrice: inventoryItem.sellingPrice,
          totalPrice: inventoryItem.sellingPrice * quantity,
          availableStock: inventoryItem.availableQuantity
        };
        
        this.cart.set([...this.cart(), cartItem]);
      }

      this.addItemForm.reset({ quantity: 1 });
      this.error.set(null);
    }
  }

  updateCartItemQuantity(productId: number, newQuantity: number): void {
    const inventoryItem = this.inventory()?.items.find(item => item.productId === productId);
    if (!inventoryItem) return;

    if (newQuantity > inventoryItem.availableQuantity) {
      this.error.set(`Only ${inventoryItem.availableQuantity} units available`);
      return;
    }

    const updatedCart = this.cart().map(item => {
      if (item.productId === productId) {
        return {
          ...item,
          quantity: newQuantity,
          totalPrice: item.unitPrice * newQuantity
        };
      }
      return item;
    });

    this.cart.set(updatedCart);
    this.error.set(null);
  }

  removeFromCart(productId: number): void {
    const updatedCart = this.cart().filter(item => item.productId !== productId);
    this.cart.set(updatedCart);
  }

  clearCart(): void {
    this.cart.set([]);
    this.showDiscountForm.set(false);
    this.discountForm.reset({ discountAmount: 0, currency: 'USD' });
  }

  // Quick add functionality
  quickAddProduct(product: ViewProductDto): void {
    const inventoryItem = this.inventory()?.items.find(item => item.productId === product.id);
    if (!inventoryItem || inventoryItem.availableQuantity === 0) {
      this.error.set('Product not available');
      return;
    }

    this.addItemForm.patchValue({
      productId: product.id,
      quantity: 1
    });
    this.addToCart();
  }

  // Discount management
  toggleDiscountForm(): void {
    this.showDiscountForm.set(!this.showDiscountForm());
    if (!this.showDiscountForm()) {
      this.discountForm.reset({ discountAmount: 0, currency: 'USD' });
    }
  }

  // Sale processing
  createSale(): void {
    if (!this.hasCartItems()) {
      this.error.set('Cart is empty');
      return;
    }

    this.submitting.set(true);
    this.error.set(null);

    const saleItems: SaleItemDto[] = this.cart().map(item => ({
      productId: item.productId,
      quantity: item.quantity
    }));

    const discountAmount = this.discountForm.value.discountAmount || 0;
    const currency = this.discountForm.value.currency || 'USD';

    const createDto: CreateSaleDto = {
      items: saleItems,
      discountAmount: discountAmount > 0 ? discountAmount : undefined,
      currency: discountAmount > 0 ? currency : undefined
    };

    this.saleService.createSale(createDto)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.createdSaleId.set(response.id);
          this.saleCreated.set(true);
          this.submitting.set(false);
          this.clearCart();
        },
        error: (error) => {
          this.error.set(error.message || 'Failed to create sale');
          this.submitting.set(false);
          console.error('Error creating sale:', error);
        }
      });
  }

  completeSale(): void {
    const saleId = this.createdSaleId();
    if (!saleId) return;

    this.submitting.set(true);
    this.error.set(null);

    const completeDto: CompleteSaleDto = {
      saleId,
      currency: 'USD'
    };

    this.saleService.completeSale(completeDto)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.submitting.set(false);
          this.router.navigate(['/sales', saleId, 'view']);
        },
        error: (error) => {
          this.error.set(error.message || 'Failed to complete sale');
          this.submitting.set(false);
          console.error('Error completing sale:', error);
        }
      });
  }

  startNewSale(): void {
    this.saleCreated.set(false);
    this.createdSaleId.set(null);
    this.clearCart();
    this.error.set(null);
  }

  // Utility methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  getInventoryItem(productId: number): ViewInventoryItemDto | undefined {
    return this.inventory()?.items.find(item => item.productId === productId);
  }

  isFieldInvalid(formGroup: FormGroup, fieldName: string): boolean {
    const field = formGroup.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(formGroup: FormGroup, fieldName: string): string {
    const field = formGroup.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['min']) return `${fieldName} must be at least ${field.errors['min'].min}`;
    }
    return '';
  }
}
