import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-sale-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Sale Details</h1>
          <p class="page-description">View detailed sale information</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">🔍</div>
          <h2 class="placeholder-title">Sale Detail View</h2>
          <p class="placeholder-description">
            This page will display comprehensive sale transaction details and related information.
          </p>
          <div class="placeholder-actions">
            <a routerLink="/sales" class="btn btn-secondary">← Back to Sales</a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class SaleDetailComponent {}
