import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-category-create',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Create Category</h1>
          <p class="page-description">Add a new product category</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">➕</div>
          <h2 class="placeholder-title">Category Creation Form</h2>
          <p class="placeholder-description">
            This page will contain a form for creating new product categories.
          </p>
          <div class="placeholder-actions">
            <a routerLink="/categories" class="btn btn-secondary">← Back to Categories</a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class CategoryCreateComponent {}
