import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { LoadingSpinnerComponent } from '../../../components/ui/loading-spinner/loading-spinner.component';
import { CategoryService } from '../../../services/category.service';
import { ProductService } from '../../../services/product.service';
import { ProductCategoryDto, PatchProductCategoryDto, ViewProductDto } from '../../../models';

@Component({
  selector: 'app-enhanced-category-list',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, LoadingSpinnerComponent],
  templateUrl: './enhanced-category-list.component.html',
  styleUrl: './enhanced-category-list.component.css'
})
export class EnhancedCategoryListComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  loading = signal(true);
  submitting = signal(false);
  error = signal<string | null>(null);
  categories = signal<ProductCategoryDto[]>([]);
  editingCategory = signal<ProductCategoryDto | null>(null);
  showCreateForm = signal(false);
  productCounts = signal<Map<number, number>>(new Map());

  createForm: FormGroup;
  editForm: FormGroup;

  // Computed values
  hasCategories = computed(() => this.categories().length > 0);
  totalCategories = computed(() => this.categories().length);

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private categoryService: CategoryService,
    private productService: ProductService
  ) {
    this.createForm = this.createCreateForm();
    this.editForm = this.createEditForm();
  }

  ngOnInit(): void {
    this.loadCategories();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createCreateForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]]
    });
  }

  private createEditForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]]
    });
  }

  private loadCategories(): void {
    this.loading.set(true);
    this.error.set(null);

    this.categoryService.getCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (categories) => {
          this.categories.set(categories);
          this.loadProductCounts(categories);
          this.loading.set(false);
        },
        error: (error) => {
          this.error.set(error.message || 'Failed to load categories');
          this.loading.set(false);
          console.error('Error loading categories:', error);
        }
      });
  }

  private loadProductCounts(categories: ProductCategoryDto[]): void {
    // Load product counts for each category
    categories.forEach(category => {
      this.productService.getProductsByCategory(category.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (products) => {
            const currentCounts = this.productCounts();
            currentCounts.set(category.id, products.length);
            this.productCounts.set(new Map(currentCounts));
          },
          error: (error) => {
            console.error(`Error loading product count for category ${category.id}:`, error);
          }
        });
    });
  }

  // Create category methods
  showCreateFormToggle(): void {
    this.showCreateForm.set(!this.showCreateForm());
    if (this.showCreateForm()) {
      this.createForm.reset();
      this.error.set(null);
    }
  }

  onCreateSubmit(): void {
    if (this.createForm.valid) {
      this.submitting.set(true);
      this.error.set(null);

      const name = this.createForm.value.name.trim();
      
      this.categoryService.createCategory(name)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (category) => {
            this.submitting.set(false);
            this.showCreateForm.set(false);
            this.createForm.reset();
            this.loadCategories(); // Reload to get updated list
          },
          error: (error) => {
            this.error.set(error.message || 'Failed to create category');
            this.submitting.set(false);
            console.error('Error creating category:', error);
          }
        });
    } else {
      this.markFormGroupTouched(this.createForm);
    }
  }

  // Edit category methods
  startEdit(category: ProductCategoryDto): void {
    this.editingCategory.set(category);
    this.editForm.patchValue({ name: category.name });
    this.error.set(null);
  }

  cancelEdit(): void {
    this.editingCategory.set(null);
    this.editForm.reset();
    this.error.set(null);
  }

  onEditSubmit(): void {
    const category = this.editingCategory();
    if (this.editForm.valid && category) {
      this.submitting.set(true);
      this.error.set(null);

      const patchDto: PatchProductCategoryDto = {
        id: category.id,
        name: this.editForm.value.name.trim()
      };

      this.categoryService.patchCategory(patchDto)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.submitting.set(false);
            this.editingCategory.set(null);
            this.editForm.reset();
            this.loadCategories(); // Reload to get updated list
          },
          error: (error) => {
            this.error.set(error.message || 'Failed to update category');
            this.submitting.set(false);
            console.error('Error updating category:', error);
          }
        });
    } else {
      this.markFormGroupTouched(this.editForm);
    }
  }

  // Delete category
  deleteCategory(category: ProductCategoryDto): void {
    const productCount = this.productCounts().get(category.id) || 0;
    
    if (productCount > 0) {
      alert(`Cannot delete category "${category.name}" because it contains ${productCount} product(s). Please move or delete the products first.`);
      return;
    }

    if (confirm(`Are you sure you want to delete the category "${category.name}"?`)) {
      this.submitting.set(true);
      this.error.set(null);

      this.categoryService.deleteCategory(category.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.submitting.set(false);
            this.loadCategories(); // Reload to get updated list
          },
          error: (error) => {
            this.error.set(error.message || 'Failed to delete category');
            this.submitting.set(false);
            console.error('Error deleting category:', error);
          }
        });
    }
  }

  // Navigation methods
  viewCategoryProducts(category: ProductCategoryDto): void {
    this.router.navigate(['/products'], { 
      queryParams: { categoryId: category.id } 
    });
  }

  // Utility methods
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  isFieldInvalid(formGroup: FormGroup, fieldName: string): boolean {
    const field = formGroup.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(formGroup: FormGroup, fieldName: string): string {
    const field = formGroup.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return 'Category name is required';
      }
      if (field.errors['minlength']) {
        return `Category name must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors['maxlength']) {
        return `Category name cannot exceed ${field.errors['maxlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  getProductCount(categoryId: number): number {
    return this.productCounts().get(categoryId) || 0;
  }


  getTotalProductCount(): number {
    let total = 0;
    this.productCounts().forEach((count: number) => total += count);
    return total;
  }

  refreshData(): void {
    this.loadCategories();
  }
}


