import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-category-list',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Categories</h1>
          <p class="page-description">Organize your products by categories</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">📂</div>
          <h2 class="placeholder-title">Category Management</h2>
          <p class="placeholder-description">
            This page will contain category management features including listing, creating, and editing categories.
          </p>
          <div class="placeholder-actions">
            <a routerLink="/categories/create" class="btn btn-primary">
              <span>➕</span> Create Category
            </a>
            <a routerLink="/dashboard" class="btn btn-secondary">
              <span>🏠</span> Back to Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class CategoryListComponent {}
