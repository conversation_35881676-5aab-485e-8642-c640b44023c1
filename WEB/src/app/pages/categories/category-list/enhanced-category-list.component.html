<div class="page-container">
  <div class="container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">Categories</h1>
        <p class="page-description">Organize your products by categories</p>
      </div>
      <div class="header-actions">
        <button 
          class="btn btn-primary" 
          (click)="showCreateFormToggle()"
          [disabled]="submitting()">
          <span class="btn-icon">{{ showCreateForm() ? '×' : '➕' }}</span>
          {{ showCreateForm() ? 'Cancel' : 'Add Category' }}
        </button>
        <button 
          class="btn btn-outline-primary" 
          (click)="refreshData()"
          [disabled]="loading()">
          <span class="btn-icon">🔄</span>
          Refresh
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading()" class="loading-container">
      <app-loading-spinner></app-loading-spinner>
      <p class="loading-text">Loading categories...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error() && !loading()" class="alert alert-danger">
      <h4>Error Loading Categories</h4>
      <p>{{ error() }}</p>
      <button class="btn btn-danger" (click)="refreshData()">Try Again</button>
    </div>

    <!-- Create Category Form -->
    <div *ngIf="showCreateForm() && !loading()" class="create-form-container">
      <div class="form-card">
        <div class="form-header">
          <h3 class="form-title">Create New Category</h3>
        </div>
        <form [formGroup]="createForm" (ngSubmit)="onCreateSubmit()" class="category-form">
          <div class="form-group">
            <label for="createName" class="form-label required">Category Name</label>
            <input
              type="text"
              id="createName"
              formControlName="name"
              class="form-control"
              [class.is-invalid]="isFieldInvalid(createForm, 'name')"
              placeholder="Enter category name"
              maxlength="50">
            <div *ngIf="isFieldInvalid(createForm, 'name')" class="invalid-feedback">
              {{ getFieldError(createForm, 'name') }}
            </div>
          </div>
          
          <div class="form-actions">
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="submitting() || createForm.invalid">
              <span *ngIf="submitting()" class="btn-spinner">⏳</span>
              <span class="btn-text">{{ submitting() ? 'Creating...' : 'Create Category' }}</span>
            </button>
            <button
              type="button"
              class="btn btn-secondary"
              (click)="showCreateFormToggle()"
              [disabled]="submitting()">
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Categories Content -->
    <div *ngIf="!loading()" class="categories-content">
      
      <!-- Stats Summary -->
      <div class="stats-summary">
        <div class="stat-item">
          <div class="stat-value">{{ totalCategories() }}</div>
          <div class="stat-label">Total Categories</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ getTotalProductCount() }}</div>
          <div class="stat-label">Total Products</div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="!hasCategories()" class="empty-state">
        <div class="empty-icon">📂</div>
        <h2 class="empty-title">No Categories Found</h2>
        <p class="empty-description">
          Start organizing your products by creating your first category.
        </p>
        <button class="btn btn-primary" (click)="showCreateFormToggle()">
          <span class="btn-icon">➕</span>
          Create First Category
        </button>
      </div>

      <!-- Categories Grid -->
      <div *ngIf="hasCategories()" class="categories-grid">
        <div *ngFor="let category of categories()" class="category-card">
          
          <!-- Category Header -->
          <div class="category-header">
            <div class="category-info">
              <h3 class="category-name">{{ category.name }}</h3>
              <div class="category-stats">
                <span class="product-count">
                  {{ getProductCount(category.id) }} product{{ getProductCount(category.id) !== 1 ? 's' : '' }}
                </span>
              </div>
            </div>
            <div class="category-actions">
              <button
                class="btn btn-sm btn-outline-primary"
                (click)="startEdit(category)"
                [disabled]="submitting() || editingCategory()?.id === category.id"
                title="Edit category">
                ✏️
              </button>
              <button
                class="btn btn-sm btn-outline-danger"
                (click)="deleteCategory(category)"
                [disabled]="submitting() || getProductCount(category.id) > 0"
                title="Delete category">
                🗑️
              </button>
            </div>
          </div>

          <!-- Edit Form -->
          <div *ngIf="editingCategory()?.id === category.id" class="edit-form">
            <form [formGroup]="editForm" (ngSubmit)="onEditSubmit()">
              <div class="form-group">
                <input
                  type="text"
                  formControlName="name"
                  class="form-control"
                  [class.is-invalid]="isFieldInvalid(editForm, 'name')"
                  placeholder="Category name"
                  maxlength="50">
                <div *ngIf="isFieldInvalid(editForm, 'name')" class="invalid-feedback">
                  {{ getFieldError(editForm, 'name') }}
                </div>
              </div>
              <div class="form-actions">
                <button
                  type="submit"
                  class="btn btn-sm btn-primary"
                  [disabled]="submitting() || editForm.invalid">
                  <span *ngIf="submitting()" class="btn-spinner">⏳</span>
                  Save
                </button>
                <button
                  type="button"
                  class="btn btn-sm btn-secondary"
                  (click)="cancelEdit()"
                  [disabled]="submitting()">
                  Cancel
                </button>
              </div>
            </form>
          </div>

          <!-- Category Content -->
          <div *ngIf="editingCategory()?.id !== category.id" class="category-content">
            <div class="category-description">
              <p>Manage products in the {{ category.name }} category</p>
            </div>
            
            <div class="category-quick-actions">
              <button
                class="btn btn-outline-primary btn-sm"
                (click)="viewCategoryProducts(category)"
                [disabled]="getProductCount(category.id) === 0">
                <span class="btn-icon">👁️</span>
                View Products ({{ getProductCount(category.id) }})
              </button>
              
              <a
                routerLink="/products/create"
                [queryParams]="{categoryId: category.id}"
                class="btn btn-outline-success btn-sm">
                <span class="btn-icon">➕</span>
                Add Product
              </a>
            </div>

            <!-- Product Count Warning -->
            <div *ngIf="getProductCount(category.id) > 0" class="category-warning">
              <span class="warning-icon">⚠️</span>
              <span class="warning-text">
                This category contains {{ getProductCount(category.id) }} product(s) and cannot be deleted.
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div *ngIf="hasCategories()" class="quick-actions-section">
        <h2 class="section-title">Quick Actions</h2>
        <div class="quick-actions-grid">
          <a routerLink="/products/create" class="quick-action-card">
            <div class="action-icon">📦</div>
            <div class="action-content">
              <h3 class="action-title">Create Product</h3>
              <p class="action-description">Add a new product to a category</p>
            </div>
          </a>
          
          <a routerLink="/products" class="quick-action-card">
            <div class="action-icon">📋</div>
            <div class="action-content">
              <h3 class="action-title">View All Products</h3>
              <p class="action-description">Browse products by category</p>
            </div>
          </a>
          
          <a routerLink="/tags" class="quick-action-card">
            <div class="action-icon">🏷️</div>
            <div class="action-content">
              <h3 class="action-title">Manage Tags</h3>
              <p class="action-description">Organize products with tags</p>
            </div>
          </a>
        </div>
      </div>

    </div>
  </div>
</div>
