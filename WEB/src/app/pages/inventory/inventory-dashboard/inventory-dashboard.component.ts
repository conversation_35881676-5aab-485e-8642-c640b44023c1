import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-inventory-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Inventory Dashboard</h1>
          <p class="page-description">Monitor and manage your inventory levels</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">📋</div>
          <h2 class="placeholder-title">Inventory Management System</h2>
          <p class="placeholder-description">
            This dashboard will provide comprehensive inventory management including stock levels, 
            low stock alerts, inventory history, and reporting.
          </p>
          <ul class="placeholder-features">
            <li>Real-time stock level monitoring</li>
            <li>Low stock alerts and notifications</li>
            <li>Inventory movement tracking</li>
            <li>Waste and damage recording</li>
            <li>Monthly inventory reports</li>
          </ul>
          <div class="placeholder-actions">
            <a routerLink="/inventory/current" class="btn btn-primary">
              <span>📦</span> Current Inventory
            </a>
            <a routerLink="/dashboard" class="btn btn-secondary">
              <span>🏠</span> Back to Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class InventoryDashboardComponent {}
