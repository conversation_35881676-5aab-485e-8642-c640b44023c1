<div class="page-container">
  <div class="container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">Current Inventory</h1>
        <p class="page-description">View and manage current stock levels</p>
      </div>
      <div class="header-actions">
        <button 
          class="btn btn-primary" 
          (click)="showAddItemFormToggle()"
          [disabled]="submitting()">
          <span class="btn-icon">{{ showAddItemForm() ? '×' : '➕' }}</span>
          {{ showAddItemForm() ? 'Cancel' : 'Add Item' }}
        </button>
        <button 
          class="btn btn-outline-primary" 
          (click)="refreshData()"
          [disabled]="loading()">
          <span class="btn-icon">🔄</span>
          Refresh
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading()" class="loading-container">
      <app-loading-spinner></app-loading-spinner>
      <p class="loading-text">Loading inventory...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error() && !loading()" class="alert alert-danger">
      <h4>Error Loading Inventory</h4>
      <p>{{ error() }}</p>
      <button class="btn btn-danger" (click)="refreshData()">Try Again</button>
    </div>

    <!-- Inventory Content -->
    <div *ngIf="hasInventory() && !loading()" class="inventory-content">
      
      <!-- Inventory Summary -->
      <div class="inventory-summary">
        <div class="summary-card">
          <div class="summary-icon">📦</div>
          <div class="summary-content">
            <div class="summary-value">{{ totalItems() }}</div>
            <div class="summary-label">Total Items</div>
          </div>
        </div>
        
        <div class="summary-card warning" *ngIf="lowStockItems() > 0">
          <div class="summary-icon">⚠️</div>
          <div class="summary-content">
            <div class="summary-value">{{ lowStockItems() }}</div>
            <div class="summary-label">Low Stock</div>
          </div>
        </div>
        
        <div class="summary-card danger" *ngIf="outOfStockItems() > 0">
          <div class="summary-icon">❌</div>
          <div class="summary-content">
            <div class="summary-value">{{ outOfStockItems() }}</div>
            <div class="summary-label">Out of Stock</div>
          </div>
        </div>
        
        <div class="summary-card success">
          <div class="summary-icon">💰</div>
          <div class="summary-content">
            <div class="summary-value">{{ formatCurrency(totalValue()) }}</div>
            <div class="summary-label">Total Value</div>
          </div>
        </div>
      </div>

      <!-- Filters and Search -->
      <div class="filters-section">
        <div class="search-group">
          <input
            type="text"
            class="form-control"
            placeholder="Search products..."
            [value]="searchTerm()"
            (input)="onSearchChange($event)">
        </div>
        
        <div class="filter-group">
          <select
            class="form-control"
            [value]="statusFilter()"
            (change)="onStatusFilterChange($event)">
            <option value="all">All Status</option>
            <option value="available">Available</option>
            <option value="lowstock">Low Stock</option>
            <option value="outofstock">Out of Stock</option>
          </select>
        </div>
        
        <button class="btn btn-outline-secondary" (click)="clearFilters()">
          Clear Filters
        </button>
      </div>

      <!-- Empty State -->
      <div *ngIf="filteredItems().length === 0 && !loading()" class="empty-state">
        <div class="empty-icon">📦</div>
        <h2 class="empty-title">No Inventory Items Found</h2>
        <p class="empty-description">
          {{ searchTerm() || statusFilter() !== 'all' ? 'No items match your current filters.' : 'Start by adding items to your inventory.' }}
        </p>
        <div class="empty-actions">
          <button *ngIf="searchTerm() || statusFilter() !== 'all'" class="btn btn-secondary" (click)="clearFilters()">
            Clear Filters
          </button>
          <button class="btn btn-primary" (click)="showAddItemFormToggle()">
            <span class="btn-icon">➕</span>
            Add First Item
          </button>
        </div>
      </div>

      <!-- Inventory Items Table -->
      <div *ngIf="filteredItems().length > 0" class="inventory-table-container">
        <table class="inventory-table">
          <thead>
            <tr>
              <th>Product</th>
              <th>Available</th>
              <th>Total</th>
              <th>Reserved</th>
              <th>Threshold</th>
              <th>Status</th>
              <th>Actual Price</th>
              <th>Selling Price</th>
              <th>Value</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of filteredItems()" class="inventory-row">
              <td class="product-cell">
                <div class="product-info">
                  <div class="product-name">{{ item.productName }}</div>
                  <div class="product-id">ID: {{ item.productId }}</div>
                </div>
              </td>
              <td class="quantity-cell">
                <span class="quantity-value" [class.low-stock]="item.availableQuantity <= item.threshold">
                  {{ item.availableQuantity }}
                </span>
              </td>
              <td class="quantity-cell">{{ item.quantity }}</td>
              <td class="quantity-cell">{{ item.reservedQuantity }}</td>
              <td class="threshold-cell">{{ item.threshold }}</td>
              <td class="status-cell">
                <span class="badge" [ngClass]="getStatusClass(item.status)">
                  {{ item.status }}
                </span>
              </td>
              <td class="price-cell">{{ formatCurrency(item.actualPrice) }}</td>
              <td class="price-cell">{{ formatCurrency(item.sellingPrice) }}</td>
              <td class="value-cell">{{ formatCurrency(item.sellingPrice * item.availableQuantity) }}</td>
              <td class="actions-cell">
                <button
                  class="btn btn-sm btn-outline-warning"
                  (click)="showWasteFormFor(item)"
                  [disabled]="item.availableQuantity === 0 || submitting()"
                  title="Record waste">
                  🗑️
                </button>
                <a
                  routerLink="/products"
                  [queryParams]="{id: item.productId}"
                  class="btn btn-sm btn-outline-info"
                  title="View product">
                  👁️
                </a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

    </div>

    <!-- No Inventory State -->
    <div *ngIf="!hasInventory() && !loading() && !error()" class="empty-state">
      <div class="empty-icon">📦</div>
      <h2 class="empty-title">No Current Inventory</h2>
      <p class="empty-description">
        There is no current inventory record. Create one to start managing your stock.
      </p>
      <a routerLink="/inventory/dashboard" class="btn btn-primary">
        <span class="btn-icon">📋</span>
        Go to Inventory Dashboard
      </a>
    </div>

    <!-- Waste Form Modal -->
    <div *ngIf="showWasteForm()" class="modal-overlay" (click)="hideWasteForm()">
      <div class="modal-content" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h3 class="modal-title">Record Waste for {{ selectedItem()?.productName }}</h3>
          <button class="modal-close" (click)="hideWasteForm()">×</button>
        </div>
        <form [formGroup]="wasteForm" (ngSubmit)="onWasteSubmit()" class="waste-form">
          <div class="form-group">
            <label for="wasteQuantity" class="form-label required">Quantity to Waste</label>
            <input
              type="number"
              id="wasteQuantity"
              formControlName="quantity"
              class="form-control"
              [class.is-invalid]="isFieldInvalid(wasteForm, 'quantity')"
              [max]="selectedItem()?.availableQuantity || 0"
              min="1">
            <div class="form-help">
              Available: {{ selectedItem()?.availableQuantity }} units
            </div>
            <div *ngIf="isFieldInvalid(wasteForm, 'quantity')" class="invalid-feedback">
              {{ getFieldError(wasteForm, 'quantity') }}
            </div>
          </div>
          
          <div class="form-group">
            <label for="wasteReason" class="form-label required">Reason</label>
            <textarea
              id="wasteReason"
              formControlName="reason"
              class="form-control"
              [class.is-invalid]="isFieldInvalid(wasteForm, 'reason')"
              rows="3"
              placeholder="Describe the reason for waste...">
            </textarea>
            <div *ngIf="isFieldInvalid(wasteForm, 'reason')" class="invalid-feedback">
              {{ getFieldError(wasteForm, 'reason') }}
            </div>
          </div>
          
          <div class="modal-actions">
            <button
              type="submit"
              class="btn btn-warning"
              [disabled]="submitting() || wasteForm.invalid">
              <span *ngIf="submitting()" class="btn-spinner">⏳</span>
              <span class="btn-text">{{ submitting() ? 'Recording...' : 'Record Waste' }}</span>
            </button>
            <button
              type="button"
              class="btn btn-secondary"
              (click)="hideWasteForm()"
              [disabled]="submitting()">
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>

  </div>
</div>
