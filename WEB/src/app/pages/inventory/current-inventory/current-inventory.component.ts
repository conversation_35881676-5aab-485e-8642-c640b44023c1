import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-current-inventory',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Current Inventory</h1>
          <p class="page-description">View and manage current stock levels</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">📦</div>
          <h2 class="placeholder-title">Current Stock Overview</h2>
          <p class="placeholder-description">
            This page will display current inventory levels, stock status, and management tools.
          </p>
          <div class="placeholder-actions">
            <a routerLink="/inventory" class="btn btn-secondary">← Back to Inventory</a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class CurrentInventoryComponent {}
