import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { LoadingSpinnerComponent } from '../../../components/ui/loading-spinner/loading-spinner.component';
import { InventoryService } from '../../../services/inventory.service';
import { ProductService } from '../../../services/product.service';
import { ViewInventoryDto, ViewInventoryItemDto, AddInventoryItemDto, WasteInventoryItemDto, ViewProductDto, InventoryItemStatus } from '../../../models';

@Component({
  selector: 'app-enhanced-current-inventory',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, LoadingSpinnerComponent],
  templateUrl: './enhanced-current-inventory.component.html',
  styleUrl: './enhanced-current-inventory.component.css'
})
export class EnhancedCurrentInventoryComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  loading = signal(true);
  submitting = signal(false);
  error = signal<string | null>(null);
  inventory = signal<ViewInventoryDto | null>(null);
  products = signal<ViewProductDto[]>([]);
  showAddItemForm = signal(false);
  showWasteForm = signal(false);
  selectedItem = signal<ViewInventoryItemDto | null>(null);
  searchTerm = signal('');
  statusFilter = signal<string>('all');

  addItemForm: FormGroup;
  wasteForm: FormGroup;

  // Computed values
  hasInventory = computed(() => this.inventory() !== null);
  inventoryItems = computed(() => this.inventory()?.items || []);
  filteredItems = computed(() => {
    const items = this.inventoryItems();
    const search = this.searchTerm().toLowerCase();
    const status = this.statusFilter();
    
    return items.filter(item => {
      const matchesSearch = !search || 
        item.productName.toLowerCase().includes(search) ||
        item.productId.toString().includes(search);
      
      const matchesStatus = status === 'all' || 
        item.status.toLowerCase() === status.toLowerCase();
      
      return matchesSearch && matchesStatus;
    });
  });
  
  totalItems = computed(() => this.inventoryItems().length);
  lowStockItems = computed(() => 
    this.inventoryItems().filter(item => 
      item.status === InventoryItemStatus.LowStock || 
      item.availableQuantity <= item.threshold
    ).length
  );
  outOfStockItems = computed(() => 
    this.inventoryItems().filter(item => 
      item.status === InventoryItemStatus.OutOfStock || 
      item.availableQuantity === 0
    ).length
  );
  totalValue = computed(() => 
    this.inventoryItems().reduce((sum, item) => 
      sum + (item.sellingPrice * item.availableQuantity), 0
    )
  );

  constructor(
    private fb: FormBuilder,
    private inventoryService: InventoryService,
    private productService: ProductService
  ) {
    this.addItemForm = this.createAddItemForm();
    this.wasteForm = this.createWasteForm();
  }

  ngOnInit(): void {
    this.loadInventory();
    this.loadProducts();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createAddItemForm(): FormGroup {
    return this.fb.group({
      productId: ['', [Validators.required]],
      quantity: ['', [Validators.required, Validators.min(1)]],
      actualPrice: ['', [Validators.required, Validators.min(0.01)]],
      sellingPrice: ['', [Validators.required, Validators.min(0.01)]],
      currency: ['USD', [Validators.required]],
      threshold: ['', [Validators.required, Validators.min(0)]]
    });
  }

  private createWasteForm(): FormGroup {
    return this.fb.group({
      quantity: ['', [Validators.required, Validators.min(1)]],
      reason: ['', [Validators.required, Validators.minLength(5)]]
    });
  }

  private loadInventory(): void {
    this.loading.set(true);
    this.error.set(null);

    this.inventoryService.getCurrentInventory()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (inventory) => {
          this.inventory.set(inventory);
          this.loading.set(false);
        },
        error: (error) => {
          this.error.set(error.message || 'Failed to load inventory');
          this.loading.set(false);
          console.error('Error loading inventory:', error);
        }
      });
  }

  private loadProducts(): void {
    this.productService.getProducts()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (products) => {
          this.products.set(products);
        },
        error: (error) => {
          console.error('Error loading products:', error);
        }
      });
  }

  // Add item methods
  showAddItemFormToggle(): void {
    this.showAddItemForm.set(!this.showAddItemForm());
    if (this.showAddItemForm()) {
      this.addItemForm.reset({ currency: 'USD' });
      this.error.set(null);
    }
  }

  onAddItemSubmit(): void {
    if (this.addItemForm.valid) {
      this.submitting.set(true);
      this.error.set(null);

      const formValue = this.addItemForm.value;
      const addDto: AddInventoryItemDto = {
        productId: parseInt(formValue.productId),
        quantity: parseInt(formValue.quantity),
        actualPrice: parseFloat(formValue.actualPrice),
        sellingPrice: parseFloat(formValue.sellingPrice),
        currency: formValue.currency,
        threshold: parseInt(formValue.threshold)
      };

      this.inventoryService.addItemToInventory(addDto)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.submitting.set(false);
            this.showAddItemForm.set(false);
            this.addItemForm.reset({ currency: 'USD' });
            this.loadInventory(); // Reload inventory
          },
          error: (error) => {
            this.error.set(error.message || 'Failed to add item to inventory');
            this.submitting.set(false);
            console.error('Error adding item:', error);
          }
        });
    } else {
      this.markFormGroupTouched(this.addItemForm);
    }
  }

  // Waste item methods
  showWasteFormFor(item: ViewInventoryItemDto): void {
    this.selectedItem.set(item);
    this.showWasteForm.set(true);
    this.wasteForm.reset();
    this.wasteForm.patchValue({
      quantity: Math.min(1, item.availableQuantity)
    });
    this.error.set(null);
  }

  hideWasteForm(): void {
    this.showWasteForm.set(false);
    this.selectedItem.set(null);
    this.wasteForm.reset();
  }

  onWasteSubmit(): void {
    const item = this.selectedItem();
    if (this.wasteForm.valid && item) {
      this.submitting.set(true);
      this.error.set(null);

      const formValue = this.wasteForm.value;
      const wasteDto: WasteInventoryItemDto = {
        productId: item.productId,
        quantity: parseInt(formValue.quantity),
        reason: formValue.reason.trim()
      };

      this.inventoryService.wasteInventoryItem(wasteDto)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.submitting.set(false);
            this.hideWasteForm();
            this.loadInventory(); // Reload inventory
          },
          error: (error) => {
            this.error.set(error.message || 'Failed to record waste');
            this.submitting.set(false);
            console.error('Error recording waste:', error);
          }
        });
    } else {
      this.markFormGroupTouched(this.wasteForm);
    }
  }

  // Filter and search methods
  onSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm.set(target.value);
  }

  onStatusFilterChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.statusFilter.set(target.value);
  }

  clearFilters(): void {
    this.searchTerm.set('');
    this.statusFilter.set('all');
  }

  // Utility methods
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  isFieldInvalid(formGroup: FormGroup, fieldName: string): boolean {
    const field = formGroup.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(formGroup: FormGroup, fieldName: string): string {
    const field = formGroup.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['min']) return `${fieldName} must be at least ${field.errors['min'].min}`;
      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
    }
    return '';
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'available': return 'badge-success';
      case 'lowstock': return 'badge-warning';
      case 'outofstock': return 'badge-danger';
      default: return 'badge-secondary';
    }
  }

  getAvailableProducts(): ViewProductDto[] {
    const inventoryProductIds = this.inventoryItems().map(item => item.productId);
    return this.products().filter(product => !inventoryProductIds.includes(product.id));
  }

  refreshData(): void {
    this.loadInventory();
  }
}
