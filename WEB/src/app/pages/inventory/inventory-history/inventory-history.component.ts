import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-inventory-history',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Inventory History</h1>
          <p class="page-description">View historical inventory records</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">📊</div>
          <h2 class="placeholder-title">Historical Inventory Data</h2>
          <p class="placeholder-description">
            This page will display historical inventory records and trends.
          </p>
          <div class="placeholder-actions">
            <a routerLink="/inventory" class="btn btn-secondary">← Back to Inventory</a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class InventoryHistoryComponent {}
