import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-tag-create',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Create Tag</h1>
          <p class="page-description">Add a new product tag</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">➕</div>
          <h2 class="placeholder-title">Tag Creation Form</h2>
          <p class="placeholder-description">
            This page will contain a form for creating new product tags.
          </p>
          <div class="placeholder-actions">
            <a routerLink="/tags" class="btn btn-secondary">← Back to Tags</a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class TagCreateComponent {}
