<div class="page-container">
  <div class="container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">Tags</h1>
        <p class="page-description">Manage product tags and labels</p>
      </div>
      <div class="header-actions">
        <button 
          class="btn btn-primary" 
          (click)="showCreateFormToggle()"
          [disabled]="submitting()">
          <span class="btn-icon">{{ showCreateForm() ? '×' : '➕' }}</span>
          {{ showCreateForm() ? 'Cancel' : 'Add Tag' }}
        </button>
        <button 
          class="btn btn-outline-primary" 
          (click)="refreshData()"
          [disabled]="loading()">
          <span class="btn-icon">🔄</span>
          Refresh
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading()" class="loading-container">
      <app-loading-spinner></app-loading-spinner>
      <p class="loading-text">Loading tags...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error() && !loading()" class="alert alert-danger">
      <h4>Error Loading Tags</h4>
      <p>{{ error() }}</p>
      <button class="btn btn-danger" (click)="refreshData()">Try Again</button>
    </div>

    <!-- Create Tag Form -->
    <div *ngIf="showCreateForm() && !loading()" class="create-form-container">
      <div class="form-card">
        <div class="form-header">
          <h3 class="form-title">Create New Tag</h3>
        </div>
        <form [formGroup]="createForm" (ngSubmit)="onCreateSubmit()" class="tag-form">
          <div class="form-group">
            <label for="createName" class="form-label required">Tag Name</label>
            <input
              type="text"
              id="createName"
              formControlName="name"
              class="form-control"
              [class.is-invalid]="isFieldInvalid(createForm, 'name')"
              placeholder="Enter tag name"
              maxlength="30">
            <div class="form-help">
              <span *ngIf="createForm.get('name')?.value" class="slug-preview">
                Slug: {{ generateSlugPreview(createForm.get('name')?.value) }}
              </span>
            </div>
            <div *ngIf="isFieldInvalid(createForm, 'name')" class="invalid-feedback">
              {{ getFieldError(createForm, 'name') }}
            </div>
          </div>
          
          <div class="form-actions">
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="submitting() || createForm.invalid">
              <span *ngIf="submitting()" class="btn-spinner">⏳</span>
              <span class="btn-text">{{ submitting() ? 'Creating...' : 'Create Tag' }}</span>
            </button>
            <button
              type="button"
              class="btn btn-secondary"
              (click)="showCreateFormToggle()"
              [disabled]="submitting()">
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Tags Content -->
    <div *ngIf="!loading()" class="tags-content">
      
      <!-- Search and Stats -->
      <div class="search-stats-section">
        <div class="search-container">
          <div class="search-input-group">
            <input
              type="text"
              class="form-control"
              placeholder="Search tags..."
              [value]="searchTerm()"
              (input)="onSearchChange($event)">
            <button 
              *ngIf="searchTerm()" 
              class="search-clear-btn"
              (click)="clearSearch()"
              title="Clear search">
              ×
            </button>
          </div>
        </div>
        
        <div class="stats-summary">
          <div class="stat-item">
            <div class="stat-value">{{ filteredCount() }}</div>
            <div class="stat-label">{{ searchTerm() ? 'Found' : 'Total' }} Tags</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ getTotalProductCount() }}</div>
            <div class="stat-label">Tagged Products</div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="!hasTags()" class="empty-state">
        <div class="empty-icon">🏷️</div>
        <h2 class="empty-title">No Tags Found</h2>
        <p class="empty-description">
          Start organizing your products by creating your first tag.
        </p>
        <button class="btn btn-primary" (click)="showCreateFormToggle()">
          <span class="btn-icon">➕</span>
          Create First Tag
        </button>
      </div>

      <!-- No Search Results -->
      <div *ngIf="hasTags() && filteredCount() === 0" class="empty-state">
        <div class="empty-icon">🔍</div>
        <h2 class="empty-title">No Tags Match Your Search</h2>
        <p class="empty-description">
          Try adjusting your search terms or create a new tag.
        </p>
        <div class="empty-actions">
          <button class="btn btn-secondary" (click)="clearSearch()">
            Clear Search
          </button>
          <button class="btn btn-primary" (click)="showCreateFormToggle()">
            <span class="btn-icon">➕</span>
            Create New Tag
          </button>
        </div>
      </div>

      <!-- Tags Grid -->
      <div *ngIf="filteredCount() > 0" class="tags-grid">
        <div *ngFor="let tag of filteredTags()" class="tag-card">
          
          <!-- Tag Header -->
          <div class="tag-header">
            <div class="tag-info">
              <h3 class="tag-name">{{ tag.name }}</h3>
              <div class="tag-slug">{{ tag.slug }}</div>
              <div class="tag-stats">
                <span class="product-count">
                  {{ getProductCount(tag.id) }} product{{ getProductCount(tag.id) !== 1 ? 's' : '' }}
                </span>
              </div>
            </div>
            <div class="tag-actions">
              <button
                class="btn btn-sm btn-outline-primary"
                (click)="startEdit(tag)"
                [disabled]="submitting() || editingTag()?.id === tag.id"
                title="Edit tag">
                ✏️
              </button>
              <button
                class="btn btn-sm btn-outline-danger"
                (click)="deleteTag(tag)"
                [disabled]="submitting() || getProductCount(tag.id) > 0"
                title="Delete tag">
                🗑️
              </button>
            </div>
          </div>

          <!-- Edit Form -->
          <div *ngIf="editingTag()?.id === tag.id" class="edit-form">
            <form [formGroup]="editForm" (ngSubmit)="onEditSubmit()">
              <div class="form-group">
                <input
                  type="text"
                  formControlName="name"
                  class="form-control"
                  [class.is-invalid]="isFieldInvalid(editForm, 'name')"
                  placeholder="Tag name"
                  maxlength="30">
                <div class="form-help">
                  <span *ngIf="editForm.get('name')?.value" class="slug-preview">
                    New slug: {{ generateSlugPreview(editForm.get('name')?.value) }}
                  </span>
                </div>
                <div *ngIf="isFieldInvalid(editForm, 'name')" class="invalid-feedback">
                  {{ getFieldError(editForm, 'name') }}
                </div>
              </div>
              <div class="form-actions">
                <button
                  type="submit"
                  class="btn btn-sm btn-primary"
                  [disabled]="submitting() || editForm.invalid">
                  <span *ngIf="submitting()" class="btn-spinner">⏳</span>
                  Save
                </button>
                <button
                  type="button"
                  class="btn btn-sm btn-secondary"
                  (click)="cancelEdit()"
                  [disabled]="submitting()">
                  Cancel
                </button>
              </div>
            </form>
          </div>

          <!-- Tag Content -->
          <div *ngIf="editingTag()?.id !== tag.id" class="tag-content">
            <div class="tag-description">
              <p>Organize products with the {{ tag.name }} tag</p>
            </div>
            
            <div class="tag-quick-actions">
              <button
                class="btn btn-outline-primary btn-sm"
                (click)="viewTagProducts(tag)"
                [disabled]="getProductCount(tag.id) === 0">
                <span class="btn-icon">👁️</span>
                View Products ({{ getProductCount(tag.id) }})
              </button>
              
              <a
                routerLink="/products/create"
                [queryParams]="{tagId: tag.id}"
                class="btn btn-outline-success btn-sm">
                <span class="btn-icon">➕</span>
                Add Product
              </a>
            </div>

            <!-- Usage Warning -->
            <div *ngIf="getProductCount(tag.id) > 0" class="tag-warning">
              <span class="warning-icon">⚠️</span>
              <span class="warning-text">
                This tag is used by {{ getProductCount(tag.id) }} product(s) and cannot be deleted.
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div *ngIf="hasTags()" class="quick-actions-section">
        <h2 class="section-title">Quick Actions</h2>
        <div class="quick-actions-grid">
          <a routerLink="/products/create" class="quick-action-card">
            <div class="action-icon">📦</div>
            <div class="action-content">
              <h3 class="action-title">Create Product</h3>
              <p class="action-description">Add a new product with tags</p>
            </div>
          </a>
          
          <a routerLink="/products" class="quick-action-card">
            <div class="action-icon">📋</div>
            <div class="action-content">
              <h3 class="action-title">View All Products</h3>
              <p class="action-description">Browse products by tags</p>
            </div>
          </a>
          
          <a routerLink="/categories" class="quick-action-card">
            <div class="action-icon">📂</div>
            <div class="action-content">
              <h3 class="action-title">Manage Categories</h3>
              <p class="action-description">Organize products with categories</p>
            </div>
          </a>
        </div>
      </div>

    </div>
  </div>
</div>
