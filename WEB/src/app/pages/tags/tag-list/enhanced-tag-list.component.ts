import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { LoadingSpinnerComponent } from '../../../components/ui/loading-spinner/loading-spinner.component';
import { TagService } from '../../../services/tag.service';
import { ProductService } from '../../../services/product.service';
import { TagDto, PatchTagDto, ViewProductDto } from '../../../models';

@Component({
  selector: 'app-enhanced-tag-list',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, LoadingSpinnerComponent],
  templateUrl: './enhanced-tag-list.component.html',
  styleUrl: './enhanced-tag-list.component.css'
})
export class EnhancedTagListComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  loading = signal(true);
  submitting = signal(false);
  error = signal<string | null>(null);
  tags = signal<TagDto[]>([]);
  editingTag = signal<TagDto | null>(null);
  showCreateForm = signal(false);
  productCounts = signal<Map<number, number>>(new Map());
  searchTerm = signal('');

  createForm: FormGroup;
  editForm: FormGroup;

  // Computed values
  filteredTags = computed(() => {
    const tags = this.tags();
    const search = this.searchTerm().toLowerCase();
    
    if (!search) return tags;
    
    return tags.filter(tag => 
      tag.name.toLowerCase().includes(search) ||
      tag.slug.toLowerCase().includes(search)
    );
  });
  
  hasTags = computed(() => this.tags().length > 0);
  totalTags = computed(() => this.tags().length);
  filteredCount = computed(() => this.filteredTags().length);

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private tagService: TagService,
    private productService: ProductService
  ) {
    this.createForm = this.createCreateForm();
    this.editForm = this.createEditForm();
  }

  ngOnInit(): void {
    this.loadTags();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createCreateForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(30)]]
    });
  }

  private createEditForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(30)]]
    });
  }

  private loadTags(): void {
    this.loading.set(true);
    this.error.set(null);

    this.tagService.getTags()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (tags) => {
          this.tags.set(tags);
          this.loadProductCounts(tags);
          this.loading.set(false);
        },
        error: (error) => {
          this.error.set(error.message || 'Failed to load tags');
          this.loading.set(false);
          console.error('Error loading tags:', error);
        }
      });
  }

  private loadProductCounts(tags: TagDto[]): void {
    // Load product counts for each tag
    tags.forEach(tag => {
      this.tagService.getTagsByProduct(tag.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (products) => {
            const currentCounts = this.productCounts();
            currentCounts.set(tag.id, products.length);
            this.productCounts.set(new Map(currentCounts));
          },
          error: (error) => {
            console.error(`Error loading product count for tag ${tag.id}:`, error);
          }
        });
    });
  }

  // Search functionality
  onSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm.set(target.value);
  }

  clearSearch(): void {
    this.searchTerm.set('');
  }

  // Create tag methods
  showCreateFormToggle(): void {
    this.showCreateForm.set(!this.showCreateForm());
    if (this.showCreateForm()) {
      this.createForm.reset();
      this.error.set(null);
    }
  }

  onCreateSubmit(): void {
    if (this.createForm.valid) {
      this.submitting.set(true);
      this.error.set(null);

      const name = this.createForm.value.name.trim();
      
      this.tagService.createTag(name)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (tag) => {
            this.submitting.set(false);
            this.showCreateForm.set(false);
            this.createForm.reset();
            this.loadTags(); // Reload to get updated list
          },
          error: (error) => {
            this.error.set(error.message || 'Failed to create tag');
            this.submitting.set(false);
            console.error('Error creating tag:', error);
          }
        });
    } else {
      this.markFormGroupTouched(this.createForm);
    }
  }

  // Edit tag methods
  startEdit(tag: TagDto): void {
    this.editingTag.set(tag);
    this.editForm.patchValue({ name: tag.name });
    this.error.set(null);
  }

  cancelEdit(): void {
    this.editingTag.set(null);
    this.editForm.reset();
    this.error.set(null);
  }

  onEditSubmit(): void {
    const tag = this.editingTag();
    if (this.editForm.valid && tag) {
      this.submitting.set(true);
      this.error.set(null);

      const patchDto: PatchTagDto = {
        id: tag.id,
        name: this.editForm.value.name.trim()
      };

      this.tagService.patchTag(patchDto)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.submitting.set(false);
            this.editingTag.set(null);
            this.editForm.reset();
            this.loadTags(); // Reload to get updated list
          },
          error: (error) => {
            this.error.set(error.message || 'Failed to update tag');
            this.submitting.set(false);
            console.error('Error updating tag:', error);
          }
        });
    } else {
      this.markFormGroupTouched(this.editForm);
    }
  }

  // Delete tag
  deleteTag(tag: TagDto): void {
    const productCount = this.productCounts().get(tag.id) || 0;
    
    if (productCount > 0) {
      alert(`Cannot delete tag "${tag.name}" because it is used by ${productCount} product(s). Please remove the tag from products first.`);
      return;
    }

    if (confirm(`Are you sure you want to delete the tag "${tag.name}"?`)) {
      this.submitting.set(true);
      this.error.set(null);

      this.tagService.deleteTag(tag.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.submitting.set(false);
            this.loadTags(); // Reload to get updated list
          },
          error: (error) => {
            this.error.set(error.message || 'Failed to delete tag');
            this.submitting.set(false);
            console.error('Error deleting tag:', error);
          }
        });
    }
  }

  // Navigation methods
  viewTagProducts(tag: TagDto): void {
    this.router.navigate(['/products'], { 
      queryParams: { tagId: tag.id } 
    });
  }

  // Utility methods
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  isFieldInvalid(formGroup: FormGroup, fieldName: string): boolean {
    const field = formGroup.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(formGroup: FormGroup, fieldName: string): string {
    const field = formGroup.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return 'Tag name is required';
      }
      if (field.errors['minlength']) {
        return `Tag name must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors['maxlength']) {
        return `Tag name cannot exceed ${field.errors['maxlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  getProductCount(tagId: number): number {
    return this.productCounts().get(tagId) || 0;
  }



  getTotalProductCount(): number {
    let total = 0;
    this.productCounts().forEach((count: number) => total += count);
    return total;
  }

  refreshData(): void {
    this.loadTags();
  }

  generateSlugPreview(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
}
