import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-tag-list',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="page-container">
      <div class="container">
        <div class="page-header">
          <h1 class="page-title">Tags</h1>
          <p class="page-description">Manage product tags and labels</p>
        </div>
        <div class="placeholder-content">
          <div class="placeholder-icon">🏷️</div>
          <h2 class="placeholder-title">Tag Management</h2>
          <p class="placeholder-description">
            This page will contain tag management features for organizing and labeling products.
          </p>
          <div class="placeholder-actions">
            <a routerLink="/tags/create" class="btn btn-primary">
              <span>➕</span> Create Tag
            </a>
            <a routerLink="/dashboard" class="btn btn-secondary">
              <span>🏠</span> Back to Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrl: '../../products/product-list/product-list.component.css'
})
export class TagListComponent {}
