import { Component, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser, Location } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './not-found.component.html',
  styleUrl: './not-found.component.css'
})
export class NotFoundComponent {
  quickLinks = [
    { label: 'Dashboard', route: '/dashboard', icon: '📊' },
    { label: 'Products', route: '/products', icon: '📦' },
    { label: 'Categories', route: '/categories', icon: '📂' },
    { label: 'Inventory', route: '/inventory', icon: '📋' },
    { label: 'Sales', route: '/sales', icon: '💰' }
  ];

  constructor(
    private location: Location,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.location.back();
    }
  }
}
