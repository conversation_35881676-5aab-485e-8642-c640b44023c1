<div class="not-found-container">
  <div class="container">
    <div class="not-found-content">
      <!-- 404 Illustration -->
      <div class="error-illustration">
        <div class="error-code">404</div>
        <div class="error-icon">🔍</div>
      </div>

      <!-- Error Message -->
      <div class="error-message">
        <h1 class="error-title">Page Not Found</h1>
        <p class="error-description">
          Sorry, we couldn't find the page you're looking for. 
          The page might have been moved, deleted, or you entered the wrong URL.
        </p>
      </div>

      <!-- Actions -->
      <div class="error-actions">
        <a routerLink="/dashboard" class="btn btn-primary">
          <span class="btn-icon">🏠</span>
          Go to Dashboard
        </a>
        <button class="btn btn-secondary" (click)="goBack()">
          <span class="btn-icon">←</span>
          Go Back
        </button>
      </div>

      <!-- Quick Links -->
      <div class="quick-links-section">
        <h3 class="quick-links-title">Or try these popular pages:</h3>
        <div class="quick-links-grid">
          <a *ngFor="let link of quickLinks" 
             [routerLink]="link.route" 
             class="quick-link">
            <span class="quick-link-icon">{{ link.icon }}</span>
            <span class="quick-link-label">{{ link.label }}</span>
          </a>
        </div>
      </div>

      <!-- Help Section -->
      <div class="help-section">
        <p class="help-text">
          Still having trouble? 
          <a href="mailto:<EMAIL>" class="help-link">Contact Support</a>
        </p>
      </div>
    </div>
  </div>
</div>
