/* Not Found Page Styles */
.not-found-container {
  min-height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8) 0;
}

.not-found-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

/* Error Illustration */
.error-illustration {
  position: relative;
  margin-bottom: var(--spacing-8);
}

.error-code {
  font-size: 8rem;
  font-weight: var(--font-weight-extrabold);
  color: var(--color-primary-200);
  line-height: 1;
  margin-bottom: var(--spacing-4);
  user-select: none;
}

.error-icon {
  font-size: var(--font-size-6xl);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translate(-50%, -50%) translateY(0px);
  }
  50% {
    transform: translate(-50%, -50%) translateY(-10px);
  }
}

/* Error Message */
.error-message {
  margin-bottom: var(--spacing-8);
}

.error-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-4) 0;
}

.error-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Actions */
.error-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-12);
  flex-wrap: wrap;
}

.btn-icon {
  margin-right: var(--spacing-2);
}

/* Quick Links */
.quick-links-section {
  margin-bottom: var(--spacing-8);
}

.quick-links-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-6) 0;
}

.quick-links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-3);
  max-width: 500px;
  margin: 0 auto;
}

.quick-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius-lg);
  text-decoration: none;
  transition: all var(--transition-base);
  background-color: var(--bg-surface);
}

.quick-link:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  text-decoration: none;
  border-color: var(--interactive-primary);
}

.quick-link-icon {
  font-size: var(--font-size-2xl);
}

.quick-link-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* Help Section */
.help-section {
  padding-top: var(--spacing-8);
  border-top: 1px solid var(--border-primary);
}

.help-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0;
}

.help-link {
  color: var(--interactive-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.help-link:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 767px) {
  .error-code {
    font-size: 6rem;
  }
  
  .error-title {
    font-size: var(--font-size-3xl);
  }
  
  .error-description {
    font-size: var(--font-size-base);
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .quick-links-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}
