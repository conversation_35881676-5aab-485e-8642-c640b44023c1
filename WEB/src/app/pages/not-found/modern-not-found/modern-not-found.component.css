/* Modern Not Found Styles */

.not-found-container {
  min-height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) 0;
  animation: fadeIn 0.5s ease-out;
}

.not-found-content {
  max-width: 600px;
  text-align: center;
  padding: var(--space-8);
}

/* Illustration */
.not-found-illustration {
  position: relative;
  margin-bottom: var(--space-8);
}

.error-code {
  font-size: clamp(4rem, 15vw, 12rem);
  font-weight: var(--font-bold);
  color: var(--primary-200);
  line-height: 1;
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--primary-400), var(--secondary-400));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  opacity: 0.8;
}

.error-icon {
  font-size: var(--text-5xl);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translate(-50%, -50%) translateY(0px); }
  50% { transform: translate(-50%, -50%) translateY(-10px); }
}

/* Text Content */
.not-found-text {
  margin-bottom: var(--space-8);
}

.error-title {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.error-description {
  margin: 0 0 var(--space-6) 0;
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Actions */
.not-found-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
}

/* Helpful Links */
.helpful-links {
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
}

.links-title {
  margin: 0 0 var(--space-6) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-4);
}

.help-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  text-decoration: none;
  transition: all var(--transition-base);
  color: var(--text-primary);
}

.help-link:hover {
  background-color: var(--bg-surface-hover);
  border-color: var(--primary-300);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  text-decoration: none;
  color: var(--primary-600);
}

.help-icon {
  font-size: var(--text-2xl);
}

.help-text {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

/* Responsive Design */
@media (max-width: 767px) {
  .not-found-content {
    padding: var(--space-4);
  }
  
  .error-title {
    font-size: var(--text-3xl);
  }
  
  .error-description {
    font-size: var(--text-base);
  }
  
  .not-found-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .links-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .helpful-links {
    padding: var(--space-6);
  }
}

@media (max-width: 480px) {
  .error-code {
    font-size: 6rem;
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
}
