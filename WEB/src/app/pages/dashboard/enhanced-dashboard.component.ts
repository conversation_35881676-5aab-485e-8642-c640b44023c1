import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Subject, takeUntil, timer, switchMap } from 'rxjs';
import { LoadingSpinnerComponent } from '../../components/ui/loading-spinner/loading-spinner.component';
import { MockDashboardService, DashboardStats } from '../../services/mock-dashboard.service';

interface QuickAction {
  title: string;
  description: string;
  icon: string;
  route: string;
  color: string;
}

@Component({
  selector: 'app-enhanced-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule, LoadingSpinnerComponent],
  templateUrl: './enhanced-dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class EnhancedDashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  loading = signal(true);
  error = signal<string | null>(null);
  stats = signal<DashboardStats | null>(null);
  recentActivity = signal<any[]>([]);

  quickActions: QuickAction[] = [
    {
      title: 'Add Product',
      description: 'Create a new product',
      icon: '📦',
      route: '/products/create',
      color: 'primary'
    },
    {
      title: 'New Sale',
      description: 'Process a new sale',
      icon: '💰',
      route: '/sales/create',
      color: 'success'
    },
    {
      title: 'Check Inventory',
      description: 'View current stock levels',
      icon: '📋',
      route: '/inventory/current',
      color: 'info'
    },
    {
      title: 'Add Category',
      description: 'Create a new category',
      icon: '📂',
      route: '/categories/create',
      color: 'secondary'
    }
  ];

  // Computed values
  hasStats = computed(() => this.stats() !== null);
  hasLowStock = computed(() => (this.stats()?.lowStockItems || 0) > 0);
  hasPendingSales = computed(() => (this.stats()?.pendingSales || 0) > 0);
  hasOutOfStock = computed(() => (this.stats()?.outOfStockItems || 0) > 0);

  constructor(private dashboardService: MockDashboardService) {}

  ngOnInit(): void {
    this.loadDashboardData();
    this.setupAutoRefresh();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadDashboardData(): void {
    this.loading.set(true);
    this.error.set(null);

    this.dashboardService.getDashboardStats()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (stats: DashboardStats) => {
          this.stats.set(stats);
          this.loading.set(false);
        },
        error: (error: any) => {
          this.error.set(error.message || 'Failed to load dashboard data');
          this.loading.set(false);
          console.error('Error loading dashboard stats:', error);
        }
      });

    this.dashboardService.getRecentActivity()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (activity: any[]) => {
          this.recentActivity.set(activity);
        },
        error: (error: any) => {
          console.error('Error loading recent activity:', error);
        }
      });
  }

  private setupAutoRefresh(): void {
    // Refresh dashboard data every 5 minutes
    timer(300000, 300000)
      .pipe(
        switchMap(() => this.dashboardService.getDashboardStats()),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (stats: DashboardStats) => {
          this.stats.set(stats);
        },
        error: (error: any) => {
          console.error('Error refreshing dashboard stats:', error);
        }
      });
  }

  refreshData(): void {
    this.loadDashboardData();
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'cancelled': return 'danger';
      case 'lowstock': return 'warning';
      case 'outofstock': return 'danger';
      default: return 'secondary';
    }
  }

  getAlertClass(): string {
    const stats = this.stats();
    if (!stats) return '';
    
    if (stats.outOfStockItems > 0) return 'alert-danger';
    if (stats.lowStockItems > 0) return 'alert-warning';
    return 'alert-success';
  }

  getAlertMessage(): string {
    const stats = this.stats();
    if (!stats) return '';
    
    if (stats.outOfStockItems > 0) {
      return `${stats.outOfStockItems} products are out of stock!`;
    }
    if (stats.lowStockItems > 0) {
      return `${stats.lowStockItems} products are running low on stock.`;
    }
    return 'All products are well stocked.';
  }
}
