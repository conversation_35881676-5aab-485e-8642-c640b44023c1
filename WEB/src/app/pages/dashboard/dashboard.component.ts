import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { LoadingSpinnerComponent } from '../../components/ui/loading-spinner/loading-spinner.component';

interface DashboardStats {
  totalProducts: number;
  totalCategories: number;
  totalSales: number;
  totalRevenue: number;
  lowStockItems: number;
  pendingSales: number;
}

interface QuickAction {
  title: string;
  description: string;
  icon: string;
  route: string;
  color: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule, LoadingSpinnerComponent],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css'
})
export class DashboardComponent implements OnInit {
  loading = true;
  stats: DashboardStats = {
    totalProducts: 0,
    totalCategories: 0,
    totalSales: 0,
    totalRevenue: 0,
    lowStockItems: 0,
    pendingSales: 0
  };

  quickActions: QuickAction[] = [
    {
      title: 'Add Product',
      description: 'Create a new product',
      icon: '📦',
      route: '/products/create',
      color: 'primary'
    },
    {
      title: 'New Sale',
      description: 'Process a new sale',
      icon: '💰',
      route: '/sales/create',
      color: 'success'
    },
    {
      title: 'Check Inventory',
      description: 'View current stock levels',
      icon: '📋',
      route: '/inventory/current',
      color: 'info'
    },
    {
      title: 'Add Category',
      description: 'Create a new category',
      icon: '📂',
      route: '/categories/create',
      color: 'secondary'
    }
  ];

  recentActivities = [
    { type: 'sale', message: 'New sale completed - $125.50', time: '2 minutes ago' },
    { type: 'product', message: 'Product "Laptop Stand" added', time: '15 minutes ago' },
    { type: 'inventory', message: 'Low stock alert: Wireless Mouse', time: '1 hour ago' },
    { type: 'category', message: 'Category "Electronics" updated', time: '2 hours ago' }
  ];

  ngOnInit(): void {
    this.loadDashboardData();
  }

  private loadDashboardData(): void {
    // Simulate API call
    setTimeout(() => {
      this.stats = {
        totalProducts: 156,
        totalCategories: 12,
        totalSales: 89,
        totalRevenue: 15420.75,
        lowStockItems: 8,
        pendingSales: 3
      };
      this.loading = false;
    }, 1500);
  }

  getActivityIcon(type: string): string {
    switch (type) {
      case 'sale': return '💰';
      case 'product': return '📦';
      case 'inventory': return '📋';
      case 'category': return '📂';
      default: return '📄';
    }
  }
}
