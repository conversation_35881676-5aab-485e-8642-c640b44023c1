/* Dashboard Styles */
.dashboard-container {
  min-height: calc(100vh - 200px);
}

.page-header {
  margin-bottom: var(--spacing-8);
  text-align: center;
}

.page-title {
  margin: 0 0 var(--spacing-2) 0;
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.page-description {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.stat-card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-card.warning {
  border-color: var(--status-warning-border);
  background-color: var(--status-warning-bg);
}

.stat-card.info {
  border-color: var(--status-info-border);
  background-color: var(--status-info-bg);
}

.stat-icon {
  font-size: var(--font-size-3xl);
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Main Grid */
.main-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

/* Quick Actions */
.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3);
}

.quick-action-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius-md);
  text-decoration: none;
  transition: all var(--transition-fast);
  background-color: var(--bg-surface);
}

.quick-action-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
}

.action-primary:hover {
  border-color: var(--color-primary-300);
  background-color: var(--color-primary-50);
}

.action-success:hover {
  border-color: var(--color-success-300);
  background-color: var(--color-success-50);
}

.action-info:hover {
  border-color: var(--color-info-300);
  background-color: var(--color-info-50);
}

.action-secondary:hover {
  border-color: var(--color-neutral-300);
  background-color: var(--color-neutral-50);
}

.action-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.action-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
}

.action-description {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* Recent Activity */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-fast);
}

.activity-item:hover {
  background-color: var(--bg-surface-hover);
}

.activity-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
  margin-top: 2px;
}

.activity-message {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
}

.activity-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.activity-footer {
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-primary);
  text-align: center;
}

.view-all-link {
  color: var(--interactive-primary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.view-all-link:hover {
  text-decoration: underline;
}

/* Navigation Cards */
.nav-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.nav-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-6);
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius-lg);
  text-decoration: none;
  transition: all var(--transition-base);
  background-color: var(--bg-surface);
}

.nav-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  text-decoration: none;
  border-color: var(--interactive-primary);
}

.nav-card-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-3);
}

.nav-card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.nav-card-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

/* Responsive Design */
@media (max-width: 1023px) {
  .main-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: var(--font-size-3xl);
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .nav-cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }
}
