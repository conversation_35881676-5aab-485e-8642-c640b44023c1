<div class="dashboard-container">
  <div class="container">
    <!-- <PERSON> Header -->
    <div class="page-header">
      <h1 class="page-title">Dashboard</h1>
      <p class="page-description">Welcome to your Tele-Shop management dashboard</p>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="loading-section">
      <app-loading-spinner 
        size="lg" 
        message="Loading dashboard data..."
        [showMessage]="true">
      </app-loading-spinner>
    </div>

    <!-- Dashboard Content -->
    <div *ngIf="!loading" class="dashboard-content">
      <!-- Stats Grid -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">📦</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalProducts }}</div>
            <div class="stat-label">Total Products</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">📂</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalCategories }}</div>
            <div class="stat-label">Categories</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">💰</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalSales }}</div>
            <div class="stat-label">Total Sales</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">💵</div>
          <div class="stat-content">
            <div class="stat-value">${{ stats.totalRevenue | number:'1.2-2' }}</div>
            <div class="stat-label">Revenue</div>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">⚠️</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.lowStockItems }}</div>
            <div class="stat-label">Low Stock Items</div>
          </div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">⏳</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.pendingSales }}</div>
            <div class="stat-label">Pending Sales</div>
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="main-grid">
        <!-- Quick Actions -->
        <div class="quick-actions-section">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Quick Actions</h3>
            </div>
            <div class="card-body">
              <div class="quick-actions-grid">
                <a *ngFor="let action of quickActions" 
                   [routerLink]="action.route"
                   class="quick-action-card"
                   [class]="'action-' + action.color">
                  <div class="action-icon">{{ action.icon }}</div>
                  <div class="action-content">
                    <div class="action-title">{{ action.title }}</div>
                    <div class="action-description">{{ action.description }}</div>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="recent-activity-section">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Recent Activity</h3>
            </div>
            <div class="card-body">
              <div class="activity-list">
                <div *ngFor="let activity of recentActivities" class="activity-item">
                  <div class="activity-icon">{{ getActivityIcon(activity.type) }}</div>
                  <div class="activity-content">
                    <div class="activity-message">{{ activity.message }}</div>
                    <div class="activity-time">{{ activity.time }}</div>
                  </div>
                </div>
              </div>
              <div class="activity-footer">
                <a routerLink="/sales/history" class="view-all-link">View all activity →</a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation Cards -->
      <div class="navigation-section">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Explore Tele-Shop</h3>
          </div>
          <div class="card-body">
            <div class="nav-cards-grid">
              <a routerLink="/products" class="nav-card">
                <div class="nav-card-icon">📦</div>
                <div class="nav-card-title">Products</div>
                <div class="nav-card-description">Manage your product catalog</div>
              </a>

              <a routerLink="/inventory" class="nav-card">
                <div class="nav-card-icon">📋</div>
                <div class="nav-card-title">Inventory</div>
                <div class="nav-card-description">Track stock levels and manage inventory</div>
              </a>

              <a routerLink="/sales" class="nav-card">
                <div class="nav-card-icon">💰</div>
                <div class="nav-card-title">Sales</div>
                <div class="nav-card-description">Process sales and view reports</div>
              </a>

              <a routerLink="/categories" class="nav-card">
                <div class="nav-card-icon">📂</div>
                <div class="nav-card-title">Categories</div>
                <div class="nav-card-description">Organize products by categories</div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
