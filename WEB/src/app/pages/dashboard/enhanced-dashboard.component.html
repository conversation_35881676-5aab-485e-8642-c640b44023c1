<div class="page-container">
  <div class="container">
    <!-- <PERSON> Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">Dashboard</h1>
        <p class="page-description">Welcome to Tele-Shop Management System</p>
      </div>
      <div class="header-actions">
        <button 
          class="btn btn-outline-primary" 
          (click)="refreshData()"
          [disabled]="loading()">
          <span class="btn-icon">🔄</span>
          Refresh
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading()" class="loading-container">
      <app-loading-spinner></app-loading-spinner>
      <p class="loading-text">Loading dashboard data...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error() && !loading()" class="alert alert-danger">
      <h4>Error Loading Dashboard</h4>
      <p>{{ error() }}</p>
      <button class="btn btn-danger" (click)="refreshData()">Try Again</button>
    </div>

    <!-- Dashboard Content -->
    <div *ngIf="hasStats() && !loading()" class="dashboard-content">
      
      <!-- Inventory Alert -->
      <div *ngIf="hasLowStock() || hasOutOfStock()" class="alert" [ngClass]="getAlertClass()">
        <div class="alert-content">
          <span class="alert-icon">⚠️</span>
          <span class="alert-message">{{ getAlertMessage() }}</span>
          <a routerLink="/inventory/current" class="alert-link">View Inventory</a>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="stats-grid">
        <div class="stat-card primary">
          <div class="stat-icon">📦</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats()?.totalProducts || 0 }}</div>
            <div class="stat-label">Total Products</div>
          </div>
          <a routerLink="/products" class="stat-link">View All</a>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">💰</div>
          <div class="stat-content">
            <div class="stat-value">{{ formatCurrency(stats()?.totalRevenue || 0) }}</div>
            <div class="stat-label">Total Revenue</div>
          </div>
          <a routerLink="/sales" class="stat-link">View Sales</a>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">📋</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats()?.totalSales || 0 }}</div>
            <div class="stat-label">Completed Sales</div>
          </div>
          <a routerLink="/sales/history" class="stat-link">View History</a>
        </div>

        <div class="stat-card warning" *ngIf="hasPendingSales()">
          <div class="stat-icon">⏳</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats()?.pendingSales || 0 }}</div>
            <div class="stat-label">Pending Sales</div>
          </div>
          <a routerLink="/sales/active" class="stat-link">Manage</a>
        </div>

        <div class="stat-card secondary">
          <div class="stat-icon">📂</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats()?.totalCategories || 0 }}</div>
            <div class="stat-label">Categories</div>
          </div>
          <a routerLink="/categories" class="stat-link">Manage</a>
        </div>

        <div class="stat-card" [ngClass]="hasLowStock() ? 'danger' : 'success'">
          <div class="stat-icon">{{ hasLowStock() ? '⚠️' : '✅' }}</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats()?.lowStockItems || 0 }}</div>
            <div class="stat-label">Low Stock Items</div>
          </div>
          <a routerLink="/inventory/current" class="stat-link">Check Stock</a>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions-section">
        <h2 class="section-title">Quick Actions</h2>
        <div class="quick-actions-grid">
          <a 
            *ngFor="let action of quickActions" 
            [routerLink]="action.route" 
            class="quick-action-card"
            [ngClass]="'quick-action-' + action.color">
            <div class="action-icon">{{ action.icon }}</div>
            <div class="action-content">
              <h3 class="action-title">{{ action.title }}</h3>
              <p class="action-description">{{ action.description }}</p>
            </div>
            <div class="action-arrow">→</div>
          </a>
        </div>
      </div>

      <!-- Dashboard Grid -->
      <div class="dashboard-grid">
        
        <!-- Top Selling Products -->
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">Top Selling Products</h3>
            <a routerLink="/products" class="card-link">View All</a>
          </div>
          <div class="card-body">
            <div *ngIf="stats()?.topSellingProducts?.length === 0" class="empty-state">
              <p>No sales data available</p>
            </div>
            <div *ngFor="let product of stats()?.topSellingProducts" class="product-item">
              <div class="product-info">
                <div class="product-name">{{ product.productName }}</div>
                <div class="product-stats">
                  {{ product.totalQuantitySold }} sold • {{ formatCurrency(product.totalRevenue) }}
                </div>
              </div>
              <div class="product-badge">{{ product.totalQuantitySold }}</div>
            </div>
          </div>
        </div>

        <!-- Low Stock Products -->
        <div class="dashboard-card" *ngIf="hasLowStock()">
          <div class="card-header">
            <h3 class="card-title">Low Stock Alert</h3>
            <a routerLink="/inventory/current" class="card-link">View Inventory</a>
          </div>
          <div class="card-body">
            <div *ngFor="let product of stats()?.lowStockProducts?.slice(0, 5)" class="stock-item">
              <div class="stock-info">
                <div class="stock-name">{{ product.productName }}</div>
                <div class="stock-level">
                  {{ product.currentStock }} / {{ product.threshold }} threshold
                </div>
              </div>
              <div class="stock-badge" [ngClass]="getStatusColor(product.status)">
                {{ product.status }}
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">Recent Activity</h3>
            <a routerLink="/sales" class="card-link">View All</a>
          </div>
          <div class="card-body">
            <div *ngIf="recentActivity().length === 0" class="empty-state">
              <p>No recent activity</p>
            </div>
            <div *ngFor="let activity of recentActivity().slice(0, 5)" class="activity-item">
              <div class="activity-icon">{{ activity.icon }}</div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.description }}</div>
                <div class="activity-time">{{ formatDate(activity.timestamp) }}</div>
              </div>
              <div class="activity-status" [ngClass]="getStatusColor(activity.status)">
                {{ activity.status }}
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Metrics -->
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">Today's Performance</h3>
          </div>
          <div class="card-body">
            <div class="metric-item">
              <div class="metric-label">Sales Today</div>
              <div class="metric-value">{{ stats()?.completedSalesToday || 0 }}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Average Order Value</div>
              <div class="metric-value">{{ formatCurrency(stats()?.averageOrderValue || 0) }}</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Out of Stock</div>
              <div class="metric-value">{{ stats()?.outOfStockItems || 0 }}</div>
            </div>
          </div>
        </div>

      </div>

      <!-- Navigation Cards -->
      <div class="navigation-section">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Explore Tele-Shop</h3>
          </div>
          <div class="card-body">
            <div class="nav-cards-grid">
              <a routerLink="/products" class="nav-card">
                <div class="nav-card-icon">📦</div>
                <div class="nav-card-title">Products</div>
                <div class="nav-card-description">Manage your product catalog</div>
              </a>

              <a routerLink="/inventory" class="nav-card">
                <div class="nav-card-icon">📋</div>
                <div class="nav-card-title">Inventory</div>
                <div class="nav-card-description">Track stock levels and manage inventory</div>
              </a>

              <a routerLink="/sales" class="nav-card">
                <div class="nav-card-icon">💰</div>
                <div class="nav-card-title">Sales</div>
                <div class="nav-card-description">Process sales and view transaction history</div>
              </a>

              <a routerLink="/categories" class="nav-card">
                <div class="nav-card-icon">📂</div>
                <div class="nav-card-title">Categories</div>
                <div class="nav-card-description">Organize products into categories</div>
              </a>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>
