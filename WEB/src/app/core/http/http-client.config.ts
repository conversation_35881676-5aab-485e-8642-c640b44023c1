import { environment } from '../../../environments/environment';

export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export const API_CONFIG: ApiConfig = {
  baseUrl: environment.apiUrl,
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000
};

export interface HttpClientConfig {
  enableRetry: boolean;
  enableLogging: boolean;
  enableCaching: boolean;
  defaultTimeout: number;
}

export const HTTP_CLIENT_CONFIG: HttpClientConfig = {
  enableRetry: true,
  enableLogging: !environment.production,
  enableCaching: true,
  defaultTimeout: 30000
};
