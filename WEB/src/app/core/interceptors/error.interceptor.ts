import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { throwError, timer } from 'rxjs';
import { catchError, retryWhen, concatMap, take } from 'rxjs/operators';
import { ErrorHandlerService } from '../../services/error-handler.service';
import { LoadingService } from '../../services/loading.service';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const errorHandler = inject(ErrorHandlerService);
  const loadingService = inject(LoadingService);

  return next(req).pipe(
    retryWhen(errors =>
      errors.pipe(
        concatMap((error: HttpErrorResponse, index) => {
          if (index >= 2) {
            return throwError(() => error);
          }
          
          if (isRetryableError(error)) {
            const delay = Math.pow(2, index) * 1000;
            return timer(delay);
          }
          
          return throwError(() => error);
        }),
        take(3)
      )
    ),
    catchError((error: HttpErrorResponse) => {
      loadingService.setLoading(false);
      
      let errorMessage = 'An unknown error occurred';
      let userMessage = 'Something went wrong. Please try again.';
      
      if (error.error instanceof ErrorEvent) {
        errorMessage = `Network Error: ${error.error.message}`;
        userMessage = 'Network connection problem. Please check your internet connection.';
      } else {
        switch (error.status) {
          case 400:
            errorMessage = 'Bad Request';
            userMessage = 'Invalid request. Please check your input.';
            break;
          case 401:
            errorMessage = 'Unauthorized';
            userMessage = 'You are not authorized. Please log in.';
            break;
          case 403:
            errorMessage = 'Forbidden';
            userMessage = 'You do not have permission to perform this action.';
            break;
          case 404:
            errorMessage = 'Not Found';
            userMessage = 'The requested resource was not found.';
            break;
          case 409:
            errorMessage = 'Conflict';
            userMessage = 'There was a conflict with your request.';
            break;
          case 422:
            errorMessage = 'Validation Error';
            userMessage = 'Please check your input and try again.';
            break;
          case 500:
            errorMessage = 'Internal Server Error';
            userMessage = 'Server error. Please try again later.';
            break;
          case 503:
            errorMessage = 'Service Unavailable';
            userMessage = 'Service is temporarily unavailable. Please try again later.';
            break;
          default:
            errorMessage = `Server Error: ${error.status} ${error.statusText}`;
            userMessage = 'Something went wrong. Please try again.';
        }
        
        if (error.error && typeof error.error === 'object' && error.error.message) {
          errorMessage = error.error.message;
        }
      }

      console.error('HTTP Error:', {
        status: error.status,
        message: errorMessage,
        url: error.url,
        error: error.error
      });

      const enhancedError = new Error(userMessage);
      (enhancedError as any).originalError = error;
      (enhancedError as any).userMessage = userMessage;
      
      return throwError(() => enhancedError);
    })
  );
};

function isRetryableError(error: HttpErrorResponse): boolean {
  return error.status >= 500 || error.status === 0 || error.status === 408;
}
