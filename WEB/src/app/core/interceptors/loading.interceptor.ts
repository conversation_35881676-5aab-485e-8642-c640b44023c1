import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { LoadingService } from '../../services/loading.service';

export const loadingInterceptor: HttpInterceptorFn = (req, next) => {
  const loadingService = inject(LoadingService);
  
  const requestKey = `${req.method}-${req.url}`;
  
  if (!req.headers.has('X-Skip-Loading')) {
    loadingService.setLoading(true, requestKey);
  }

  return next(req).pipe(
    finalize(() => {
      if (!req.headers.has('X-Skip-Loading')) {
        loadingService.setLoading(false, requestKey);
      }
    })
  );
};
