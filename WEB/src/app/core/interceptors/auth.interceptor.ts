import { HttpInterceptorFn } from '@angular/common/http';
import { inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const platformId = inject(PLATFORM_ID);
  
  // Skip auth for auth endpoints and requests with skip header
  if (req.url.includes('/auth/') || req.headers.has('X-Skip-Auth')) {
    return next(req);
  }

  // Only add token if we're in the browser
  if (isPlatformBrowser(platformId)) {
    const token = localStorage.getItem('tele-shop-token');
    
    if (token) {
      const authReq = req.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        }
      });
      
      return next(authReq).pipe(
        catchError(error => {
          if (error.status === 401) {
            // Clear auth data on 401 errors
            localStorage.removeItem('tele-shop-token');
            localStorage.removeItem('tele-shop-refresh-token');
            localStorage.removeItem('tele-shop-user');
            
            // Redirect to login page
            if (typeof window !== 'undefined') {
              window.location.href = '/auth/login';
            }
          }
          return throwError(() => error);
        })
      );
    }
  }

  return next(req);
};
