import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, retry, timeout } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { PagedResult, QueryParams, ApiResponse } from '../../models/common.models';
import { ErrorHandlerService } from '../../services/error-handler.service';
import { API_CONFIG } from '../http/http-client.config';

@Injectable({
  providedIn: 'root'
})
export abstract class BaseApiService<T, TCreate, TUpdate> {
  protected abstract endpoint: string;
  protected baseUrl: string;

  constructor(
    protected http: HttpClient,
    protected errorHandler: ErrorHandlerService
  ) {
    this.baseUrl = environment.apiUrl;
  }

  protected getFullUrl(path?: string): string {
    const url = `${this.baseUrl}/${this.endpoint}`;
    return path ? `${url}/${path}` : url;
  }

  protected buildHttpParams(params?: QueryParams): HttpParams {
    let httpParams = new HttpParams();
    
    if (params) {
      if (params.pageNumber) {
        httpParams = httpParams.set('pageNumber', params.pageNumber.toString());
      }
      if (params.pageSize) {
        httpParams = httpParams.set('pageSize', params.pageSize.toString());
      }
      if (params.sortBy) {
        httpParams = httpParams.set('sortBy', params.sortBy);
      }
      if (params.sortDirection) {
        httpParams = httpParams.set('sortDirection', params.sortDirection);
      }
      if (params.search) {
        httpParams = httpParams.set('search', params.search);
      }
      if (params.filters) {
        Object.keys(params.filters).forEach(key => {
          if (params.filters![key] !== null && params.filters![key] !== undefined) {
            httpParams = httpParams.set(key, params.filters![key].toString());
          }
        });
      }
    }
    
    return httpParams;
  }

  getAll(params?: QueryParams): Observable<PagedResult<T>> {
    const httpParams = this.buildHttpParams(params);
    return this.http.get<PagedResult<T>>(this.getFullUrl(), { params: httpParams })
      .pipe(
        timeout(API_CONFIG.timeout),
        retry(API_CONFIG.retryAttempts),
        catchError(this.errorHandler.handleError)
      );
  }

  getById(id: string | number): Observable<T> {
    return this.http.get<T>(this.getFullUrl(id.toString()))
      .pipe(
        timeout(API_CONFIG.timeout),
        retry(API_CONFIG.retryAttempts),
        catchError(this.errorHandler.handleError)
      );
  }

  create(item: TCreate): Observable<T> {
    return this.http.post<T>(this.getFullUrl(), item)
      .pipe(
        timeout(API_CONFIG.timeout),
        catchError(this.errorHandler.handleError)
      );
  }

  update(id: string | number, item: TUpdate): Observable<T> {
    return this.http.put<T>(this.getFullUrl(id.toString()), item)
      .pipe(
        timeout(API_CONFIG.timeout),
        catchError(this.errorHandler.handleError)
      );
  }

  patch(id: string | number, patch: Partial<TUpdate>): Observable<T> {
    return this.http.patch<T>(this.getFullUrl(id.toString()), patch)
      .pipe(
        timeout(API_CONFIG.timeout),
        catchError(this.errorHandler.handleError)
      );
  }

  delete(id: string | number): Observable<void> {
    return this.http.delete<void>(this.getFullUrl(id.toString()))
      .pipe(
        timeout(API_CONFIG.timeout),
        catchError(this.errorHandler.handleError)
      );
  }

  search(query: string, params?: QueryParams): Observable<PagedResult<T>> {
    const searchParams = { ...params, search: query };
    return this.getAll(searchParams);
  }
}
