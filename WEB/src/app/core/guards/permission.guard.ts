import { inject } from '@angular/core';
import { CanActivateFn, Router, ActivatedRouteSnapshot } from '@angular/router';
import { map } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

export const permissionGuard: CanActivateFn = (route: ActivatedRouteSnapshot, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  const requiredPermissions = route.data['permissions'] as string[];
  
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true;
  }

  return authService.currentUser$.pipe(
    map(user => {
      if (!user) {
        router.navigate(['/auth/login'], { 
          queryParams: { returnUrl: state.url } 
        });
        return false;
      }

      const hasRequiredPermission = requiredPermissions.some(permission => 
        authService.hasPermission(permission)
      );

      if (hasRequiredPermission) {
        return true;
      } else {
        router.navigate(['/unauthorized']);
        return false;
      }
    })
  );
};
