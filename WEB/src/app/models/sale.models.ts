export interface ViewSaleDto {
  id: string;
  items: ViewSaleItemDto[];
  totalAmount: number;
  discountAmount: number;
  finalAmount: number;
  currency: string;
  createdAt: string;
  status: string;
  cancellationReason?: string;
}

export interface ViewSaleItemDto {
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  currency: string;
  totalPrice: number;
}

export interface CreateSaleDto {
  discountAmount?: number;
  currency?: string;
  items?: SaleItemDto[];
}

export interface SaleItemDto {
  productId: number;
  quantity: number;
}

export interface CompleteSaleDto {
  saleId: string;
  currency: string;
}

export interface CancelSaleDto {
  saleId: string;
  reason?: string;
}

export interface ApplySaleDiscountDto {
  saleId: string;
  amount: number;
  currency: string;
}

export interface DateRangeDto {
  startDate: string;
  endDate: string;
}
