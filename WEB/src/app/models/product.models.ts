export interface ViewProductDto {
  id: number;
  name: string;
  description: string;
  categoryName: string;
  tags: string[];
}

export interface CreateProductDto {
  name: string;
  description: string;
  categoryId: number;
  tagIds?: number[];
}

export interface UpdateProductDto {
  id: number;
  name: string;
  description: string;
  categoryId: number;
  tagIds?: number[];
}

export interface PatchProductDto {
  id: number;
  name?: string;
  description?: string;
  categoryId?: number;
  tagIds?: number[];
}
