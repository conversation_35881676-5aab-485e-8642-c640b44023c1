export interface ViewInventoryDto {
  id: string;
  balance: number;
  currency: string;
  createdAt: string;
  items: ViewInventoryItemDto[];
}

export interface ViewInventoryItemDto {
  id: string;
  productId: number;
  productName: string;
  quantity: number;
  reservedQuantity: number;
  threshold: number;
  soldQuantity: number;
  totalSoldQuantity: number;
  totalWastedQuantity: number;
  status: string;
  actualPrice: number;
  sellingPrice: number;
  currency: string;
  availableQuantity: number;
}

export interface CreateInventoryDto {
  balance: number;
  currency: string;
  recordDate?: string;
}

export interface AddInventoryItemDto {
  productId: number;
  quantity: number;
  actualPrice: number;
  sellingPrice: number;
  currency: string;
  threshold: number;
}

export interface WasteInventoryItemDto {
  productId: number;
  quantity: number;
  reason: string;
}

export interface ViewInventoryItemPriceDto {
  id: string;
  actualPrice: number;
  sellingPrice: number;
  currency: string;
  createdAt: string;
}

export interface ViewWasteRecordDto {
  id: string;
  quantity: number;
  reason: string;
  recordedDate: string;
}

export interface UpdateInventoryItemPriceDto {
  productId: number;
  actualPrice: number;
  sellingPrice: number;
  currency: string;
}
