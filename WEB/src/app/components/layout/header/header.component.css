/* Header Styles */
.header {
  background-color: var(--header-bg);
  border-bottom: 1px solid var(--header-border);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
  backdrop-filter: blur(8px);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
  gap: var(--spacing-4);
}

/* Brand */
.header-brand {
  flex-shrink: 0;
}

.brand-link {
  text-decoration: none;
  color: inherit;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.brand-icon {
  font-size: var(--font-size-2xl);
}

.brand-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--header-text);
}

/* Desktop Navigation */
.header-nav {
  flex: 1;
  max-width: 600px;
}

.nav-list {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  color: var(--nav-link-color);
  text-decoration: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.nav-link:hover {
  color: var(--nav-link-hover);
  background-color: var(--bg-surface-hover);
}

.nav-link.active {
  color: var(--nav-link-active);
  background-color: var(--color-primary-50);
}

.nav-icon {
  font-size: var(--font-size-base);
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.theme-icon,
.user-avatar {
  font-size: var(--font-size-lg);
}

/* Hamburger Menu */
.hamburger-icon {
  display: flex;
  flex-direction: column;
  gap: 3px;
  width: 20px;
  height: 16px;
}

.hamburger-icon span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: var(--text-primary);
  border-radius: 1px;
  transition: all var(--transition-fast);
}

.hamburger-icon.open span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-icon.open span:nth-child(2) {
  opacity: 0;
}

.hamburger-icon.open span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--header-bg);
  border-bottom: 1px solid var(--header-border);
  box-shadow: var(--shadow-lg);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  z-index: var(--z-index-dropdown);
}

.mobile-nav.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: var(--spacing-4) 0;
}

.mobile-nav-item {
  margin: 0;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--nav-link-color);
  text-decoration: none;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
}

.mobile-nav-link:hover {
  color: var(--nav-link-hover);
  background-color: var(--bg-surface-hover);
}

.mobile-nav-link.active {
  color: var(--nav-link-active);
  background-color: var(--color-primary-50);
  border-left-color: var(--nav-link-active);
}

/* Mobile Backdrop */
.mobile-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  z-index: var(--z-index-modal-backdrop);
}

.mobile-backdrop.open {
  opacity: 1;
  visibility: visible;
}

/* Responsive Design */
@media (max-width: 767px) {
  .header-content {
    height: 3.5rem;
  }
  
  .brand-text {
    font-size: var(--font-size-lg);
  }
  
  .brand-icon {
    font-size: var(--font-size-xl);
  }
}
