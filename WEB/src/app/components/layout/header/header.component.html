<header class="header">
  <div class="container">
    <div class="header-content">
      <!-- Logo/Brand -->
      <div class="header-brand">
        <a routerLink="/" class="brand-link">
          <div class="brand-logo">
            <span class="brand-icon">🛒</span>
            <span class="brand-text">Tele-Shop</span>
          </div>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <nav class="header-nav d-md-flex d-sm-none">
        <ul class="nav-list">
          <li class="nav-item">
            <a routerLink="/dashboard" routerLinkActive="active" class="nav-link">
              <span class="nav-icon">📊</span>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a routerLink="/products" routerLinkActive="active" class="nav-link">
              <span class="nav-icon">📦</span>
              Products
            </a>
          </li>
          <li class="nav-item">
            <a routerLink="/categories" routerLinkActive="active" class="nav-link">
              <span class="nav-icon">📂</span>
              Categories
            </a>
          </li>
          <li class="nav-item">
            <a routerLink="/inventory" routerLinkActive="active" class="nav-link">
              <span class="nav-icon">📋</span>
              Inventory
            </a>
          </li>
          <li class="nav-item">
            <a routerLink="/sales" routerLinkActive="active" class="nav-link">
              <span class="nav-icon">💰</span>
              Sales
            </a>
          </li>
        </ul>
      </nav>

      <!-- Header Actions -->
      <div class="header-actions">
        <!-- Theme Toggle -->
        <button
          class="btn btn-ghost btn-icon"
          (click)="toggleTheme()"
          [title]="getThemeLabel()"
          aria-label="Toggle theme">
          <span class="theme-icon">{{ getThemeIcon() }}</span>
        </button>

        <!-- User Menu (placeholder) -->
        <div class="user-menu">
          <button class="btn btn-ghost btn-icon" title="User menu" aria-label="User menu">
            <span class="user-avatar">👤</span>
          </button>
        </div>

        <!-- Mobile Menu Toggle -->
        <button
          class="btn btn-ghost btn-icon d-md-none d-sm-flex"
          (click)="toggleMobileMenu()"
          [attr.aria-expanded]="isMobileMenuOpen"
          aria-label="Toggle mobile menu">
          <span class="hamburger-icon" [class.open]="isMobileMenuOpen">
            <span></span>
            <span></span>
            <span></span>
          </span>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav d-md-none" [class.open]="isMobileMenuOpen">
      <ul class="mobile-nav-list">
        <li class="mobile-nav-item">
          <a routerLink="/dashboard"
             routerLinkActive="active"
             class="mobile-nav-link"
             (click)="closeMobileMenu()">
            <span class="nav-icon">📊</span>
            Dashboard
          </a>
        </li>
        <li class="mobile-nav-item">
          <a routerLink="/products"
             routerLinkActive="active"
             class="mobile-nav-link"
             (click)="closeMobileMenu()">
            <span class="nav-icon">📦</span>
            Products
          </a>
        </li>
        <li class="mobile-nav-item">
          <a routerLink="/categories"
             routerLinkActive="active"
             class="mobile-nav-link"
             (click)="closeMobileMenu()">
            <span class="nav-icon">📂</span>
            Categories
          </a>
        </li>
        <li class="mobile-nav-item">
          <a routerLink="/inventory"
             routerLinkActive="active"
             class="mobile-nav-link"
             (click)="closeMobileMenu()">
            <span class="nav-icon">📋</span>
            Inventory
          </a>
        </li>
        <li class="mobile-nav-item">
          <a routerLink="/sales"
             routerLinkActive="active"
             class="mobile-nav-link"
             (click)="closeMobileMenu()">
            <span class="nav-icon">💰</span>
            Sales
          </a>
        </li>
      </ul>
    </nav>
  </div>

  <!-- Mobile Menu Backdrop -->
  <div class="mobile-backdrop"
       [class.open]="isMobileMenuOpen"
       (click)="closeMobileMenu()"></div>
</header>
