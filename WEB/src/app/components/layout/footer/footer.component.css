.app-footer {
  background-color: var(--bg-surface);
  border-top: 1px solid var(--border-primary);
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-8);
  padding: var(--space-8) 0;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

/* Footer Brand */
.footer-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.footer-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  color: white;
}

.footer-text {
  display: flex;
  flex-direction: column;
}

.footer-name {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1;
}

.footer-tagline {
  font-size: var(--text-sm);
  color: var(--text-muted);
  font-weight: var(--font-medium);
}

/* Footer Links */
.footer-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
}

.footer-links {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.footer-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--text-sm);
  transition: color var(--transition-fast);
}

.footer-link:hover {
  color: var(--interactive-primary);
  text-decoration: none;
}

/* System Info */
.system-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--text-sm);
}

.info-label {
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.info-value {
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

.status-online {
  color: var(--success-600);
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) 0;
  border-top: 1px solid var(--border-primary);
  margin-top: var(--space-6);
}

.footer-copyright {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.footer-copyright p {
  margin: 0;
}

.footer-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}
