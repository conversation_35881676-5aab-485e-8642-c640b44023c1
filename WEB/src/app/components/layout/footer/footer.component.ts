import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.css'
})
export class FooterComponent {
  currentYear = new Date().getFullYear();
  
  quickLinks = [
    { label: 'Dashboard', route: '/dashboard' },
    { label: 'Products', route: '/products' },
    { label: 'Categories', route: '/categories' },
    { label: 'Inventory', route: '/inventory' },
    { label: 'Sales', route: '/sales' }
  ];

  supportLinks = [
    { label: 'Help Center', route: '/help' },
    { label: 'Documentation', route: '/docs' },
    { label: 'Contact Support', route: '/support' },
    { label: 'System Status', route: '/status' }
  ];
}
