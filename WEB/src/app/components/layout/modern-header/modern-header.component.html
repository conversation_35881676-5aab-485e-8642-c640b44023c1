<header class="app-header">
  <div class="container">
    <div class="header-content">
      <!-- Mobile Menu Button -->
      <button 
        class="btn btn-icon sidebar-toggle lg:hidden"
        (click)="onToggleSidebar()"
        aria-label="Toggle navigation menu"
      >
        <span class="hamburger-icon">☰</span>
      </button>

      <!-- Logo and Brand -->
      <div class="brand">
        <a routerLink="/" class="brand-link">
          <div class="brand-icon">🛒</div>
          <div class="brand-text">
            <div class="brand-name">Tele-Shop</div>
            <div class="brand-tagline">Modern Commerce</div>
          </div>
        </a>
      </div>

      <!-- Search Bar -->
      <div class="search-container hidden md:flex">
        <div class="search-input-wrapper">
          <span class="search-icon">🔍</span>
          <input 
            type="text" 
            class="search-input" 
            placeholder="Search products, categories..."
            aria-label="Search"
          >
          <kbd class="search-shortcut">⌘K</kbd>
        </div>
      </div>

      <!-- Header Actions -->
      <div class="header-actions">
        <!-- Search Button (Mobile) -->
        <button class="btn btn-icon md:hidden" aria-label="Search">
          <span>🔍</span>
        </button>

        <!-- Notifications -->
        <div class="notification-wrapper">
          <button class="btn btn-icon notification-btn" aria-label="Notifications">
            <span>🔔</span>
            <span class="notification-badge">3</span>
          </button>
        </div>

        <!-- Theme Toggle -->
        <div class="theme-toggle-wrapper">
          <button 
            class="btn btn-icon theme-toggle"
            (click)="onToggleTheme()"
            [attr.aria-label]="'Switch to ' + (effectiveTheme === 'light' ? 'dark' : 'light') + ' theme'"
            [title]="getThemeLabel()"
          >
            <span class="theme-icon">{{ getThemeIcon() }}</span>
          </button>
          
          <!-- Theme Dropdown (Hidden for now, can be expanded) -->
          <div class="theme-dropdown hidden">
            <button 
              class="theme-option"
              (click)="onSetTheme('light')"
              [class.active]="currentTheme === 'light'"
            >
              <span>☀️</span> Light
            </button>
            <button 
              class="theme-option"
              (click)="onSetTheme('dark')"
              [class.active]="currentTheme === 'dark'"
            >
              <span>🌙</span> Dark
            </button>
            <button 
              class="theme-option"
              (click)="onSetTheme('system')"
              [class.active]="currentTheme === 'system'"
            >
              <span>🖥️</span> System
            </button>
          </div>
        </div>

        <!-- User Menu -->
        <div class="user-menu-wrapper">
          <button class="user-menu-trigger" aria-label="User menu">
            <div class="user-avatar">
              <span>👤</span>
            </div>
            <div class="user-info hidden sm:block">
              <div class="user-name">Admin User</div>
              <div class="user-role">Administrator</div>
            </div>
            <span class="dropdown-arrow">▼</span>
          </button>
          
          <!-- User Dropdown (Hidden for now, can be expanded) -->
          <div class="user-dropdown hidden">
            <a href="#" class="user-dropdown-item">
              <span>👤</span> Profile
            </a>
            <a href="#" class="user-dropdown-item">
              <span>⚙️</span> Settings
            </a>
            <a href="#" class="user-dropdown-item">
              <span>❓</span> Help
            </a>
            <hr class="user-dropdown-divider">
            <a href="#" class="user-dropdown-item">
              <span>🚪</span> Sign Out
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
