import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ModernThemeService, Theme } from '../../../services/modern-theme.service';

@Component({
  selector: 'app-modern-header',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './modern-header.component.html',
  styleUrl: './modern-header.component.css'
})
export class ModernHeaderComponent implements OnInit, OnDestroy {
  @Output() toggleSidebar = new EventEmitter<void>();

  currentTheme: Theme = 'system';
  effectiveTheme: 'light' | 'dark' = 'light';
  
  private destroy$ = new Subject<void>();

  constructor(private themeService: ModernThemeService) {}

  ngOnInit(): void {
    this.themeService.currentTheme$
      .pipe(takeUntil(this.destroy$))
      .subscribe(theme => {
        this.currentTheme = theme;
      });

    this.themeService.effectiveTheme$
      .pipe(takeUntil(this.destroy$))
      .subscribe(theme => {
        this.effectiveTheme = theme;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onToggleSidebar(): void {
    this.toggleSidebar.emit();
  }

  onToggleTheme(): void {
    this.themeService.toggleTheme();
  }

  onSetTheme(theme: Theme): void {
    this.themeService.setTheme(theme);
  }

  getThemeIcon(): string {
    if (this.currentTheme === 'system') {
      return '🖥️';
    }
    return this.effectiveTheme === 'dark' ? '🌙' : '☀️';
  }

  getThemeLabel(): string {
    if (this.currentTheme === 'system') {
      return `System (${this.effectiveTheme})`;
    }
    return this.currentTheme === 'dark' ? 'Dark' : 'Light';
  }
}
