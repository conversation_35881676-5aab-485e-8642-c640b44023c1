/* Modern Header Styles */
.header-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4) 0;
  min-height: 64px;
}

/* Sidebar Toggle */
.sidebar-toggle {
  background-color: transparent;
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
}

.sidebar-toggle:hover {
  background-color: var(--bg-surface-hover);
}

.hamburger-icon {
  font-size: var(--text-lg);
}

/* Brand */
.brand {
  margin-right: auto;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.brand-link:hover {
  text-decoration: none;
  transform: scale(1.02);
}

.brand-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  box-shadow: var(--shadow-md);
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-name {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1;
}

.brand-tagline {
  font-size: var(--text-xs);
  color: var(--text-muted);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Search */
.search-container {
  flex: 1;
  max-width: 500px;
  margin: 0 var(--space-6);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  color: var(--text-muted);
  font-size: var(--text-base);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-12) var(--space-3) var(--space-10);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--border-focus);
  background-color: var(--bg-surface);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-shortcut {
  position: absolute;
  right: var(--space-3);
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  color: var(--text-muted);
  font-family: var(--font-family-mono);
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* Notifications */
.notification-wrapper {
  position: relative;
}

.notification-btn {
  background-color: transparent;
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  position: relative;
}

.notification-btn:hover {
  background-color: var(--bg-surface-hover);
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: var(--error-500);
  color: white;
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* Theme Toggle */
.theme-toggle-wrapper {
  position: relative;
}

.theme-toggle {
  background-color: transparent;
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.theme-toggle:hover {
  background-color: var(--bg-surface-hover);
  transform: rotate(15deg);
}

.theme-icon {
  font-size: var(--text-base);
  transition: transform var(--transition-fast);
}

.theme-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--space-2);
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-2);
  min-width: 150px;
  z-index: var(--z-dropdown);
}

.theme-option {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-2) var(--space-3);
  background: none;
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.theme-option:hover {
  background-color: var(--bg-surface-hover);
}

.theme-option.active {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

/* User Menu */
.user-menu-wrapper {
  position: relative;
}

.user-menu-trigger {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-3);
  background-color: transparent;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-primary);
}

.user-menu-trigger:hover {
  background-color: var(--bg-surface-hover);
  box-shadow: var(--shadow-md);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-400), var(--secondary-400));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  color: white;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  line-height: 1;
}

.user-role {
  font-size: var(--text-xs);
  color: var(--text-muted);
  line-height: 1;
}

.dropdown-arrow {
  font-size: var(--text-xs);
  color: var(--text-muted);
  transition: transform var(--transition-fast);
}

.user-menu-trigger:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--space-2);
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-2);
  min-width: 200px;
  z-index: var(--z-dropdown);
}

.user-dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
}

.user-dropdown-item:hover {
  background-color: var(--bg-surface-hover);
  text-decoration: none;
}

.user-dropdown-divider {
  margin: var(--space-2) 0;
  border: none;
  border-top: 1px solid var(--border-primary);
}

/* Responsive Design */
@media (max-width: 767px) {
  .header-content {
    gap: var(--space-2);
  }
  
  .brand-text {
    display: none;
  }
  
  .user-info {
    display: none;
  }
  
  .user-menu-trigger {
    padding: var(--space-2);
  }
}

@media (max-width: 1023px) {
  .search-container {
    display: none;
  }
}
