<!-- Sidebar Overlay (Mobile) -->
<div
  class="sidebar-overlay lg:hidden"
  [class.open]="isOpen"
  (click)="onOverlayClick()"
></div>

<!-- Sidebar -->
<aside
  class="sidebar"
  [class.sidebar-mobile]="true"
  [class.sidebar-collapsed]="isCollapsed"
  [class.open]="isOpen"
>
  <!-- Navigation Menu -->
  <nav class="nav-menu">
    <!-- Quick Actions -->
    <div class="nav-section">
      <div class="nav-section-title" [class.hidden]="isCollapsed">Quick Actions</div>
      <div class="quick-actions">
        <a routerLink="/products/create" class="quick-action-btn" title="Add Product">
          <span class="quick-action-icon">➕</span>
          <span class="quick-action-label" [class.hidden]="isCollapsed">Add Product</span>
        </a>
        <a routerLink="/sales/create" class="quick-action-btn" title="New Sale">
          <span class="quick-action-icon">💰</span>
          <span class="quick-action-label" [class.hidden]="isCollapsed">New Sale</span>
        </a>
      </div>
    </div>

    <!-- Main Navigation -->
    <div class="nav-section">
      <div class="nav-section-title" [class.hidden]="isCollapsed">Navigation</div>
      <ul class="nav-list">
        <li
          class="nav-item"
          *ngFor="let item of navigationItems; trackBy: trackByFn"
        >
          <!-- Parent Item -->
          <button
            class="nav-link"
            [class.active]="item.isActive"
            [class.expanded]="item.isExpanded"
            [class.has-children]="item.children && item.children.length > 0"
            (click)="onItemClick(item)"
            [title]="item.label"
          >
            <span class="nav-icon">{{ item.icon }}</span>
            <span class="nav-label" [class.hidden]="isCollapsed">{{ item.label }}</span>
            <span
              class="nav-badge"
              *ngIf="item.badge && !isCollapsed"
            >{{ item.badge }}</span>
            <span
              class="nav-arrow"
              *ngIf="item.children && item.children.length > 0 && !isCollapsed"
              [class.rotated]="item.isExpanded"
            >▶</span>
          </button>

          <!-- Children Items -->
          <ul
            class="nav-children"
            *ngIf="item.children && item.children.length > 0 && !isCollapsed"
            [class.expanded]="item.isExpanded"
          >
            <li
              class="nav-child-item"
              *ngFor="let child of item.children; trackBy: trackByFn"
            >
              <button
                class="nav-child-link"
                [class.active]="child.isActive"
                (click)="onChildClick(child)"
                [title]="child.label"
              >
                <span class="nav-child-icon">{{ child.icon }}</span>
                <span class="nav-child-label">{{ child.label }}</span>
                <span
                  class="nav-badge"
                  *ngIf="child.badge"
                >{{ child.badge }}</span>
              </button>
            </li>
          </ul>
        </li>
      </ul>
    </div>

    <!-- User Section -->
    <div class="nav-section">
      <div class="nav-section-title" [class.hidden]="isCollapsed">Account</div>
      <ul class="nav-list">
        <li class="nav-item">
          <button class="nav-link" title="Profile">
            <span class="nav-icon">👤</span>
            <span class="nav-label" [class.hidden]="isCollapsed">Profile</span>
          </button>
        </li>
        <li class="nav-item">
          <button class="nav-link" title="Help & Support">
            <span class="nav-icon">❓</span>
            <span class="nav-label" [class.hidden]="isCollapsed">Help</span>
          </button>
        </li>
      </ul>
    </div>

    <div class="user-card" [class.collapsed]="isCollapsed">
      <div class="user-avatar">
        <span>👤</span>
      </div>
      <div class="user-details" [class.hidden]="isCollapsed">
        <div class="user-name">Admin User</div>
        <div class="user-status">Online</div>
      </div>
      <button class="user-menu-btn" [class.hidden]="isCollapsed" title="User menu">
        <span>⋯</span>
      </button>
    </div>
  </nav>
</aside>
