/* Modern Sidebar Styles */
/* Sidebar Container */
.sidebar {
  width: 280px;
  height: 100%;
  background-color: var(--bg-surface);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-base);
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-modal);
  transform: translateX(-100%);
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-collapsed {
  width: 64px;
}

/* Desktop Sidebar */
@media (min-width: 1024px) {
  .sidebar {
    position: relative;
    transform: translateX(0);
    z-index: auto;
  }
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  min-height: 64px;
}

.sidebar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.brand-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-base);
  color: white;
  flex-shrink: 0;
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-name {
  font-size: var(--text-base);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1;
}

.brand-version {
  font-size: var(--text-xs);
  color: var(--text-muted);
  font-weight: var(--font-medium);
}

.sidebar-close {
  background-color: transparent;
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
}

.sidebar-close:hover {
  background-color: var(--bg-surface-hover);
}

/* Navigation Menu */
.nav-menu {
  flex: 1;
  padding: var(--space-4);
  overflow-y: auto;
  overflow-x: hidden;
}

.nav-section {
  margin-bottom: var(--space-6);
}

.nav-section-title {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-3);
  padding: 0 var(--space-2);
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.sidebar-collapsed .quick-actions {
  grid-template-columns: 1fr;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3);
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-lg);
  color: var(--primary-700);
  text-decoration: none;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  transition: all var(--transition-fast);
}

.quick-action-btn:hover {
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  border-color: var(--primary-300);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  text-decoration: none;
}

.quick-action-icon {
  font-size: var(--text-sm);
}

.quick-action-label {
  font-size: var(--text-xs);
}

/* Navigation List */
.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: var(--space-1);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3) var(--space-2);
  background: none;
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
}

.nav-link:hover {
  background-color: var(--bg-surface-hover);
  color: var(--text-primary);
}

.nav-link.active {
  background-color: var(--primary-100);
  color: var(--primary-700);
  box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .nav-link.active {
  background-color: var(--primary-900);
  color: var(--primary-300);
}

.nav-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
  width: 20px;
  text-align: center;
}

.nav-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-badge {
  background-color: var(--error-500);
  color: white;
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.nav-arrow {
  font-size: var(--text-xs);
  color: var(--text-muted);
  transition: transform var(--transition-fast);
  flex-shrink: 0;
}

.nav-arrow.rotated {
  transform: rotate(90deg);
}

/* Children Navigation */
.nav-children {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-base);
}

.nav-children.expanded {
  max-height: 500px;
}

.nav-child-item {
  margin-bottom: var(--space-1);
}

.nav-child-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-2) var(--space-2) var(--space-2) var(--space-8);
  background: none;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-muted);
  text-decoration: none;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
}

.nav-child-link:hover {
  background-color: var(--bg-surface-hover);
  color: var(--text-secondary);
}

.nav-child-link.active {
  background-color: var(--primary-50);
  color: var(--primary-600);
}

[data-theme="dark"] .nav-child-link.active {
  background-color: var(--primary-950);
  color: var(--primary-400);
}

.nav-child-icon {
  font-size: var(--text-xs);
  flex-shrink: 0;
  width: 16px;
  text-align: center;
}

.nav-child-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

.user-card {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-xl);
  transition: all var(--transition-fast);
}

.user-card:hover {
  background-color: var(--bg-surface-hover);
}

.user-card.collapsed {
  justify-content: center;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--secondary-400), var(--primary-400));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  color: white;
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--space-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  font-size: var(--text-xs);
  color: var(--success-600);
  font-weight: var(--font-medium);
  line-height: 1;
}

.user-menu-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.user-menu-btn:hover {
  background-color: var(--bg-surface-hover);
  color: var(--text-primary);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

/* Responsive Design */
@media (max-width: 1023px) {
  .sidebar {
    box-shadow: var(--shadow-2xl);
  }
}
