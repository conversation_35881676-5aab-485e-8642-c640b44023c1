import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';

interface NavigationItem {
  label: string;
  icon: string;
  route?: string;
  children?: NavigationItem[];
  badge?: string | number;
  isActive?: boolean;
  isExpanded?: boolean;
}

@Component({
  selector: 'app-modern-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './modern-sidebar.component.html',
  styleUrl: './modern-sidebar.component.css'
})
export class ModernSidebarComponent {
  @Input() isOpen = false;
  @Input() isCollapsed = false;
  @Output() closeSidebar = new EventEmitter<void>();

  navigationItems: NavigationItem[] = [
    {
      label: 'Dashboard',
      icon: '📊',
      route: '/dashboard'
    },
    {
      label: 'Products',
      icon: '📦',
      children: [
        { label: 'All Products', icon: '📋', route: '/products/list' },
        { label: 'Add Product', icon: '➕', route: '/products/create' },
        { label: 'Categories', icon: '📂', route: '/categories/list' },
        { label: 'Tags', icon: '🏷️', route: '/tags/list' }
      ]
    },
    {
      label: 'Inventory',
      icon: '📋',
      children: [
        { label: 'Current Stock', icon: '📊', route: '/inventory/current' },
        { label: 'History', icon: '📈', route: '/inventory/history' },
        { label: 'Reports', icon: '📄', route: '/inventory/reports' }
      ]
    },
    {
      label: 'Sales',
      icon: '💰',
      children: [
        { label: 'Dashboard', icon: '📊', route: '/sales/dashboard' },
        { label: 'Active Sales', icon: '🔄', route: '/sales/active', badge: '3' },
        { label: 'Create Sale', icon: '➕', route: '/sales/create' },
        { label: 'History', icon: '📈', route: '/sales/history' },
        { label: 'Reports', icon: '📄', route: '/sales/reports' }
      ]
    },
    {
      label: 'Analytics',
      icon: '📈',
      children: [
        { label: 'Overview', icon: '📊', route: '/analytics/overview' },
        { label: 'Sales Analytics', icon: '💹', route: '/analytics/sales' },
        { label: 'Product Performance', icon: '📦', route: '/analytics/products' },
        { label: 'Customer Insights', icon: '👥', route: '/analytics/customers' }
      ]
    },
    {
      label: 'Settings',
      icon: '⚙️',
      children: [
        { label: 'General', icon: '🔧', route: '/settings/general' },
        { label: 'Users', icon: '👥', route: '/settings/users' },
        { label: 'Permissions', icon: '🔐', route: '/settings/permissions' },
        { label: 'Integrations', icon: '🔗', route: '/settings/integrations' }
      ]
    }
  ];

  constructor(private router: Router) {
    this.updateActiveStates();
  }

  onItemClick(item: NavigationItem): void {
    if (item.route) {
      this.router.navigate([item.route]);
      this.closeSidebar.emit();
    } else if (item.children) {
      item.isExpanded = !item.isExpanded;
    }
  }

  onChildClick(child: NavigationItem): void {
    if (child.route) {
      this.router.navigate([child.route]);
      this.closeSidebar.emit();
    }
  }

  onOverlayClick(): void {
    this.closeSidebar.emit();
  }

  private updateActiveStates(): void {
    const currentUrl = this.router.url;
    
    this.navigationItems.forEach(item => {
      item.isActive = item.route === currentUrl;
      
      if (item.children) {
        item.children.forEach(child => {
          child.isActive = child.route === currentUrl;
          if (child.isActive) {
            item.isExpanded = true;
          }
        });
      }
    });
  }

  trackByFn(index: number, item: NavigationItem): string {
    return item.label;
  }
}
