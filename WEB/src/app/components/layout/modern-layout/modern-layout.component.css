/* Modern Layout Styles */

.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  transition: background-color var(--transition-base);
}

/* Main Content Area */
.main-content {
  grid-column: span 4 / span 4;
  grid-row: span 4 / span 4;
  grid-column-start: 2;
  grid-row-start: 1;
  transition: all var(--transition-base);
}

/* Desktop Layout */
@media (min-width: 1024px) {
  .main-content {
    /*margin-left: 280px; !* Sidebar width *!*/
    position: absolute;
    overflow: scroll;
    right: 0;
    left: 0;
    height: 100%;
  }

  .app-layout:has(.sidebar-collapsed) .main-content {
    margin-left: 64px; /* Collapsed sidebar width */
  }
}

/* Mobile Layout */
@media (max-width: 1023px) {
  .main-content {
    margin-left: 0;
  }

  .main-content.sidebar-open {
    overflow: hidden;
  }
}

/* App Main Content */
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.app-content {
  flex: 1;
  padding: var(--space-6) 0;
  animation: fadeIn 0.3s ease-out;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Page Transitions */
.page-transition-enter {
  animation: fadeIn 0.3s ease-out;
}

.page-transition-leave {
  animation: fadeIn 0.3s ease-out reverse;
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

[data-theme="dark"] .loading-overlay {
  background-color: rgba(0, 0, 0, 0.8);
}

.loading-spinner-large {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-primary);
  border-radius: 50%;
  border-top-color: var(--primary-500);
  animation: spin 1s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 767px) {
  .app-content {
    padding: var(--space-4) 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-6);
    padding: var(--space-6) 0;
  }

  .footer-bottom {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }

  .footer-actions {
    justify-content: center;
  }
}

@media (max-width: 1023px) {
  .footer-content {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

/* Print Styles */
@media print {
  .app-footer {
    display: none;
  }

  .app-content {
    padding: 0;
  }

  .main-content {
    margin-left: 0 !important;
  }
}
