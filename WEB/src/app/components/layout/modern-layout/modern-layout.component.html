<div class="app-layout" [attr.data-theme]="currentTheme">
  <!-- Sidebar -->
  <app-modern-header
    (toggleSidebar)="onToggleSidebar()"
  ></app-modern-header>


  <div style="flex: 1; display: flex; flex-direction: row;">
    <app-modern-sidebar
      [isOpen]="isSidebarOpen"
      [isCollapsed]="isSidebarCollapsed"
      (closeSidebar)="onCloseSidebar()"
    ></app-modern-sidebar>

    <!-- Main Content -->
    <main class="app-main">
      <div class="app-content">
        <div class="container">
          <!-- Page content will be rendered here -->
          <router-outlet></router-outlet>
        </div>
      </div>
    </main>
  </div>

<!--  &lt;!&ndash; Main Content Area &ndash;&gt;-->
<!--  <div class="main-content" [class.sidebar-open]="isSidebarOpen && !isDesktop">-->
<!--    &lt;!&ndash; Header &ndash;&gt;-->

<!--    &lt;!&ndash; Footer &ndash;&gt;-->
<!--  </div>-->
</div>
<app-footer></app-footer>
