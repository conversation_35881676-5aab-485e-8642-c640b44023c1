import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { ModernHeaderComponent } from '../modern-header/modern-header.component';
import { ModernSidebarComponent } from '../modern-sidebar/modern-sidebar.component';
import { ModernThemeService } from '../../../services/modern-theme.service';
import {FooterComponent} from '../footer/footer.component';

@Component({
  selector: 'app-modern-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    ModernHeaderComponent,
    ModernSidebarComponent,
    FooterComponent
  ],
  templateUrl: './modern-layout.component.html',
  styleUrl: './modern-layout.component.css'
})
export class ModernLayoutComponent implements OnInit, OnDestroy {
  isSidebarOpen = false;
  isSidebarCollapsed = false;
  currentTheme: 'light' | 'dark' = 'light';

  private destroy$ = new Subject<void>();

  constructor(private themeService: ModernThemeService) {}

  ngOnInit(): void {
    // Initialize theme system
    this.themeService.listenToSystemThemeChanges();

    // Subscribe to theme changes
    this.themeService.effectiveTheme$
      .pipe(takeUntil(this.destroy$))
      .subscribe(theme => {
        this.currentTheme = theme;
      });

    // Handle responsive sidebar behavior
    this.handleResize();
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    window.removeEventListener('resize', this.handleResize.bind(this));
  }

  onToggleSidebar(): void {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  onCloseSidebar(): void {
    this.isSidebarOpen = false;
  }

  onToggleSidebarCollapse(): void {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }

  private handleResize(): void {
    const isDesktop = window.innerWidth >= 1024;

    if (isDesktop) {
      this.isSidebarOpen = false; // Desktop sidebar is always visible
    } else {
      // Mobile: sidebar is hidden by default
      if (this.isSidebarOpen) {
        // Keep it open if user explicitly opened it
      }
    }
  }

  // Utility method to check if we're on mobile
  get isMobile(): boolean {
    return window.innerWidth < 1024;
  }

  // Utility method to check if we're on desktop
  get isDesktop(): boolean {
    return window.innerWidth >= 1024;
  }
}
