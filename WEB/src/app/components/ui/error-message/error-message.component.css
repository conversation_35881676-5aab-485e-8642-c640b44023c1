/* Error Message Styles */
.error-message {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  border: 1px solid;
  margin-bottom: var(--spacing-4);
}

.error-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  flex: 1;
}

.error-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
  margin-top: 2px;
}

.error-text {
  flex: 1;
}

.error-title {
  margin: 0 0 var(--spacing-1) 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
}

.error-description {
  margin: 0;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.error-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.error-dismiss {
  padding: var(--spacing-1);
  min-width: auto;
  width: 1.75rem;
  height: 1.75rem;
  font-size: var(--font-size-sm);
  line-height: 1;
}

/* Alert Variants */
.alert-error {
  color: var(--status-error);
  background-color: var(--status-error-bg);
  border-color: var(--status-error-border);
}

.alert-warning {
  color: var(--status-warning);
  background-color: var(--status-warning-bg);
  border-color: var(--status-warning-border);
}

.alert-info {
  color: var(--status-info);
  background-color: var(--status-info-bg);
  border-color: var(--status-info-border);
}

/* Responsive Design */
@media (max-width: 767px) {
  .error-message {
    flex-direction: column;
    align-items: stretch;
  }
  
  .error-actions {
    justify-content: flex-end;
    margin-top: var(--spacing-2);
  }
}
