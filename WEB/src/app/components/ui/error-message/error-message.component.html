<div class="error-message" [class]="'alert-' + type" role="alert">
  <div class="error-content">
    <!-- Icon -->
    <div *ngIf="showIcon" class="error-icon">
      {{ getIcon() }}
    </div>
    
    <!-- Message Content -->
    <div class="error-text">
      <h4 *ngIf="title || getDefaultTitle()" class="error-title">
        {{ title || getDefaultTitle() }}
      </h4>
      <p class="error-description">{{ message }}</p>
    </div>
  </div>

  <!-- Actions -->
  <div class="error-actions">
    <button *ngIf="retryAction" 
            class="btn btn-sm btn-outline"
            (click)="onRetry()"
            type="button">
      {{ retryLabel }}
    </button>
    
    <button *ngIf="dismissible" 
            class="btn btn-sm btn-ghost error-dismiss"
            (click)="onDismiss()"
            type="button"
            aria-label="Dismiss">
      ✕
    </button>
  </div>
</div>
