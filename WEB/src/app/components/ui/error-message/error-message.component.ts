import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export type ErrorType = 'error' | 'warning' | 'info';

@Component({
  selector: 'app-error-message',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './error-message.component.html',
  styleUrl: './error-message.component.css'
})
export class ErrorMessageComponent {
  @Input() message: string = '';
  @Input() type: ErrorType = 'error';
  @Input() title: string = '';
  @Input() dismissible: boolean = true;
  @Input() showIcon: boolean = true;
  @Input() retryAction: boolean = false;
  @Input() retryLabel: string = 'Try Again';
  
  @Output() dismiss = new EventEmitter<void>();
  @Output() retry = new EventEmitter<void>();

  onDismiss(): void {
    this.dismiss.emit();
  }

  onRetry(): void {
    this.retry.emit();
  }

  getIcon(): string {
    switch (this.type) {
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '❌';
    }
  }

  getDefaultTitle(): string {
    switch (this.type) {
      case 'error':
        return 'Error';
      case 'warning':
        return 'Warning';
      case 'info':
        return 'Information';
      default:
        return 'Error';
    }
  }
}
