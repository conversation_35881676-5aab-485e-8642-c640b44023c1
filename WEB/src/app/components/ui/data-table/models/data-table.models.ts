import { TemplateRef } from '@angular/core';

export interface DataTableColumn<T = any> {
  key: string;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  minWidth?: string;
  maxWidth?: string;
  align?: 'left' | 'center' | 'right';
  type?: 'text' | 'number' | 'date' | 'boolean' | 'currency' | 'custom';
  format?: string;
  customTemplate?: TemplateRef<any>;
  headerTemplate?: TemplateRef<any>;
  filterTemplate?: TemplateRef<any>;
  getValue?: (item: T) => any;
  sortValue?: (item: T) => any;
  cssClass?: string;
  headerCssClass?: string;
  cellCssClass?: string;
  visible?: boolean;
  resizable?: boolean;
  sticky?: boolean;
}

export interface DataTableConfig {
  selectable?: boolean;
  multiSelect?: boolean;
  sortable?: boolean;
  filterable?: boolean;
  paginated?: boolean;
  resizable?: boolean;
  striped?: boolean;
  bordered?: boolean;
  hover?: boolean;
  dense?: boolean;
  loading?: boolean;
  trackByFn?: (index: number, item: any) => any;
  emptyMessage?: string;
  loadingMessage?: string;
  pageSize?: number;
  pageSizeOptions?: number[];
  showPageSizeSelector?: boolean;
  showPaginationInfo?: boolean;
  maxHeight?: string;
  stickyHeader?: boolean;
  virtualScrolling?: boolean;
  expandable?: boolean;
  actionColumn?: boolean;
  actionColumnWidth?: string;
  actionColumnLabel?: string;
  actionColumnAlign?: 'left' | 'center' | 'right';
}

export interface DataTableSort {
  column: string;
  direction: 'asc' | 'desc';
}

export interface DataTableFilter {
  column: string;
  value: any;
  operator?: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'between';
}

export interface DataTableSelection<T = any> {
  selected: T[];
  isAllSelected: boolean;
  isIndeterminate: boolean;
}

export interface DataTableAction<T = any> {
  label: string;
  icon?: string;
  action: (item: T) => void;
  visible?: (item: T) => boolean;
  disabled?: (item: T) => boolean;
  cssClass?: string;
  tooltip?: string;
  confirmMessage?: string;
}

export interface DataTableEvent<T = any> {
  type: 'sort' | 'filter' | 'page' | 'select' | 'action' | 'expand';
  data: any;
  item?: T;
}

export interface DataTableState {
  sort?: DataTableSort;
  filters: DataTableFilter[];
  pageNumber: number;
  pageSize: number;
  selectedItems: any[];
  expandedItems: any[];
}

export interface DataTablePaginationInfo {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  startItem: number;
  endItem: number;
}

export type DataTableSize = 'sm' | 'md' | 'lg';
export type DataTableVariant = 'default' | 'striped' | 'bordered' | 'hover';
export type DataTableDensity = 'comfortable' | 'compact' | 'dense';

export interface DataTableTheme {
  size: DataTableSize;
  variant: DataTableVariant;
  density: DataTableDensity;
  colorScheme?: 'light' | 'dark' | 'auto';
}

export interface DataTableExportConfig {
  enabled: boolean;
  formats: ('csv' | 'excel' | 'pdf' | 'json')[];
  filename?: string;
  includeHeaders?: boolean;
  selectedOnly?: boolean;
}

export interface DataTableSearchConfig {
  enabled: boolean;
  placeholder?: string;
  debounceTime?: number;
  searchColumns?: string[];
  caseSensitive?: boolean;
  exactMatch?: boolean;
}
