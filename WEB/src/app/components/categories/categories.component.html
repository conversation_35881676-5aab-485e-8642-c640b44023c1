<div class="categories-container">
  <h2>Categories</h2>
  
  <div *ngIf="loading" class="loading">
    Loading categories...
  </div>
  
  <div *ngIf="error" class="error">
    {{ error }}
    <button (click)="loadCategories()" class="retry-btn">Retry</button>
  </div>
  
  <div *ngIf="!loading && !error && categories.length === 0" class="no-categories">
    No categories found.
  </div>
  
  <div *ngIf="!loading && !error && categories.length > 0" class="categories-list">
    <div *ngFor="let category of categories" class="category-card">
      <h3>{{ category.name }}</h3>
      <p class="category-id">ID: {{ category.id }}</p>
    </div>
  </div>
</div>
