.categories-container {
  padding: 20px;
}

.categories-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.category-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  background-color: #f9f9f9;
  transition: box-shadow 0.2s;
}

.category-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.category-card h3 {
  margin-top: 0;
  color: #333;
}

.category-id {
  color: #666;
  font-size: 0.9em;
  margin: 0;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.error {
  color: red;
  padding: 10px;
  background-color: #ffe6e6;
  border-radius: 4px;
  margin: 10px 0;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
}

.retry-btn:hover {
  background: #0056b3;
}

.no-categories {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}
