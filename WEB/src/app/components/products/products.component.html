<div class="products-container">
  <h2>Products</h2>
  <div *ngIf="loading">Loading products...</div>
  <div *ngIf="error" class="error">Error: {{ error }}</div>
  <div *ngIf="products.length > 0" class="products-list">
    <div *ngFor="let product of products" class="product-card">
      <h3>{{ product.name }}</h3>
      <p>{{ product.description }}</p>
      <p><strong>Category:</strong> {{ product.categoryName }}</p>
      <p><strong>Tags:</strong> {{ product.tags?.join(', ') }}</p>
    </div>
  </div>
  <div *ngIf="!loading && products.length === 0 && !error">
    No products found.
  </div>
</div>
