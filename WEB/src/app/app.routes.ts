import { Routes } from '@angular/router';

export const routes: Routes = [
  // Default redirect to dashboard
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  
  // Dashboard
  {
    path: 'dashboard',
    loadComponent: () => import('./pages/modern-dashboard/modern-dashboard.component').then(m => m.ModernDashboardComponent)
  },
  
  // Products Module
  {
    path: 'products',
    children: [
      {
        path: '',
        redirectTo: 'list',
        pathMatch: 'full'
      },
      {
        path: 'list',
        loadComponent: () => import('./pages/products/modern-product-list/modern-product-list.component').then(m => m.ModernProductListComponent)
      },
      {
        path: 'create',
        loadComponent: () => import('./pages/products/modern-product-create/modern-product-create.component').then(m => m.ModernProductCreateComponent)
      },
      {
        path: ':id/edit',
        loadComponent: () => import('./pages/products/modern-product-edit/modern-product-edit.component').then(m => m.ModernProductEditComponent)
      },
      {
        path: ':id/view',
        loadComponent: () => import('./pages/products/modern-product-detail/modern-product-detail.component').then(m => m.ModernProductDetailComponent)
      }
    ]
  },
  
  // Categories Module
  {
    path: 'categories',
    children: [
      {
        path: '',
        redirectTo: 'list',
        pathMatch: 'full'
      },
      {
        path: 'list',
        loadComponent: () => import('./pages/categories/modern-category-list/modern-category-list.component').then(m => m.ModernCategoryListComponent)
      },
      {
        path: 'create',
        loadComponent: () => import('./pages/categories/modern-category-create/modern-category-create.component').then(m => m.ModernCategoryCreateComponent)
      },
      {
        path: ':id/edit',
        loadComponent: () => import('./pages/categories/modern-category-edit/modern-category-edit.component').then(m => m.ModernCategoryEditComponent)
      }
    ]
  },
  
  // Tags Module
  {
    path: 'tags',
    children: [
      {
        path: '',
        redirectTo: 'list',
        pathMatch: 'full'
      },
      {
        path: 'list',
        loadComponent: () => import('./pages/tags/modern-tag-list/modern-tag-list.component').then(m => m.ModernTagListComponent)
      },
      {
        path: 'create',
        loadComponent: () => import('./pages/tags/modern-tag-create/modern-tag-create.component').then(m => m.ModernTagCreateComponent)
      },
      {
        path: ':id/edit',
        loadComponent: () => import('./pages/tags/modern-tag-edit/modern-tag-edit.component').then(m => m.ModernTagEditComponent)
      }
    ]
  },
  
  // Inventory Module
  {
    path: 'inventory',
    children: [
      {
        path: '',
        redirectTo: 'current',
        pathMatch: 'full'
      },
      {
        path: 'current',
        loadComponent: () => import('./pages/inventory/modern-current-inventory/modern-current-inventory.component').then(m => m.ModernCurrentInventoryComponent)
      },
      {
        path: 'history',
        loadComponent: () => import('./pages/inventory/modern-inventory-history/modern-inventory-history.component').then(m => m.ModernInventoryHistoryComponent)
      },
      {
        path: 'reports',
        loadComponent: () => import('./pages/inventory/modern-inventory-reports/modern-inventory-reports.component').then(m => m.ModernInventoryReportsComponent)
      }
    ]
  },
  
  // Sales Module
  {
    path: 'sales',
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./pages/sales/modern-sales-dashboard/modern-sales-dashboard.component').then(m => m.ModernSalesDashboardComponent)
      },
      {
        path: 'active',
        loadComponent: () => import('./pages/sales/modern-active-sales/modern-active-sales.component').then(m => m.ModernActiveSalesComponent)
      },
      {
        path: 'history',
        loadComponent: () => import('./pages/sales/modern-sales-history/modern-sales-history.component').then(m => m.ModernSalesHistoryComponent)
      },
      {
        path: 'create',
        loadComponent: () => import('./pages/sales/modern-create-sale/modern-create-sale.component').then(m => m.ModernCreateSaleComponent)
      },
      {
        path: ':id/view',
        loadComponent: () => import('./pages/sales/modern-sale-detail/modern-sale-detail.component').then(m => m.ModernSaleDetailComponent)
      },
      {
        path: 'reports',
        loadComponent: () => import('./pages/sales/modern-sales-reports/modern-sales-reports.component').then(m => m.ModernSalesReportsComponent)
      }
    ]
  },
  
  // Analytics Module
  {
    path: 'analytics',
    children: [
      {
        path: '',
        redirectTo: 'overview',
        pathMatch: 'full'
      },
      {
        path: 'overview',
        loadComponent: () => import('./pages/analytics/modern-analytics-overview/modern-analytics-overview.component').then(m => m.ModernAnalyticsOverviewComponent)
      },
      {
        path: 'sales',
        loadComponent: () => import('./pages/analytics/modern-sales-analytics/modern-sales-analytics.component').then(m => m.ModernSalesAnalyticsComponent)
      },
      {
        path: 'products',
        loadComponent: () => import('./pages/analytics/modern-product-analytics/modern-product-analytics.component').then(m => m.ModernProductAnalyticsComponent)
      },
      {
        path: 'customers',
        loadComponent: () => import('./pages/analytics/modern-customer-analytics/modern-customer-analytics.component').then(m => m.ModernCustomerAnalyticsComponent)
      }
    ]
  },
  
  // Settings Module
  {
    path: 'settings',
    children: [
      {
        path: '',
        redirectTo: 'general',
        pathMatch: 'full'
      },
      {
        path: 'general',
        loadComponent: () => import('./pages/settings/modern-general-settings/modern-general-settings.component').then(m => m.ModernGeneralSettingsComponent)
      },
      {
        path: 'users',
        loadComponent: () => import('./pages/settings/modern-user-settings/modern-user-settings.component').then(m => m.ModernUserSettingsComponent)
      },
      {
        path: 'permissions',
        loadComponent: () => import('./pages/settings/modern-permission-settings/modern-permission-settings.component').then(m => m.ModernPermissionSettingsComponent)
      },
      {
        path: 'integrations',
        loadComponent: () => import('./pages/settings/modern-integration-settings/modern-integration-settings.component').then(m => m.ModernIntegrationSettingsComponent)
      }
    ]
  },
  
  // Profile and Account
  {
    path: 'profile',
    loadComponent: () => import('./pages/profile/modern-profile/modern-profile.component').then(m => m.ModernProfileComponent)
  },
  
  // Help and Support
  {
    path: 'help',
    loadComponent: () => import('./pages/help/modern-help/modern-help.component').then(m => m.ModernHelpComponent)
  },
  
  // Activity Log
  {
    path: 'activity',
    loadComponent: () => import('./pages/activity/modern-activity-log/modern-activity-log.component').then(m => m.ModernActivityLogComponent)
  },
  
  // Wildcard route - must be last
  {
    path: '**',
    loadComponent: () => import('./pages/not-found/modern-not-found/modern-not-found.component').then(m => m.ModernNotFoundComponent)
  }
];
