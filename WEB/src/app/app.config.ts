import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient, withFetch, withInterceptors } from '@angular/common/http';

import { routes } from './app.routes';

import { apiInterceptor } from './interceptors/api.interceptor';
import { mockInterceptor } from './interceptors/mock.interceptor';
import { loadingInterceptor } from './core/interceptors/loading.interceptor';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(
      withFetch(), 
      withInterceptors([
        mockInterceptor,
        apiInterceptor,
        
        loadingInterceptor,
        
      ])
    ),
    
  ]
};
