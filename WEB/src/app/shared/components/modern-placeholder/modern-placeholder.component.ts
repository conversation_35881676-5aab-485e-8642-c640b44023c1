import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-modern-placeholder',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './modern-placeholder.component.html',
  styleUrl: './modern-placeholder.component.css'
})
export class ModernPlaceholderComponent {
  @Input() title = 'Coming Soon';
  @Input() subtitle = 'This feature is under development';
  @Input() icon = '🚧';
  @Input() description = 'We\'re working hard to bring you this feature. Check back soon for updates!';
  @Input() showBackButton = true;
  @Input() backRoute = '/dashboard';
  @Input() backLabel = 'Back to Dashboard';
}
