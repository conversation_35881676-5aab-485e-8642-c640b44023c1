/* Modern Placeholder Styles */

.placeholder-container {
  min-height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) 0;
  animation: fadeIn 0.5s ease-out;
}

.placeholder-content {
  max-width: 600px;
  text-align: center;
  padding: var(--space-8);
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
}

.placeholder-icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-6);
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.placeholder-title {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.placeholder-subtitle {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-xl);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.placeholder-description {
  margin: 0 0 var(--space-8) 0;
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.placeholder-actions {
  margin-bottom: var(--space-8);
}

.placeholder-features {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  text-align: left;
}

.features-title {
  margin: 0 0 var(--space-4) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-align: center;
}

.features-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.feature-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 767px) {
  .placeholder-content {
    margin: 0 var(--space-4);
    padding: var(--space-6);
  }
  
  .placeholder-title {
    font-size: var(--text-3xl);
  }
  
  .placeholder-subtitle {
    font-size: var(--text-lg);
  }
}
