import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface SimpleTableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
}

export interface SimpleTableAction<T> {
  label: string;
  icon?: string;
  action: (item: T) => void;
  condition?: (item: T) => boolean;
}

export interface SimpleTableConfig<T> {
  columns: SimpleTableColumn<T>[];
  data: T[];
  actions?: SimpleTableAction<T>[];
  selectable?: boolean;
}

@Component({
  selector: 'app-simple-data-table',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="table-container">
      <table class="table">
        <thead>
          <tr>
            <th *ngIf="config.selectable">
              <input type="checkbox" (change)="toggleSelectAll($event)">
            </th>
            <th *ngFor="let column of config.columns">
              {{ column.label }}
            </th>
            <th *ngIf="config.actions && config.actions.length > 0">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of config.data; trackBy: trackByFn">
            <td *ngIf="config.selectable">
              <input type="checkbox" [checked]="isSelected(item)" (change)="toggleSelect(item, $event)">
            </td>
            <td *ngFor="let column of config.columns">
              {{ getColumnValue(item, column.key) }}
            </td>
            <td *ngIf="config.actions && config.actions.length > 0">
              <button 
                *ngFor="let action of config.actions" 
                class="btn btn-sm"
                (click)="action.action(item)"
                [disabled]="action.condition && !action.condition(item)">
                {{ action.icon || '' }} {{ action.label }}
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  `,
  styles: [`
    .table-container {
      overflow-x: auto;
    }
    .table {
      width: 100%;
      border-collapse: collapse;
    }
    .table th,
    .table td {
      padding: 8px 12px;
      border: 1px solid #ddd;
      text-align: left;
    }
    .table th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    .btn {
      padding: 4px 8px;
      margin: 0 2px;
      border: 1px solid #ccc;
      background: #fff;
      cursor: pointer;
    }
    .btn:hover {
      background: #f0f0f0;
    }
    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  `]
})
export class SimpleDataTableComponent<T> {
  @Input() config!: SimpleTableConfig<T>;
  @Output() selectionChange = new EventEmitter<T[]>();

  private selectedItems: T[] = [];

  trackByFn(index: number, item: T): any {
    return (item as any).id || index;
  }

  getColumnValue(item: T, key: keyof T): any {
    return item[key];
  }

  isSelected(item: T): boolean {
    return this.selectedItems.includes(item);
  }

  toggleSelect(item: T, event: Event): void {
    const target = event.target as HTMLInputElement;
    if (target.checked) {
      this.selectedItems.push(item);
    } else {
      const index = this.selectedItems.indexOf(item);
      if (index > -1) {
        this.selectedItems.splice(index, 1);
      }
    }
    this.selectionChange.emit([...this.selectedItems]);
  }

  toggleSelectAll(event: Event): void {
    const target = event.target as HTMLInputElement;
    if (target.checked) {
      this.selectedItems = [...this.config.data];
    } else {
      this.selectedItems = [];
    }
    this.selectionChange.emit([...this.selectedItems]);
  }
}
