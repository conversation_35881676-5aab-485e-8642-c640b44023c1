.data-table-container {
  position: relative;
  background: var(--surface-color, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  padding: 20px;
  font-size: 16px;
  color: var(--primary-color, #007bff);
}

.table-wrapper {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
}

.data-table th {
  background: var(--surface-variant, #f5f5f5);
  font-weight: 600;
  color: var(--on-surface, #333);
  position: sticky;
  top: 0;
  z-index: 5;
}

.data-table tbody tr:hover {
  background: var(--surface-hover, #f9f9f9);
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.sortable {
  cursor: pointer;
  user-select: none;
}

.sortable:hover {
  background: var(--surface-hover, #eeeeee);
}

.sort-icon {
  font-size: 12px;
  color: var(--text-secondary, #666);
}

.filter-input {
  margin-top: 8px;
}

.filter-field {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  font-size: 12px;
}

.select-column,
.select-cell {
  width: 40px;
  text-align: center;
}

.select-all-checkbox,
.select-checkbox {
  cursor: pointer;
}

.actions-column,
.actions-cell {
  width: 120px;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 4px 8px;
  border: 1px solid var(--border-color, #ddd);
  background: var(--surface-color, #fff);
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--primary-color, #007bff);
  color: white;
  border-color: var(--primary-color, #007bff);
}

.action-icon {
  font-size: 10px;
}

.empty-row {
  text-align: center;
}

.empty-cell {
  padding: 40px;
  color: var(--text-secondary, #666);
  font-style: italic;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: var(--surface-variant, #f5f5f5);
  border-top: 1px solid var(--border-color, #e0e0e0);
}

.pagination-info {
  font-size: 14px;
  color: var(--text-secondary, #666);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.page-size-select {
  padding: 4px 8px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  background: var(--surface-color, #fff);
}

.page-navigation {
  display: flex;
  gap: 4px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #ddd);
  background: var(--surface-color, #fff);
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: var(--primary-color, #007bff);
  color: white;
  border-color: var(--primary-color, #007bff);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn.active {
  background: var(--primary-color, #007bff);
  color: white;
  border-color: var(--primary-color, #007bff);
}

.cell-currency {
  text-align: right;
  font-family: monospace;
}

.cell-number {
  text-align: right;
}

.cell-date {
  white-space: nowrap;
}

.cell-boolean {
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-table-container {
    margin: 0 -16px;
    border-radius: 0;
  }
  
  .data-table th,
  .data-table td {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .pagination-controls {
    justify-content: center;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
