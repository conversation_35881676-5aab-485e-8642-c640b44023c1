<div class="data-table-container">
  <!-- Loading State -->
  <div *ngIf="config.loading" class="loading-overlay">
    <div class="loading-spinner">Loading...</div>
  </div>

  <!-- Table -->
  <div class="table-wrapper">
    <table class="data-table">
      <thead>
        <tr>
          <!-- Selection Column -->
          <th *ngIf="config.selectable" class="select-column">
            <input 
              type="checkbox" 
              [checked]="allSelected()"
              (change)="onSelectAll()"
              class="select-all-checkbox">
          </th>
          
          <!-- Data Columns -->
          <th 
            *ngFor="let column of config.columns" 
            [style.width]="column.width"
            [class.sortable]="column.sortable"
            (click)="onSort(column)">
            <div class="column-header">
              <span>{{ column.label }}</span>
              <span *ngIf="column.sortable" class="sort-icon">
                {{ getSortIcon(column) }}
              </span>
            </div>
            
            <!-- Filter Input -->
            <div *ngIf="column.filterable" class="filter-input">
              <input 
                type="text" 
                placeholder="Filter..."
                (input)="onFilter(column, ($event.target as HTMLInputElement).value)"
                class="filter-field">
            </div>
          </th>
          
          <!-- Actions Column -->
          <th *ngIf="config.actions && config.actions.length > 0" class="actions-column">
            Actions
          </th>
        </tr>
      </thead>
      
      <tbody>
        <tr *ngFor="let item of config.data; trackBy: trackByFn" class="data-row">
          <!-- Selection Cell -->
          <td *ngIf="config.selectable" class="select-cell">
            <input 
              type="checkbox" 
              [checked]="isSelected(item)"
              (change)="onSelectItem(item)"
              class="select-checkbox">
          </td>
          
          <!-- Data Cells -->
          <td *ngFor="let column of config.columns" [class]="'cell-' + column.type">
            <ng-container *ngIf="column.template; else defaultCell">
              <ng-container 
                *ngTemplateOutlet="column.template; context: { $implicit: item, column: column }">
              </ng-container>
            </ng-container>
            
            <ng-template #defaultCell>
              <span [ngSwitch]="column.type">
                <span *ngSwitchCase="'currency'">
                  {{ item[column.key] | currency }}
                </span>
                <span *ngSwitchCase="'date'">
                  {{ item[column.key] | date }}
                </span>
                <span *ngSwitchCase="'boolean'">
                  {{ item[column.key] ? 'Yes' : 'No' }}
                </span>
                <span *ngSwitchDefault>
                  {{ item[column.key] }}
                </span>
              </span>
            </ng-template>
          </td>
          
          <!-- Actions Cell -->
          <td *ngIf="config.actions && config.actions.length > 0" class="actions-cell">
            <div class="action-buttons">
              <button 
                *ngFor="let action of config.actions" *ngIf="canShowAction(action, item)"
                *ngIf="canShowAction(action, item)"
                (click)="onAction(action.action, item)"
                [class]="'action-btn ' + (action.class || '')"
                [title]="action.label">
                <span *ngIf="action.icon" class="action-icon">{{ action.icon }}</span>
                <span class="action-label">{{ action.label }}</span>
              </button>
            </div>
          </td>
        </tr>
        
        <!-- Empty State -->
        <tr *ngIf="config.data.length === 0 && !config.loading" class="empty-row">
          <td [attr.colspan]="getColumnCount()" class="empty-cell">
            No data available
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <div *ngIf="config.pagination" class="pagination-container">
    <div class="pagination-info">
      Showing {{ getStartItem() }} to {{ getEndItem() }} of {{ config.pagination.totalCount }} entries
    </div>
    
    <div class="pagination-controls">
      <!-- Page Size Selector -->
      <div class="page-size-selector">
        <label>Show:</label>
        <select 
          [value]="config.pagination.pageSize"
          (change)="onPageSizeChange(+($event.target as HTMLSelectElement).value)"
          class="page-size-select">
          <option *ngFor="let size of config.pagination.pageSizeOptions" [value]="size">
            {{ size }}
          </option>
        </select>
        <label>entries</label>
      </div>
      
      <!-- Page Navigation -->
      <div class="page-navigation">
        <button 
          (click)="onPageChange(config.pagination.pageNumber - 1)"
          [disabled]="config.pagination.pageNumber <= 1"
          class="page-btn prev-btn">
          Previous
        </button>
        
        <button 
          *ngFor="let page of getPages()"
          (click)="onPageChange(page)"
          [class.active]="page === config.pagination.pageNumber"
          class="page-btn page-number">
          {{ page }}
        </button>
        
        <button 
          (click)="onPageChange(config.pagination.pageNumber + 1)"
          [disabled]="config.pagination.pageNumber >= getTotalPages()"
          class="page-btn next-btn">
          Next
        </button>
      </div>
    </div>
  </div>
</div>
