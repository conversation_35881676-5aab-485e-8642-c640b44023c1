import { Component, Input, Output, EventEmitter, TemplateRef, ChangeDetectionStrategy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SortConfig, QueryParams } from '../../../models/common.models';

export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  type?: 'text' | 'number' | 'date' | 'boolean' | 'currency' | 'custom';
  width?: string;
  template?: TemplateRef<any>;
  pipe?: string;
  pipeArgs?: any[];
}

export interface TableAction<T> {
  label: string;
  icon?: string;
  action: string;
  condition?: (item: T) => boolean;
  class?: string;
}

export interface PaginationConfig {
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  pageSizeOptions: number[];
}

export interface TableConfig<T> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  selectable?: boolean;
  pagination?: PaginationConfig;
  sorting?: SortConfig;
  filtering?: boolean;
  actions?: TableAction<T>[];
}

@Component({
  selector: 'app-data-table',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './data-table.component.html',
  styleUrl: './data-table.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DataTableComponent<T> {
  @Input() config!: TableConfig<T>;
  @Output() sortChange = new EventEmitter<SortConfig>();
  @Output() filterChange = new EventEmitter<any>();
  @Output() selectionChange = new EventEmitter<T[]>();
  @Output() actionClick = new EventEmitter<{action: string, item: T}>();
  @Output() pageChange = new EventEmitter<{pageNumber: number, pageSize: number}>();

  selectedItems = signal<T[]>([]);
  filters = signal<Record<string, any>>({});
  allSelected = signal(false);

  onSort(column: TableColumn<T>): void {
    if (!column.sortable) return;

    const currentSort = this.config.sorting;
    let direction: 'asc' | 'desc' = 'asc';

    if (currentSort?.field === column.key) {
      direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    }

    this.sortChange.emit({
      field: column.key as string,
      direction
    });
  }

  onFilter(column: TableColumn<T>, value: any): void {
    const currentFilters = this.filters();
    const newFilters = { ...currentFilters, [column.key]: value };
    this.filters.set(newFilters);
    this.filterChange.emit(newFilters);
  }

  onSelectAll(): void {
    const newAllSelected = !this.allSelected();
    this.allSelected.set(newAllSelected);
    
    if (newAllSelected) {
      this.selectedItems.set([...this.config.data]);
    } else {
      this.selectedItems.set([]);
    }
    
    this.selectionChange.emit(this.selectedItems());
  }

  onSelectItem(item: T): void {
    const selected = this.selectedItems();
    const index = selected.indexOf(item);
    
    if (index > -1) {
      selected.splice(index, 1);
    } else {
      selected.push(item);
    }
    
    this.selectedItems.set([...selected]);
    this.allSelected.set(selected.length === this.config.data.length);
    this.selectionChange.emit(this.selectedItems());
  }

  onAction(action: string, item: T): void {
    this.actionClick.emit({ action, item });
  }

  onPageChange(pageNumber: number): void {
    if (this.config.pagination) {
      this.pageChange.emit({
        pageNumber,
        pageSize: this.config.pagination.pageSize
      });
    }
  }

  onPageSizeChange(pageSize: number): void {
    if (this.config.pagination) {
      this.pageChange.emit({
        pageNumber: 1,
        pageSize
      });
    }
  }

  isSelected(item: T): boolean {
    return this.selectedItems().includes(item);
  }

  getSortIcon(column: TableColumn<T>): string {
    if (!column.sortable) return '';
    
    const currentSort = this.config.sorting;
    if (currentSort?.field !== column.key) return 'sort';
    
    return currentSort.direction === 'asc' ? 'sort-up' : 'sort-down';
  }

  getPages(): number[] {
    if (!this.config.pagination) return [];
    
    const { pageNumber, totalCount, pageSize } = this.config.pagination;
    const totalPages = Math.ceil(totalCount / pageSize);
    const pages: number[] = [];
    
    const startPage = Math.max(1, pageNumber - 2);
    const endPage = Math.min(totalPages, pageNumber + 2);
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  canShowAction(action: TableAction<T>, item: T): boolean {
    return !action.condition || action.condition(item);
  }

  trackByFn(index: number, item: T): any {
    return (item as any).id || index;
  }

  getColumnCount(): number {
    let count = this.config.columns.length;
    if (this.config.selectable) count++;
    if (this.config.actions && this.config.actions.length > 0) count++;
    return count;
  }

  getStartItem(): number {
    if (!this.config.pagination) return 0;
    return (this.config.pagination.pageNumber - 1) * this.config.pagination.pageSize + 1;
  }

  getEndItem(): number {
    if (!this.config.pagination) return this.config.data.length;
    const end = this.config.pagination.pageNumber * this.config.pagination.pageSize;
    return Math.min(end, this.config.pagination.totalCount);
  }

  getTotalPages(): number {
  }

  getPages(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
    return pages;

    if (!this.config.pagination) return 1;
    return Math.ceil(this.config.pagination.totalCount / this.config.pagination.pageSize);

    return !action.condition || action.condition(item);
  }
}

