<div class="product-form-container">
  <form [formGroup]="form" (ngSubmit)="onSubmit()" class="product-form">
    
    <!-- Form Header -->
    <div class="form-header">
      <h2 class="form-title">{{ formTitle }}</h2>
      <div *ngIf="hasUnsavedChanges()" class="unsaved-indicator">
        <span class="unsaved-icon">●</span>
        <span class="unsaved-text">Unsaved changes</span>
      </div>
    </div>

    <!-- Form Fields -->
    <div class="form-body">
      
      <!-- Product Name -->
      <div class="form-field">
        <label for="name" class="field-label required">
          Product Name
        </label>
        <input
          id="name"
          type="text"
          formControlName="name"
          placeholder="Enter product name"
          [class.invalid]="isFieldInvalid('name')"
          [class.valid]="isFieldValid('name')"
          class="field-input">
        <div *ngIf="getFieldError('name')" class="field-error">
          {{ getFieldError('name') }}
        </div>
      </div>

      <!-- Description -->
      <div class="form-field">
        <label for="description" class="field-label required">
          Description
        </label>
        <textarea
          id="description"
          formControlName="description"
          placeholder="Enter product description"
          rows="4"
          [class.invalid]="isFieldInvalid('description')"
          [class.valid]="isFieldValid('description')"
          class="field-textarea"></textarea>
        <div *ngIf="getFieldError('description')" class="field-error">
          {{ getFieldError('description') }}
        </div>
        <div class="field-hint">
          Provide a detailed description of the product (10-500 characters)
        </div>
      </div>

      <!-- Category -->
      <div class="form-field">
        <label for="categoryId" class="field-label required">
          Category
        </label>
        <select
          id="categoryId"
          formControlName="categoryId"
          [class.invalid]="isFieldInvalid('categoryId')"
          [class.valid]="isFieldValid('categoryId')"
          class="field-select">
          <option value="">Select a category</option>
          <option 
            *ngFor="let category of config.categories" 
            [value]="category.id">
            {{ category.name }}
          </option>
        </select>
        <div *ngIf="getFieldError('categoryId')" class="field-error">
          {{ getFieldError('categoryId') }}
        </div>
      </div>

      <!-- Tags -->
      <div class="form-field">
        <label class="field-label">
          Tags
          <span class="optional-indicator">(optional)</span>
        </label>
        <div class="tags-selection">
          <div class="tag-checkboxes">
            <label 
              *ngFor="let tag of config.tags" 
              class="tag-checkbox">
              <input 
                type="checkbox"
                [checked]="isTagSelected(tag.id)"
                (change)="onTagToggle(tag.id, $event.target.checked)">
              <span class="tag-label">{{ tag.name }}</span>
            </label>
          </div>
        </div>
        <div class="field-hint">
          Select one or more tags to categorize this product
        </div>
      </div>

    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <div class="action-group">
        <button
          type="button"
          (click)="onCancel()"
          [disabled]="loading"
          class="btn btn-secondary">
          Cancel
        </button>
        
        <button
          type="button"
          (click)="onReset()"
          [disabled]="loading || !hasUnsavedChanges()"
          class="btn btn-outline">
          Reset
        </button>
      </div>

      <button
        type="submit"
        [disabled]="!canSubmit"
        class="btn btn-primary">
        <span *ngIf="loading" class="loading-spinner"></span>
        {{ submitButtonText }}
      </button>
    </div>

    <!-- Form Validation Summary -->
    <div *ngIf="showValidationErrors() && form.invalid" class="validation-summary">
      <div class="validation-header">
        <span class="validation-icon">⚠️</span>
        <span class="validation-title">Please fix the following errors:</span>
      </div>
      <ul class="validation-list">
        <li *ngIf="getFieldError('name')" class="validation-item">
          {{ getFieldError('name') }}
        </li>
        <li *ngIf="getFieldError('description')" class="validation-item">
          {{ getFieldError('description') }}
        </li>
        <li *ngIf="getFieldError('categoryId')" class="validation-item">
          {{ getFieldError('categoryId') }}
        </li>
      </ul>
    </div>

  </form>
</div>
