import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { FormBuilderService, FormFieldConfig } from '../../services/form-builder.service';
import { ViewProductDto, CreateProductDto, UpdateProductDto, ProductCategoryDto, TagDto } from '../../../models';

export interface ProductFormData {
  name: string;
  description: string;
  categoryId: number;
  tagIds: number[];
}

export interface ProductFormConfig {
  mode: 'create' | 'edit';
  initialData?: ViewProductDto;
  categories: ProductCategoryDto[];
  tags: TagDto[];
  showAdvanced?: boolean;
}

@Component({
  selector: 'app-product-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './product-form.component.html',
  styleUrl: './product-form.component.css'
})
export class ProductFormComponent implements OnInit, OnDestroy {
  @Input() config!: ProductFormConfig;
  @Input() loading = false;
  @Input() disabled = false;

  @Output() formSubmit = new EventEmitter<ProductFormData>();
  @Output() formCancel = new EventEmitter<void>();
  @Output() formChange = new EventEmitter<ProductFormData>();

  private destroy$ = new Subject<void>();

  form!: FormGroup;
  hasUnsavedChanges = signal(false);
  showValidationErrors = signal(false);

  // Form field configurations
  formFields: FormFieldConfig[] = [];

  constructor(private formBuilder: FormBuilderService) {}

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormChangeTracking();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.formFields = this.buildFormFields();
    this.form = this.formBuilder.createForm(this.formFields);

    if (this.config.initialData) {
      this.populateForm(this.config.initialData);
    }

    if (this.disabled) {
      this.form.disable();
    }
  }

  private buildFormFields(): FormFieldConfig[] {
    const categoryOptions = this.config.categories.map(cat => ({
      value: cat.id,
      label: cat.name
    }));

    const tagOptions = this.config.tags.map(tag => ({
      value: tag.id,
      label: tag.name
    }));

    return [
      {
        key: 'name',
        type: 'text',
        label: 'Product Name',
        placeholder: 'Enter product name',
        required: true,
        validators: [Validators.minLength(2), Validators.maxLength(100)],
        errorMessages: {
          required: 'Product name is required',
          minlength: 'Product name must be at least 2 characters',
          maxlength: 'Product name cannot exceed 100 characters'
        }
      },
      {
        key: 'description',
        type: 'textarea',
        label: 'Description',
        placeholder: 'Enter product description',
        required: true,
        rows: 4,
        validators: [Validators.minLength(10), Validators.maxLength(500)],
        errorMessages: {
          required: 'Description is required',
          minlength: 'Description must be at least 10 characters',
          maxlength: 'Description cannot exceed 500 characters'
        }
      },
      {
        key: 'categoryId',
        type: 'select',
        label: 'Category',
        required: true,
        options: categoryOptions,
        errorMessages: {
          required: 'Please select a category'
        }
      },
      {
        key: 'tagIds',
        type: 'multiselect',
        label: 'Tags',
        options: tagOptions,
        multiple: true,
        hint: 'Select one or more tags (optional)'
      }
    ];
  }

  private populateForm(data: ViewProductDto): void {
    // Convert tag names back to IDs
    const tagIds = data.tags?.map(tagName => {
      const tag = this.config.tags.find(t => t.name === tagName);
      return tag ? tag.id : null;
    }).filter(id => id !== null) || [];

    // Find category ID by name
    const category = this.config.categories.find(c => c.name === data.categoryName);
    const categoryId = category ? category.id : null;

    this.form.patchValue({
      name: data.name,
      description: data.description,
      categoryId: categoryId,
      tagIds: tagIds
    });

    this.hasUnsavedChanges.set(false);
  }

  private setupFormChangeTracking(): void {
    this.form.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.hasUnsavedChanges.set(this.form.dirty);
        this.formChange.emit(value);
      });
  }

  onSubmit(): void {
    if (this.form.valid) {
      this.showValidationErrors.set(false);
      const formData: ProductFormData = this.form.value;
      this.formSubmit.emit(formData);
    } else {
      this.showValidationErrors.set(true);
      this.markAllFieldsAsTouched();
    }
  }

  onCancel(): void {
    if (this.hasUnsavedChanges() && !this.confirmDiscard()) {
      return;
    }
    this.formCancel.emit();
  }

  onReset(): void {
    if (this.hasUnsavedChanges() && !this.confirmDiscard()) {
      return;
    }

    if (this.config.initialData) {
      this.populateForm(this.config.initialData);
    } else {
      this.form.reset();
    }
    
    this.hasUnsavedChanges.set(false);
    this.showValidationErrors.set(false);
  }

  getFieldError(fieldKey: string): string {
    const control = this.form.get(fieldKey);
    const field = this.formFields.find(f => f.key === fieldKey);
    
    if (!control || !field) return '';
    
    if (control.invalid && (control.touched || this.showValidationErrors())) {
      return this.formBuilder.getErrorMessage(control, field.label, field.errorMessages);
    }
    
    return '';
  }

  isFieldInvalid(fieldKey: string): boolean {
    const control = this.form.get(fieldKey);
    return !!(control && control.invalid && (control.touched || this.showValidationErrors()));
  }

  isFieldValid(fieldKey: string): boolean {
    const control = this.form.get(fieldKey);
    return !!(control && control.valid && control.touched);
  }

  getFieldHint(fieldKey: string): string {
    const field = this.formFields.find(f => f.key === fieldKey);
    return field?.hint || '';
  }

  private markAllFieldsAsTouched(): void {
    Object.keys(this.form.controls).forEach(key => {
      this.form.get(key)?.markAsTouched();
    });
  }

  private confirmDiscard(): boolean {
    return confirm('You have unsaved changes. Are you sure you want to discard them?');
  }

  // Utility methods for template
  get isCreateMode(): boolean {
    return this.config.mode === 'create';
  }

  get isEditMode(): boolean {
    return this.config.mode === 'edit';
  }

  get formTitle(): string {
    return this.isCreateMode ? 'Create Product' : 'Edit Product';
  }

  get submitButtonText(): string {
    if (this.loading) {
      return this.isCreateMode ? 'Creating...' : 'Updating...';
    }
    return this.isCreateMode ? 'Create Product' : 'Update Product';
  }

  get canSubmit(): boolean {
    return this.form.valid && !this.loading && !this.disabled;
  }

  // Methods for handling multi-select
  onTagSelectionChange(event: Event): void {
    const select = event.target as HTMLSelectElement;
    const selectedOptions = Array.from(select.selectedOptions);
    const selectedValues = selectedOptions.map(option => +option.value);
    
    this.form.patchValue({ tagIds: selectedValues });
  }

  isTagSelected(tagId: number): boolean {
    const selectedTags = this.form.get('tagIds')?.value || [];
    return selectedTags.includes(tagId);
  }
}

  onTagToggle(tagId: number, checked: boolean): void {
    const currentTags = this.form.get('tagIds')?.value || [];
    let updatedTags: number[];
    
    if (checked) {
      updatedTags = [...currentTags, tagId];
    } else {
      updatedTags = currentTags.filter((id: number) => id !== tagId);
    }
    
    this.form.patchValue({ tagIds: updatedTags });
  }
}
