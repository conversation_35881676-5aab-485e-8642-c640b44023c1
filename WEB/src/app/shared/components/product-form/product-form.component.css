.product-form-container {
  max-width: 600px;
  margin: 0 auto;
  background: var(--surface-color, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.product-form {
  padding: 24px;
}

/* Form Header */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary, #333);
  margin: 0;
}

.unsaved-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--warning-color, #f39c12);
  font-size: 14px;
}

.unsaved-icon {
  font-size: 12px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.unsaved-text {
  font-weight: 500;
}

/* Form Body */
.form-body {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 32px;
}

/* Form Fields */
.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #333);
  display: flex;
  align-items: center;
  gap: 4px;
}

.field-label.required::after {
  content: '*';
  color: var(--danger-color, #dc3545);
  font-weight: bold;
}

.optional-indicator {
  font-weight: 400;
  color: var(--text-secondary, #666);
  font-size: 12px;
}

.field-input,
.field-textarea,
.field-select {
  padding: 10px 12px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  font-size: 14px;
  background: var(--surface-color, #fff);
  color: var(--text-primary, #333);
  transition: all 0.2s ease;
}

.field-input:focus,
.field-textarea:focus,
.field-select:focus {
  outline: none;
  border-color: var(--primary-color, #007bff);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.field-input.invalid,
.field-textarea.invalid,
.field-select.invalid {
  border-color: var(--danger-color, #dc3545);
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.field-input.valid,
.field-textarea.valid,
.field-select.valid {
  border-color: var(--success-color, #28a745);
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
}

.field-textarea {
  resize: vertical;
  min-height: 80px;
}

.field-select {
  cursor: pointer;
}

.field-error {
  color: var(--danger-color, #dc3545);
  font-size: 12px;
  font-weight: 500;
  margin-top: 4px;
}

.field-hint {
  color: var(--text-secondary, #666);
  font-size: 12px;
  margin-top: 4px;
}

/* Tags Selection */
.tags-selection {
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  padding: 12px;
  background: var(--surface-variant, #f8f9fa);
}

.tag-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  max-height: 120px;
  overflow-y: auto;
}

.tag-checkbox {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
}

.tag-checkbox input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.tag-label {
  color: var(--text-primary, #333);
  user-select: none;
}

.tag-checkbox:hover .tag-label {
  color: var(--primary-color, #007bff);
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color, #e0e0e0);
}

.action-group {
  display: flex;
  gap: 12px;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color, #007bff);
  color: white;
  border-color: var(--primary-color, #007bff);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover, #0056b3);
  border-color: var(--primary-hover, #0056b3);
}

.btn-secondary {
  background: var(--surface-color, #fff);
  color: var(--text-primary, #333);
  border-color: var(--border-color, #ddd);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--surface-hover, #f5f5f5);
}

.btn-outline {
  background: transparent;
  color: var(--text-secondary, #666);
  border-color: var(--border-color, #ddd);
}

.btn-outline:hover:not(:disabled) {
  background: var(--surface-hover, #f5f5f5);
  color: var(--text-primary, #333);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Validation Summary */
.validation-summary {
  margin-top: 20px;
  padding: 16px;
  background: var(--error-light, #f8d7da);
  border: 1px solid var(--error-border, #f5c6cb);
  border-radius: 6px;
}

.validation-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.validation-icon {
  font-size: 18px;
}

.validation-title {
  font-weight: 600;
  color: var(--error-color, #721c24);
}

.validation-list {
  margin: 0;
  padding-left: 20px;
}

.validation-item {
  color: var(--error-color, #721c24);
  font-size: 14px;
  margin-bottom: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-form-container {
    margin: 0 16px;
    border-radius: 0;
    box-shadow: none;
    border: 1px solid var(--border-color, #e0e0e0);
  }
  
  .product-form {
    padding: 16px;
  }
  
  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .form-title {
    font-size: 20px;
  }
  
  .form-actions {
    flex-direction: column-reverse;
    align-items: stretch;
  }
  
  .action-group {
    justify-content: center;
  }
  
  .btn {
    min-width: auto;
  }
  
  .tag-checkboxes {
    max-height: 100px;
  }
}
