<div class="search-bar-container">
  <div class="search-input-wrapper">
    <div class="search-icon">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.35-4.35"></path>
      </svg>
    </div>
    
    <input
      type="text"
      [formControl]="searchControl"
      [placeholder]="config.placeholder"
      (focus)="onFocus()"
      (blur)="onBlur()"
      (keydown)="onKeyDown($event)"
      class="search-input"
      autocomplete="off">
    
    <div class="search-actions">
      <div *ngIf="loading" class="loading-indicator">
        <div class="spinner"></div>
      </div>
      
      <button
        *ngIf="config.showClearButton && hasValue && !loading"
        (click)="onClear()"
        class="clear-button"
        type="button"
        aria-label="Clear search">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
  </div>
  
  <!-- Suggestions Dropdown -->
  <div 
    *ngIf="showSuggestions() && filteredSuggestions().length > 0"
    class="suggestions-dropdown">
    <ul class="suggestions-list">
      <li 
        *ngFor="let suggestion of filteredSuggestions(); trackBy: trackBySuggestion"
        (click)="onSuggestionClick(suggestion)"
        class="suggestion-item">
        <div class="suggestion-icon">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.35-4.35"></path>
          </svg>
        </div>
        <span class="suggestion-text">{{ suggestion }}</span>
      </li>
    </ul>
  </div>
</div>
