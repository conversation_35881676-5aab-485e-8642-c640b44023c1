import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

export interface SearchConfig {
  placeholder: string;
  debounceTime: number;
  minLength: number;
  suggestions?: boolean;
  showClearButton?: boolean;
}

@Component({
  selector: 'app-search-bar',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './search-bar.component.html',
  styleUrl: './search-bar.component.css'
})
export class SearchBarComponent implements OnInit, OnDestroy {
  @Input() config: SearchConfig = {
    placeholder: 'Search...',
    debounceTime: 300,
    minLength: 2,
    suggestions: false,
    showClearButton: true
  };
  
  @Input() suggestions: string[] = [];
  @Input() loading = false;
  
  @Output() search = new EventEmitter<string>();
  @Output() clear = new EventEmitter<void>();
  @Output() suggestionSelected = new EventEmitter<string>();

  searchControl = new FormControl('');
  showSuggestions = signal(false);
  filteredSuggestions = signal<string[]>([]);
  
  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.searchControl.valueChanges
      .pipe(
        debounceTime(this.config.debounceTime),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        const searchTerm = value?.trim() || '';
        
        if (searchTerm.length >= this.config.minLength) {
          this.search.emit(searchTerm);
          this.updateSuggestions(searchTerm);
        } else if (searchTerm.length === 0) {
          this.search.emit('');
          this.hideSuggestions();
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onFocus(): void {
    if (this.config.suggestions && this.suggestions.length > 0) {
      this.updateSuggestions(this.searchControl.value || '');
    }
  }

  onBlur(): void {
    setTimeout(() => {
      this.hideSuggestions();
    }, 200);
  }

  onClear(): void {
    this.searchControl.setValue('');
    this.hideSuggestions();
    this.clear.emit();
  }

  onSuggestionClick(suggestion: string): void {
  }

  trackBySuggestion(index: number, suggestion: string): string {
    return suggestion;

    this.searchControl.setValue(suggestion);
    this.hideSuggestions();
    this.suggestionSelected.emit(suggestion);
    this.search.emit(suggestion);
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.hideSuggestions();
    }
  }

  private updateSuggestions(searchTerm: string): void {
    if (!this.config.suggestions || !searchTerm) {
      this.hideSuggestions();
      return;
    }

    const filtered = this.suggestions.filter(suggestion =>
      suggestion.toLowerCase().includes(searchTerm.toLowerCase())
    );

    this.filteredSuggestions.set(filtered);
    this.showSuggestions.set(filtered.length > 0);
  }

  private hideSuggestions(): void {
    this.showSuggestions.set(false);
    this.filteredSuggestions.set([]);
  }

  get hasValue(): boolean {
    return !!(this.searchControl.value?.trim());
  }
}

