.search-bar-container {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--surface-color, #ffffff);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  padding: 0 12px;
  transition: all 0.2s ease;
}

.search-input-wrapper:focus-within {
  border-color: var(--primary-color, #007bff);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.search-icon {
  display: flex;
  align-items: center;
  color: var(--text-secondary, #666);
  margin-right: 8px;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 0;
  font-size: 14px;
  background: transparent;
  color: var(--text-primary, #333);
}

.search-input::placeholder {
  color: var(--text-secondary, #999);
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.loading-indicator {
  display: flex;
  align-items: center;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color, #e0e0e0);
  border-top: 2px solid var(--primary-color, #007bff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-secondary, #666);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-button:hover {
  background: var(--surface-hover, #f5f5f5);
  color: var(--text-primary, #333);
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--surface-color, #ffffff);
  border: 1px solid var(--border-color, #e0e0e0);
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.suggestions-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background: var(--surface-hover, #f5f5f5);
}

.suggestion-icon {
  display: flex;
  align-items: center;
  color: var(--text-secondary, #666);
  margin-right: 8px;
}

.suggestion-text {
  font-size: 14px;
  color: var(--text-primary, #333);
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-bar-container {
    max-width: none;
  }
  
  .search-input-wrapper {
    padding: 0 8px;
  }
  
  .search-input {
    padding: 10px 0;
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  .suggestions-dropdown {
    max-height: 150px;
  }
  
  .suggestion-item {
    padding: 10px 12px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .search-input-wrapper {
    background: var(--surface-dark, #2d2d2d);
    border-color: var(--border-dark, #404040);
  }
  
  .search-input {
    color: var(--text-dark, #ffffff);
  }
  
  .search-input::placeholder {
    color: var(--text-secondary-dark, #aaa);
  }
  
  .suggestions-dropdown {
    background: var(--surface-dark, #2d2d2d);
    border-color: var(--border-dark, #404040);
  }
  
  .suggestion-item:hover {
    background: var(--surface-hover-dark, #3d3d3d);
  }
  
  .suggestion-text {
    color: var(--text-dark, #ffffff);
  }
}
