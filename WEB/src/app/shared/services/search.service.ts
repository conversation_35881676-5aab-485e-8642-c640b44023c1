import { Injectable } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable, BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, startWith } from 'rxjs/operators';
import { QueryParams } from '../../models/common.models';

export interface SearchConfig {
  placeholder: string;
  debounceTime: number;
  minLength: number;
  suggestions?: boolean;
  filters?: FilterConfig[];
}

export interface FilterConfig {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date-range' | 'number-range' | 'boolean';
  options?: SelectOption[];
  multiple?: boolean;
}

export interface SelectOption {
  value: any;
  label: string;
}

export interface SavedSearch {
  id: string;
  name: string;
  query: string;
  filters: any;
  createdAt: Date;
}

export interface DateRange {
  start: Date | null;
  end: Date | null;
}

export interface NumberRange {
  min: number | null;
  max: number | null;
}

@Injectable({
  providedIn: 'root'
})
export class SearchService {
  private readonly SAVED_SEARCHES_KEY = 'tele-shop-saved-searches';
  private readonly SEARCH_HISTORY_KEY = 'tele-shop-search-history';
  private readonly MAX_HISTORY_ITEMS = 10;

  private searchHistorySubject = new BehaviorSubject<string[]>([]);
  public searchHistory$ = this.searchHistorySubject.asObservable();

  constructor() {
    this.loadSearchHistory();
  }

  createSearchObservable(searchControl: FormControl, config: SearchConfig): Observable<string> {
    return searchControl.valueChanges.pipe(
      startWith(searchControl.value || ''),
      debounceTime(config.debounceTime),
      distinctUntilChanged(),
      filter(value => !value || value.length >= config.minLength),
      map(value => (value || '').trim())
    );
  }

  buildFilterQuery(filters: any): QueryParams {
    const queryParams: QueryParams = {
      filters: {}
    };

    Object.keys(filters).forEach(key => {
      const value = filters[key];
      
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value) && value.length > 0) {
          queryParams.filters![key] = value;
        } else if (!Array.isArray(value)) {
          queryParams.filters![key] = value;
        }
      }
    });

    return queryParams;
  }

  saveSearch(name: string, query: string, filters: any): void {
    const savedSearches = this.getSavedSearches();
    const newSearch: SavedSearch = {
      id: this.generateId(),
      name,
      query,
      filters,
      createdAt: new Date()
    };

    // Remove existing search with same name
    const filteredSearches = savedSearches.filter(s => s.name !== name);
    filteredSearches.unshift(newSearch);

    // Keep only last 20 saved searches
    const limitedSearches = filteredSearches.slice(0, 20);
    
    localStorage.setItem(this.SAVED_SEARCHES_KEY, JSON.stringify(limitedSearches));
  }

  getSavedSearches(): SavedSearch[] {
    try {
      const saved = localStorage.getItem(this.SAVED_SEARCHES_KEY);
      return saved ? JSON.parse(saved) : [];
    } catch {
      return [];
    }
  }

  deleteSavedSearch(id: string): void {
    const savedSearches = this.getSavedSearches();
    const filtered = savedSearches.filter(s => s.id !== id);
    localStorage.setItem(this.SAVED_SEARCHES_KEY, JSON.stringify(filtered));
  }

  addToSearchHistory(query: string): void {
    if (!query.trim()) return;

    const history = this.getSearchHistory();
    const filtered = history.filter(item => item !== query);
    filtered.unshift(query);

    const limitedHistory = filtered.slice(0, this.MAX_HISTORY_ITEMS);
    localStorage.setItem(this.SEARCH_HISTORY_KEY, JSON.stringify(limitedHistory));
    this.searchHistorySubject.next(limitedHistory);
  }

  getSearchHistory(): string[] {
    try {
      const history = localStorage.getItem(this.SEARCH_HISTORY_KEY);
      return history ? JSON.parse(history) : [];
    } catch {
      return [];
    }
  }

  clearSearchHistory(): void {
    localStorage.removeItem(this.SEARCH_HISTORY_KEY);
    this.searchHistorySubject.next([]);
  }

  getSuggestions(query: string, suggestions: string[]): string[] {
    if (!query || query.length < 2) {
      return [];
    }

    const lowerQuery = query.toLowerCase();
    return suggestions
      .filter(suggestion => 
        suggestion.toLowerCase().includes(lowerQuery)
      )
      .slice(0, 10);
  }

  buildAdvancedFilters(filterConfigs: FilterConfig[], filterValues: any): any {
    const filters: any = {};

    filterConfigs.forEach(config => {
      const value = filterValues[config.key];
      
      if (value !== null && value !== undefined && value !== '') {
        switch (config.type) {
          case 'date-range':
            if (value.start || value.end) {
              filters[config.key] = value;
            }
            break;
          case 'number-range':
            if (value.min !== null || value.max !== null) {
              filters[config.key] = value;
            }
            break;
          case 'select':
            if (config.multiple) {
              if (Array.isArray(value) && value.length > 0) {
                filters[config.key] = value;
              }
            } else {
              filters[config.key] = value;
            }
            break;
          case 'boolean':
            filters[config.key] = value;
            break;
          default:
            if (value.trim()) {
              filters[config.key] = value.trim();
            }
        }
      }
    });

    return filters;
  }

  exportSearchResults(data: any[], filename: string, format: 'csv' | 'json' = 'csv'): void {
    let content: string;
    let mimeType: string;
    let fileExtension: string;

    if (format === 'csv') {
      content = this.convertToCSV(data);
      mimeType = 'text/csv';
      fileExtension = 'csv';
    } else {
      content = JSON.stringify(data, null, 2);
      mimeType = 'application/json';
      fileExtension = 'json';
    }

    const blob = new Blob([content], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename}.${fileExtension}`;
    a.click();
    window.URL.revokeObjectURL(url);
  }

  private convertToCSV(data: any[]): string {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    
    const csvRows = data.map(row => 
      headers.map(header => {
        const value = row[header];
        // Escape quotes and wrap in quotes if contains comma or quote
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    );

    return [csvHeaders, ...csvRows].join('\n');
  }

  private loadSearchHistory(): void {
    const history = this.getSearchHistory();
    this.searchHistorySubject.next(history);
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Utility methods for filter validation
  isValidDateRange(range: DateRange): boolean {
    if (!range.start && !range.end) return true;
    if (range.start && range.end) {
      return range.start <= range.end;
    }
    return true;
  }

  isValidNumberRange(range: NumberRange): boolean {
    if (range.min === null && range.max === null) return true;
    if (range.min !== null && range.max !== null) {
      return range.min <= range.max;
    }
    return true;
  }

  formatFilterDisplay(config: FilterConfig, value: any): string {
    switch (config.type) {
      case 'date-range':
        if (value.start && value.end) {
          return `${value.start.toLocaleDateString()} - ${value.end.toLocaleDateString()}`;
        } else if (value.start) {
          return `From ${value.start.toLocaleDateString()}`;
        } else if (value.end) {
          return `Until ${value.end.toLocaleDateString()}`;
        }
        return '';
      case 'number-range':
        if (value.min !== null && value.max !== null) {
          return `${value.min} - ${value.max}`;
        } else if (value.min !== null) {
          return `≥ ${value.min}`;
        } else if (value.max !== null) {
          return `≤ ${value.max}`;
        }
        return '';
      case 'select':
        if (config.multiple && Array.isArray(value)) {
          return value.join(', ');
        }
        return value.toString();
      case 'boolean':
        return value ? 'Yes' : 'No';
      default:
        return value.toString();
    }
  }
}
