import { Injectable } from '@angular/core';
import { FormGroup, FormControl, Validators, ValidatorFn, AbstractControl } from '@angular/forms';

export interface SelectOption {
  value: any;
  label: string;
  disabled?: boolean;
}

export interface FormFieldConfig {
  key: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'multiselect' | 'textarea' | 'date' | 'file' | 'checkbox';
  label: string;
  placeholder?: string;
  required?: boolean;
  validators?: ValidatorFn[];
  options?: SelectOption[];
  multiple?: boolean;
  accept?: string; // for file inputs
  rows?: number; // for textarea
  min?: number; // for number inputs
  max?: number; // for number inputs
  step?: number; // for number inputs
  defaultValue?: any;
  disabled?: boolean;
  hint?: string;
  errorMessages?: Record<string, string>;
}

@Injectable({
  providedIn: 'root'
})
export class FormBuilderService {

  createForm(config: FormFieldConfig[]): FormGroup {
    const group: Record<string, FormControl> = {};

    config.forEach(field => {
      const validators = this.buildValidators(field);
      const defaultValue = this.getDefaultValue(field);
      
      group[field.key] = new FormControl({
        value: defaultValue,
        disabled: field.disabled || false
      }, validators);
    });

    return new FormGroup(group);
  }

  addValidation(control: AbstractControl, validators: ValidatorFn[]): void {
    const existingValidators = control.validator ? [control.validator] : [];
    control.setValidators([...existingValidators, ...validators]);
    control.updateValueAndValidity();
  }

  getErrorMessage(control: AbstractControl, fieldName: string, customMessages?: Record<string, string>): string {
    if (!control.errors) {
      return '';
    }

    const errors = control.errors;
    const defaultMessages = this.getDefaultErrorMessages(fieldName);
    const messages = { ...defaultMessages, ...customMessages };

    // Return the first error message found
    for (const errorKey in errors) {
      if (messages[errorKey]) {
        const errorValue = errors[errorKey];
        return this.interpolateErrorMessage(messages[errorKey], errorValue);
      }
    }

    return `${fieldName} is invalid`;
  }

  updateFormConfig(form: FormGroup, config: FormFieldConfig[]): void {
    config.forEach(field => {
      const control = form.get(field.key);
      if (control) {
        // Update validators
        const validators = this.buildValidators(field);
        control.setValidators(validators);
        
        // Update disabled state
        if (field.disabled) {
          control.disable();
        } else {
          control.enable();
        }
        
        control.updateValueAndValidity();
      }
    });
  }

  resetForm(form: FormGroup, config: FormFieldConfig[]): void {
    config.forEach(field => {
      const control = form.get(field.key);
      if (control) {
        const defaultValue = this.getDefaultValue(field);
        control.setValue(defaultValue);
        control.markAsUntouched();
        control.markAsPristine();
      }
    });
  }

  private buildValidators(field: FormFieldConfig): ValidatorFn[] {
    const validators: ValidatorFn[] = [];

    if (field.required) {
      validators.push(Validators.required);
    }

    if (field.type === 'email') {
      validators.push(Validators.email);
    }

    if (field.type === 'number') {
      if (field.min !== undefined) {
        validators.push(Validators.min(field.min));
      }
      if (field.max !== undefined) {
        validators.push(Validators.max(field.max));
      }
    }

    if (field.validators) {
      validators.push(...field.validators);
    }

    return validators;
  }

  private getDefaultValue(field: FormFieldConfig): any {
    if (field.defaultValue !== undefined) {
      return field.defaultValue;
    }

    switch (field.type) {
      case 'checkbox':
        return false;
      case 'multiselect':
        return [];
      case 'number':
        return null;
      default:
        return '';
    }
  }

  private getDefaultErrorMessages(fieldName: string): Record<string, string> {
    return {
      required: `${fieldName} is required`,
      email: `${fieldName} must be a valid email address`,
      min: `${fieldName} must be at least {min}`,
      max: `${fieldName} must not exceed {max}`,
      minlength: `${fieldName} must be at least {requiredLength} characters`,
      maxlength: `${fieldName} must not exceed {requiredLength} characters`,
      pattern: `${fieldName} format is invalid`,
      unique: `${fieldName} already exists`,
      custom: `${fieldName} is invalid`
    };
  }

  private interpolateErrorMessage(message: string, errorValue: any): string {
    if (typeof errorValue === 'object' && errorValue !== null) {
      let result = message;
      Object.keys(errorValue).forEach(key => {
        result = result.replace(`{${key}}`, errorValue[key]);
      });
      return result;
    }
    return message;
  }

  // Custom validators
  static uniqueValidator(existingValues: string[]): ValidatorFn {
    return (control: AbstractControl): {[key: string]: any} | null => {
      if (!control.value) {
        return null;
      }
      
      const isUnique = !existingValues.includes(control.value.toLowerCase());
      return isUnique ? null : { unique: { value: control.value } };
    };
  }

  static matchValidator(matchControlName: string): ValidatorFn {
    return (control: AbstractControl): {[key: string]: any} | null => {
      if (!control.parent) {
        return null;
      }
      
      const matchControl = control.parent.get(matchControlName);
      if (!matchControl) {
        return null;
      }
      
      const match = control.value === matchControl.value;
      return match ? null : { match: { value: control.value } };
    };
  }

  static urlValidator(): ValidatorFn {
    return (control: AbstractControl): {[key: string]: any} | null => {
      if (!control.value) {
        return null;
      }
      
      try {
        new URL(control.value);
        return null;
      } catch {
        return { url: { value: control.value } };
      }
    };
  }

  static phoneValidator(): ValidatorFn {
    return (control: AbstractControl): {[key: string]: any} | null => {
      if (!control.value) {
        return null;
      }
      
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      const valid = phoneRegex.test(control.value.replace(/\s/g, ''));
      return valid ? null : { phone: { value: control.value } };
    };
  }
}
