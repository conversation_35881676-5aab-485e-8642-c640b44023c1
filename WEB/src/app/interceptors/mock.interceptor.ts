import { HttpInterceptorFn, HttpResponse } from '@angular/common/http';
import { of } from 'rxjs';
import { delay } from 'rxjs/operators';

export const mockInterceptor: HttpInterceptorFn = (req, next) => {
  // Skip mock for auth endpoints and requests with skip header
  if (req.headers.has('X-Skip-Mock')) {
    return next(req);
  }

  // Mock API responses for development
  if (req.url.includes('/api/')) {
    console.log('Mocking API request:', req.method, req.url);
    
    // Mock different endpoints
    if (req.url.includes('/api/products')) {
      return of(new HttpResponse({
        status: 200,
        body: []
      })).pipe(delay(300));
    }
    
    if (req.url.includes('/api/categories')) {
      return of(new HttpResponse({
        status: 200,
        body: []
      })).pipe(delay(300));
    }
    
    if (req.url.includes('/api/sales')) {
      return of(new HttpResponse({
        status: 200,
        body: []
      })).pipe(delay(300));
    }
    
    if (req.url.includes('/api/inventory')) {
      return of(new HttpResponse({
        status: 200,
        body: {
          id: 'mock-inventory-id',
          items: [],
          recordDate: new Date().toISOString(),
          totalValue: { amount: 0, currency: 'USD' }
        }
      })).pipe(delay(300));
    }
    
    if (req.url.includes('/api/tags')) {
      return of(new HttpResponse({
        status: 200,
        body: []
      })).pipe(delay(300));
    }
    
    // Default mock response
    return of(new HttpResponse({
      status: 200,
      body: { message: 'Mock response' }
    })).pipe(delay(300));
  }

  // Let non-API requests pass through
  return next(req);
};
