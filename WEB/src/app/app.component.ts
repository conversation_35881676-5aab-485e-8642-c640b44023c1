import { Component, OnInit } from '@angular/core';

import { ModernLayoutComponent } from './components/layout/modern-layout/modern-layout.component';
import { ModernThemeService } from './services/modern-theme.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [ModernLayoutComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  title = 'Tele-Shop';

  constructor(private themeService: ModernThemeService) {}

  ngOnInit(): void {
    // Preload theme to prevent flash of unstyled content
    ModernThemeService.preloadTheme();
  }
}
