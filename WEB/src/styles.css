/* ===================================
   GLOBAL STYLES - TELE-SHOP MODERN
   =================================== */

/* Import Modern Design System */
@import './styles/modern-design-system.css';
@import './styles/modern-components.css';
@import './styles/modern-utilities.css';

/* ===== MODERN ANIMATIONS ===== */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ===== MODERN FOCUS STYLES ===== */
:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
  border-radius: var(--radius-md);
}

/* ===== MODERN SELECTION ===== */
::selection {
  background-color: var(--primary-200);
  color: var(--primary-900);
}

[data-theme="dark"] ::selection {
  background-color: var(--primary-800);
  color: var(--primary-100);
}

/* ===== MODERN SCROLLBAR ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-400);
  border-radius: var(--radius-full);
  transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--neutral-600);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}

/* ===== MODERN PAGE TRANSITIONS ===== */
.page-enter {
  animation: fadeIn 0.3s ease-out;
}

.page-leave {
  animation: fadeIn 0.3s ease-out reverse;
}

/* ===== MODERN LAYOUT COMPONENTS ===== */
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  transition: background-color var(--transition-base);
}

.app-header {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  background-color: var(--bg-surface);
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.app-content {
  flex: 1;
  padding: var(--space-6) 0;
}

.app-footer {
  background-color: var(--bg-surface);
  border-top: 1px solid var(--border-primary);
  padding: var(--space-6) 0;
  margin-top: auto;
}

/* ===== MODERN SIDEBAR ===== */
.sidebar {
  width: 280px;
  background-color: var(--bg-surface);
  border-right: 1px solid var(--border-primary);
  transition: all var(--transition-base);
  overflow-y: auto;
}

.sidebar-collapsed {
  width: 64px;
}

.sidebar-mobile {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: var(--z-modal);
  transform: translateX(-100%);
  transition: transform var(--transition-base);
}

.sidebar-mobile.open {
  transform: translateX(0);
}

.sidebar-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
}

.sidebar-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* ===== MODERN NAVIGATION ===== */
.nav-menu {
  padding: var(--space-4);
}

.nav-section {
  margin-bottom: var(--space-6);
}

.nav-section-title {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-3);
  padding: 0 var(--space-4);
}

.nav-item {
  margin-bottom: var(--space-1);
}

/* ===== MODERN BREADCRUMBS ===== */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.breadcrumb-separator {
  color: var(--text-muted);
}

.breadcrumb-link {
  color: var(--interactive-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.breadcrumb-link:hover {
  color: var(--interactive-primary-hover);
  text-decoration: underline;
}

/* ===== MODERN PAGE HEADERS ===== */
.page-header {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.page-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.page-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}

/* ===== MODERN STATS CARDS ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  background: linear-gradient(135deg, var(--bg-surface) 0%, var(--bg-surface-hover) 100%);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-base);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-2xl);
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  color: var(--primary-700);
}

.stat-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.stat-change {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
}

.stat-change.positive {
  background-color: var(--success-50);
  color: var(--success-700);
}

.stat-change.negative {
  background-color: var(--error-50);
  color: var(--error-700);
}

/* ===== MODERN RESPONSIVE DESIGN ===== */
@media (max-width: 1023px) {
  .sidebar {
    display: none;
  }
  
  .sidebar-mobile {
    display: block;
  }
  
  .app-content {
    padding: var(--space-4) 0;
  }
  
  .page-title {
    font-size: var(--text-3xl);
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: var(--text-2xl);
  }
  
  .page-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: var(--space-4);
  }
}

/* ===== MODERN PRINT STYLES ===== */
@media print {
  .sidebar,
  .app-header,
  .app-footer,
  .page-actions,
  .btn {
    display: none !important;
  }
  
  .app-content {
    padding: 0 !important;
  }
  
  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
  
  * {
    background: transparent !important;
    color: black !important;
  }
}
